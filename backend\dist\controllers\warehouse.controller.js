"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseController = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class WarehouseController {
    constructor() {
        // Drizzle DB wird direkt importiert
    }
    /**
     * Holt die Lagerauslastungsdaten für das Lager 200
     */
    async getLagerauslastung200(req, res) {
        try {
            const { startDate, endDate } = req.query;
            // Baue die WHERE-Bedingungen für Drizzle auf
            const conditions = [];
            if (startDate) {
                conditions.push((0, drizzle_orm_1.gte)(schema_1.auslastung200.aufnahmeDatum, startDate));
            }
            if (endDate) {
                conditions.push((0, drizzle_orm_1.lte)(schema_1.auslastung200.aufnahmeDatum, endDate));
            }
            // Hole die Rohdaten mit Drizzle
            let rawData;
            if (conditions.length > 0) {
                rawData = await db_1.db.select({
                    aufnahmeDatum: schema_1.auslastung200.aufnahmeDatum,
                    auslastungA: schema_1.auslastung200.auslastungA,
                    auslastungB: schema_1.auslastung200.auslastungB,
                    auslastungC: schema_1.auslastung200.auslastungC,
                }).from(schema_1.auslastung200)
                    .where((0, drizzle_orm_1.and)(...conditions))
                    .orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung200.aufnahmeDatum));
            }
            else {
                rawData = await db_1.db.select({
                    aufnahmeDatum: schema_1.auslastung200.aufnahmeDatum,
                    auslastungA: schema_1.auslastung200.auslastungA,
                    auslastungB: schema_1.auslastung200.auslastungB,
                    auslastungC: schema_1.auslastung200.auslastungC,
                }).from(schema_1.auslastung200)
                    .orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung200.aufnahmeDatum));
            }
            // Gruppiere nach Datum und berechne Durchschnitte
            const groupedByDate = rawData.reduce((acc, item) => {
                if (!item.aufnahmeDatum)
                    return acc;
                // Konvertiere das Datum zu 'YYYY-MM-DD' Format
                const date = new Date(item.aufnahmeDatum);
                const dateStr = date.toISOString().split('T')[0];
                if (!acc[dateStr]) {
                    acc[dateStr] = [0, 0, 0, 0]; // [sumA, sumB, sumC, count]
                }
                // Konvertiere alle Werte zu Zahlen, um Typenkonflikte zu vermeiden
                const a = Number(item.auslastungA) || 0;
                const b = Number(item.auslastungB) || 0;
                const c = Number(item.auslastungC) || 0;
                acc[dateStr][0] += a;
                acc[dateStr][1] += b;
                acc[dateStr][2] += c;
                acc[dateStr][3] += 1;
                return acc;
            }, {});
            // Transformiere in das gewünschte Format
            const result = Object.entries(groupedByDate).map(([date, values]) => {
                const [sumA, sumB, sumC, count] = values;
                const avgA = count > 0 ? sumA / count : 0;
                const avgB = count > 0 ? sumB / count : 0;
                const avgC = count > 0 ? sumC / count : 0;
                return {
                    date,
                    auslastungA: parseFloat(avgA.toFixed(2)),
                    auslastungB: parseFloat(avgB.toFixed(2)),
                    auslastungC: parseFloat(avgC.toFixed(2)),
                    gesamt: parseFloat(((avgA + avgB + avgC) / 3).toFixed(2))
                };
            });
            res.json(result);
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lagerauslastung 200:', error);
            res.status(500).json({ error: 'Interner Serverfehler' });
        }
    }
    /**
     * Holt die Lagerauslastungsdaten für das Lager 240
     */
    async getLagerauslastung240(req, res) {
        try {
            const { startDate, endDate } = req.query;
            // Baue die WHERE-Bedingungen für Drizzle auf
            const conditions = [];
            if (startDate) {
                conditions.push((0, drizzle_orm_1.gte)(schema_1.auslastung240.aufnahmeDatum, String(startDate)));
            }
            if (endDate) {
                conditions.push((0, drizzle_orm_1.lte)(schema_1.auslastung240.aufnahmeDatum, String(endDate)));
            }
            // Hole die Rohdaten mit Drizzle aus auslastung240 Tabelle
            let rawData;
            if (conditions.length > 0) {
                rawData = await db_1.db.select({
                    aufnahmeDatum: schema_1.auslastung240.aufnahmeDatum,
                    auslastungA: schema_1.auslastung240.auslastungA,
                    auslastungB: schema_1.auslastung240.auslastungB,
                    auslastungC: schema_1.auslastung240.auslastungC,
                }).from(schema_1.auslastung240)
                    .where((0, drizzle_orm_1.and)(...conditions))
                    .orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung240.aufnahmeDatum));
            }
            else {
                rawData = await db_1.db.select({
                    aufnahmeDatum: schema_1.auslastung240.aufnahmeDatum,
                    auslastungA: schema_1.auslastung240.auslastungA,
                    auslastungB: schema_1.auslastung240.auslastungB,
                    auslastungC: schema_1.auslastung240.auslastungC,
                }).from(schema_1.auslastung240)
                    .orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung240.aufnahmeDatum));
            }
            // Gruppiere nach Datum und berechne Durchschnitte
            const groupedByDate = rawData.reduce((acc, item) => {
                if (!item.aufnahmeDatum)
                    return acc;
                // Konvertiere das Datum zu 'YYYY-MM-DD' Format
                const date = new Date(item.aufnahmeDatum);
                const dateStr = date.toISOString().split('T')[0];
                if (!acc[dateStr]) {
                    acc[dateStr] = [0, 0, 0, 0]; // [sumA, sumB, sumC, count]
                }
                // Konvertiere alle Werte zu Zahlen und multipliziere mit 0.8 für Lager 240
                const a = (Number(item.auslastungA) || 0) * 0.8;
                const b = (Number(item.auslastungB) || 0) * 0.8;
                const c = (Number(item.auslastungC) || 0) * 0.8;
                acc[dateStr][0] += a;
                acc[dateStr][1] += b;
                acc[dateStr][2] += c;
                acc[dateStr][3] += 1;
                return acc;
            }, {});
            // Transformiere in das gewünschte Format
            const result = Object.entries(groupedByDate).map(([date, values]) => {
                const [sumA, sumB, sumC, count] = values;
                const avgA = count > 0 ? sumA / count : 0;
                const avgB = count > 0 ? sumB / count : 0;
                const avgC = count > 0 ? sumC / count : 0;
                return {
                    date,
                    auslastungA: parseFloat(avgA.toFixed(2)),
                    auslastungB: parseFloat(avgB.toFixed(2)),
                    auslastungC: parseFloat(avgC.toFixed(2)),
                    gesamt: parseFloat(((avgA + avgB + avgC) / 3).toFixed(2))
                };
            });
            res.json(result);
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lagerauslastung 240:', error);
            res.status(500).json({ error: 'Interner Serverfehler' });
        }
    }
}
exports.WarehouseController = WarehouseController;
