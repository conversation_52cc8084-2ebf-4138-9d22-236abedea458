import { db } from "../db";
import {
  stoerungen,
  stoerungsComments,
  stoerungsAttachment,
  systemStatus,
  systemStatusMessage,
} from "../db/schema";
import {
  eq,
  and,
  or,
  desc,
  asc,
  count,
  avg,
  inArray,
  gte,
  isNotNull,
} from "drizzle-orm";
import {
  StoerungCreateData,
  StoerungUpdateData,
  StoerungWithComments,
  StoerungCommentCreateData,
  StoerungsStats,
  StoerungAttachment,
  StoerungAttachmentCreateData,
  SystemStatusData,
  SystemStatusUpdateData,
} from "../types/stoerungen.types";

// Simple in-memory cache for störungen
const simpleCache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = simpleCache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  simpleCache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttlMs: number = 300000) => {
  simpleCache.set(key, { data, expires: Date.now() + ttlMs });
};

const clearCachePattern = (pattern: string) => {
  for (const key of simpleCache.keys()) {
    if (key.includes(pattern)) {
      simpleCache.delete(key);
    }
  }
};

export class StoerungenRepository {
  private static instance: StoerungenRepository;
  private db = db;

  constructor() {}

  static getInstance(database?: any): StoerungenRepository {
    if (!StoerungenRepository.instance) {
      StoerungenRepository.instance = new StoerungenRepository();
    }
    return StoerungenRepository.instance;
  }

  async createStoerung(data: StoerungCreateData) {
    const tagsJson = data.tags ? JSON.stringify(data.tags) : null;
    const now = new Date().toISOString();

    const result = await this.db
      .insert(stoerungen)
      .values({
        ...data,
        tags: tagsJson,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    const stoerung = result[0];

    // Fetch comments separately
    const comments = await this.db
      .select()
      .from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    clearCachePattern("stoerungen");
    return this.formatStoerung({ ...stoerung, comments });
  }

  async getStoerungen(options?: {
    status?: string;
    severity?: string;
    category?: string;
    affected_system?: string;
    limit?: number;
    offset?: number;
  }) {
    const cacheKey = `stoerungen:list:${JSON.stringify(options || {})}`;
    const cached = getCached<StoerungWithComments[]>(cacheKey);
    if (cached) return cached;

    const conditions: any[] = [];
    if (options?.status) conditions.push(eq(stoerungen.status, options.status));
    if (options?.severity)
      conditions.push(eq(stoerungen.severity, options.severity));
    if (options?.category)
      conditions.push(eq(stoerungen.category, options.category));
    if (options?.affected_system)
      conditions.push(eq(stoerungen.affectedSystem, options.affected_system));

    // Build query step by step to avoid Drizzle ORM type issues
    const baseQuery = this.db.select().from(stoerungen);

    let finalQuery;
    if (conditions.length > 0) {
      finalQuery = baseQuery.where(and(...conditions));
    } else {
      finalQuery = baseQuery;
    }

    // Apply ordering, limit, and offset
    finalQuery = finalQuery.orderBy(desc(stoerungen.createdAt));

    if (options?.limit) {
      finalQuery = finalQuery.limit(options.limit);
    }

    if (options?.offset) {
      finalQuery = finalQuery.offset(options.offset);
    }

    const stoerungsResult = await finalQuery;

    // Fetch comments for each störung
    const stoerungsWithComments = await Promise.all(
      stoerungsResult.map(async (stoerung) => {
        const comments = await this.db
          .select()
          .from(stoerungsComments)
          .where(eq(stoerungsComments.stoerungId, stoerung.id))
          .orderBy(desc(stoerungsComments.createdAt));
        return { ...stoerung, comments };
      }),
    );

    const formatted = stoerungsWithComments.map(this.formatStoerung);
    setCache(cacheKey, formatted, 300000); // 5 minutes TTL
    return formatted;
  }

  async getStoerungById(id: number): Promise<StoerungWithComments | null> {
    const cacheKey = `stoerungen:detail:${id}`;
    const cached = getCached<StoerungWithComments>(cacheKey);
    if (cached) return cached;

    const result = await this.db
      .select()
      .from(stoerungen)
      .where(eq(stoerungen.id, id))
      .limit(1);

    const stoerung = result[0];
    if (!stoerung) return null;

    // Fetch comments separately
    const comments = await this.db
      .select()
      .from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    const formatted = this.formatStoerung({ ...stoerung, comments });
    setCache(cacheKey, formatted, 300000);
    return formatted;
  }

  async updateStoerung(id: number, data: StoerungUpdateData) {
    const updateData: any = { ...data };

    if (data.tags) {
      updateData.tags = JSON.stringify(data.tags);
    }

    if (data.status === "RESOLVED" && !data.resolvedAt) {
      updateData.resolvedAt = new Date().toISOString();
    }

    updateData.updatedAt = new Date().toISOString();

    const result = await this.db
      .update(stoerungen)
      .set(updateData)
      .where(eq(stoerungen.id, id))
      .returning();

    const stoerung = result[0];

    // Fetch comments separately
    const comments = await this.db
      .select()
      .from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    clearCachePattern("stoerungen");
    return this.formatStoerung({ ...stoerung, comments });
  }

  async deleteStoerung(id: number): Promise<boolean> {
    try {
      await this.db.delete(stoerungen).where(eq(stoerungen.id, id));
      clearCachePattern("stoerungen");
      return true;
    } catch {
      return false;
    }
  }

  async addComment(data: StoerungCommentCreateData) {
    // Add createdAt to the comment data as required by the schema
    const commentWithTimestamp = {
      stoerungId: data.stoerungId,
      userId: data.userId,
      comment: data.comment,
      createdAt: new Date().toISOString(),
      caretakerId: data.caretakerId,
    };

    const result = await this.db
      .insert(stoerungsComments)
      .values(commentWithTimestamp)
      .returning();

    clearCachePattern("stoerungen");
    return result[0];
  }

  async getStoerungsStats(): Promise<StoerungsStats> {
    const cacheKey = "stoerungen:stats";
    const cached = getCached<StoerungsStats>(cacheKey);
    if (cached) return cached;

    // Execute queries separately to avoid Drizzle ORM type issues
    const totalResult = await this.db
      .select({ count: count() })
      .from(stoerungen);
    const activeResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(inArray(stoerungen.status, ["NEW", "IN_PROGRESS"]));
    const resolvedResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(eq(stoerungen.status, "RESOLVED"));
    const avgMttrResult = await this.db
      .select({ avg: avg(stoerungen.mttrMinutes) })
      .from(stoerungen)
      .where(
        and(
          eq(stoerungen.status, "RESOLVED"),
          isNotNull(stoerungen.mttrMinutes),
        ),
      );
    // Calculate 24 hours ago as ISO string for proper date comparison
    const twentyFourHoursAgo = new Date(
      Date.now() - 24 * 60 * 60 * 1000,
    ).toISOString();
    const recentResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(
        and(
          eq(stoerungen.status, "RESOLVED"),
          gte(stoerungen.resolvedAt, twentyFourHoursAgo),
        ),
      );

    // Get severity counts separately to avoid type issues
    const criticalResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(eq(stoerungen.severity, "CRITICAL"));
    const highResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(eq(stoerungen.severity, "HIGH"));
    const mediumResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(eq(stoerungen.severity, "MEDIUM"));
    const lowResult = await this.db
      .select({ count: count() })
      .from(stoerungen)
      .where(eq(stoerungen.severity, "LOW"));

    const stats: StoerungsStats = {
      total: totalResult[0]?.count || 0,
      active: activeResult[0]?.count || 0,
      resolved: resolvedResult[0]?.count || 0,
      critical: criticalResult[0]?.count || 0,
      high: highResult[0]?.count || 0,
      medium: mediumResult[0]?.count || 0,
      low: lowResult[0]?.count || 0,
      avg_mttr_minutes: Math.round(Number(avgMttrResult[0]?.avg) || 0),
      resolution_rate_24h: recentResult[0]?.count || 0,
    };

    setCache(cacheKey, stats, 900000); // 15 minutes TTL
    return stats;
  }

  async getSystemStatus(): Promise<SystemStatusData[]> {
    const cacheKey = "system:status:all";
    const cached = getCached<SystemStatusData[]>(cacheKey);
    if (cached) return cached;

    const statuses = await this.db
      .select()
      .from(systemStatus)
      .orderBy(asc(systemStatus.systemName));

    // Load status messages for each system status with category-based selection
    const statusesWithMessages = await Promise.all(
      statuses.map(async (status) => {
        // Get detailed status messages for this system
        const statusMessages = await this.db
          .select()
          .from(systemStatusMessage)
          .where(
            and(
              eq(systemStatusMessage.systemStatusId, status.id),
              eq(systemStatusMessage.isActive, true),
            ),
          )
          .orderBy(asc(systemStatusMessage.createdAt))
          .limit(3);

        return {
          id: status.id,
          system_name: status.systemName, // Map camelCase to snake_case for frontend
          status: status.status as "OK" | "WARNING" | "ERROR" | "OFF",
          last_check: status.lastUpdated, // Map camelCase to snake_case for frontend
          metadata: status.metadata || undefined,
          created_at: status.createdAt, // Map camelCase to snake_case for frontend
          updated_at: status.updatedAt, // Map camelCase to snake_case for frontend
          statusMessages: statusMessages.map((msg) => ({
            id: msg.id,
            title: msg.title || "Status Update",
            description: msg.description || "",
            priority: msg.priority || 1,
            category: msg.category || "general",
          })),
        };
      }),
    );

    setCache(cacheKey, statusesWithMessages, 30000); // 30 seconds TTL for live data
    return statusesWithMessages;
  }

  async updateSystemStatus(
    data: SystemStatusUpdateData,
  ): Promise<SystemStatusData> {
    // First try to find existing record
    const existingStatusResult = await this.db
      .select()
      .from(systemStatus)
      .where(eq(systemStatus.systemName, data.system_name)) // Use snake_case from interface
      .limit(1);

    const existingStatus = existingStatusResult[0];
    let status;

    if (existingStatus) {
      // Update existing record
      const updateResult = await this.db
        .update(systemStatus)
        .set({
          status: data.status,
          lastUpdated: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: data.metadata,
        })
        .where(eq(systemStatus.id, existingStatus.id))
        .returning();
      status = updateResult[0];
    } else {
      // Create new record
      const insertResult = await this.db
        .insert(systemStatus)
        .values({
          systemName: data.system_name, // Use snake_case from interface
          status: data.status,
          lastUpdated: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metadata: data.metadata || null,
        })
        .returning();
      status = insertResult[0];
    }

    clearCachePattern("system:status");

    return {
      id: status.id,
      system_name: status.systemName, // Map camelCase to snake_case for frontend
      status: status.status as "OK" | "WARNING" | "ERROR" | "OFF",
      last_check: status.lastUpdated, // Map camelCase to snake_case for frontend
      metadata: status.metadata || undefined,
      created_at: status.createdAt, // Map camelCase to snake_case for frontend
      updated_at: status.updatedAt, // Map camelCase to snake_case for frontend
      statusMessages: [],
    };
  }

  private formatStoerung(stoerung: any): StoerungWithComments {
    let tags: string[] = [];

    try {
      if (stoerung.tags) {
        if (typeof stoerung.tags === "string") {
          // Try to parse as JSON first
          try {
            tags = JSON.parse(stoerung.tags);
          } catch {
            // If JSON parsing fails, split by comma
            tags = stoerung.tags
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean);
          }
        } else if (Array.isArray(stoerung.tags)) {
          tags = stoerung.tags;
        }
      }
    } catch (error) {
      console.error("Error parsing tags for störung:", stoerung.id, error);
      tags = [];
    }

    // Convert string dates to ISO strings as expected by the frontend
    const formatDate = (
      dateStr: string | null | undefined,
      fallback: boolean = false,
    ): string | undefined => {
      if (!dateStr) {
        if (fallback) {
          // For required fields, provide current date as fallback
          return new Date().toISOString();
        }
        return undefined;
      }

      try {
        const date = new Date(dateStr);
        // Check if the date is valid
        if (isNaN(date.getTime())) {
          console.error(
            `Invalid date value for störung ${stoerung.id}:`,
            dateStr,
          );
          if (fallback) {
            return new Date().toISOString();
          }
          return undefined;
        }
        return date.toISOString();
      } catch (error) {
        console.error(
          `Error converting date for störung ${stoerung.id}:`,
          dateStr,
          error,
        );
        if (fallback) {
          return new Date().toISOString();
        }
        return undefined;
      }
    };

    // Format data to match frontend expectations (snake_case properties)
    return {
      id: stoerung.id,
      title: stoerung.title,
      description: stoerung.description,
      severity: stoerung.severity,
      status: stoerung.status,
      category: stoerung.category,
      affected_system: stoerung.affectedSystem, // Convert camelCase to snake_case
      location: stoerung.location,
      reported_by: stoerung.reportedBy, // Convert camelCase to snake_case
      assigned_to: stoerung.assignedTo, // Convert camelCase to snake_case
      created_at: (formatDate(stoerung.createdAt, true) ||
        new Date().toISOString()) as string, // Ensure string
      updated_at: (formatDate(stoerung.updatedAt, true) ||
        new Date().toISOString()) as string, // Ensure string
      resolved_at: formatDate(stoerung.resolvedAt),
      acknowledged_at: formatDate(stoerung.acknowledgedAt),
      acknowledged_by: stoerung.acknowledgedBy,
      escalation_level: stoerung.escalationLevel,
      resolution_steps: stoerung.resolutionSteps,
      root_cause: stoerung.rootCause,
      lessons_learned: stoerung.lessonsLearned,
      mttr_minutes: stoerung.mttrMinutes,
      mtta_minutes: stoerung.mttaMinutes,
      tags,
      // Ensure comments array exists
      comments: stoerung.comments || [],
    };
  }

  /**
   * Determine system category based on system name
   * This matches the categorization logic from SystemStatusHeatmap component
   */
  private determineSystemCategory(systemName: string): string {
    if (systemName.includes("Datenbank")) return "Datenbanken";
    if (systemName.includes("Terminal")) return "Terminals";
    if (systemName.includes("Fördertechnik")) return "Fördertechnik";
    if (systemName.includes("Schrumpfanlage")) return "Anlagen";
    if (systemName.includes("Wlan")) return "Netzwerk";
    if (
      systemName.includes("Automatisches Trommellager") ||
      systemName.includes("Automatisches Ringlager")
    )
      return "Läger";
    if (systemName.includes("Stapler") || systemName.includes("FTS"))
      return "Flurförderzeuge";
    if (systemName.includes("SAP")) return "SAP";
    if (systemName.includes("ITM")) return "ITM";

    // Check for machine identifiers (M1-T-H3, M2-T-H3, etc.)
    const machineIdentifiers = [
      "M1-T-H3",
      "M2-T-H3",
      "M3-R-H3",
      "M4-T-H3",
      "M5-R-H3",
      "M6-T-H1",
      "M7-R-H1",
      "M8-T-H1",
      "M9-R-H1",
      "M10-T-H1",
      "M11-R-H1",
      "M12-T-H1",
      "M13-R-H1",
      "M14-T-H1",
      "M15-R-H1",
      "M16-T-H1",
      "M17-R-H1",
      "M18-T-H1",
      "M19-T-H1",
      "M20-T-H1",
      "M21-R-H1",
      "M22-T-H3",
      "M23-T-H1",
      "M24-T-H3",
      "M25-RR-H1",
      "M26-T-H1",
      "M27-R-H3",
      "M28-T-H1",
    ];

    if (machineIdentifiers.some((id) => systemName.includes(id))) {
      return "Maschinen";
    }

    // Default fallback
    return "System";
  }

  // ===== ATTACHMENT METHODS =====

  /**
   * Get all attachments for a störung
   */
  async getAttachments(stoerungId: number): Promise<StoerungAttachment[]> {
    const cacheKey = `attachments:${stoerungId}`;
    const cached = getCached<StoerungAttachment[]>(cacheKey);
    if (cached) return cached;

    const attachments = await this.db
      .select()
      .from(stoerungsAttachment)
      .where(eq(stoerungsAttachment.stoerungId, stoerungId))
      .orderBy(desc(stoerungsAttachment.createdAt));

    setCache(cacheKey, attachments, 300000); // 5 minutes TTL
    return attachments;
  }

  /**
   * Get a specific attachment by ID
   */
  async getAttachmentById(id: number): Promise<StoerungAttachment | null> {
    const cacheKey = `attachment:${id}`;
    const cached = getCached<StoerungAttachment>(cacheKey);
    if (cached) return cached;

    const attachment = await this.db
      .select()
      .from(stoerungsAttachment)
      .where(eq(stoerungsAttachment.id, id))
      .limit(1);

    const result = attachment[0] || null;
    if (result) {
      setCache(cacheKey, result, 300000); // 5 minutes TTL
    }
    return result;
  }

  /**
   * Create a new attachment
   */
  async createAttachment(
    data: StoerungAttachmentCreateData,
  ): Promise<StoerungAttachment> {
    const attachmentData = {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const result = await this.db
      .insert(stoerungsAttachment)
      .values(attachmentData)
      .returning();

    const created = result[0];

    // Clear cache
    clearCachePattern(`attachments:${data.stoerungId}`);
    clearCachePattern("stoerungen");

    return created;
  }

  /**
   * Delete an attachment
   */
  async deleteAttachment(id: number): Promise<boolean> {
    try {
      // Get the attachment first to clear the right cache
      const attachment = await this.getAttachmentById(id);

      await this.db
        .delete(stoerungsAttachment)
        .where(eq(stoerungsAttachment.id, id));

      // Clear cache
      if (attachment) {
        clearCachePattern(`attachments:${attachment.stoerungId}`);
      }
      clearCachePattern(`attachment:${id}`);
      clearCachePattern("stoerungen");

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Update an attachment
   */
  async updateAttachment(
    id: number,
    data: Partial<StoerungAttachmentCreateData>,
  ): Promise<StoerungAttachment | null> {
    const updateData = {
      ...data,
      updatedAt: new Date().toISOString(),
    };

    const result = await this.db
      .update(stoerungsAttachment)
      .set(updateData)
      .where(eq(stoerungsAttachment.id, id))
      .returning();

    const updated = result[0] || null;

    if (updated) {
      // Clear cache
      clearCachePattern(`attachments:${updated.stoerungId}`);
      clearCachePattern(`attachment:${id}`);
      clearCachePattern("stoerungen");
    }

    return updated;
  }
}
