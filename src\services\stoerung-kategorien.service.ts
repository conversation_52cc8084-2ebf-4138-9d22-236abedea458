import apiService from "./api.service";

export interface StoerungKategorie {
  name: string;
  systeme: Array<{ name: string } | string>;
}

class StoerungKategorienService {
  private baseUrl = "/stoerungen/kategorien";

  async getKategorien(): Promise<StoerungKategorie[]> {
    try {
      // Try to fetch from API first
      console.log(
        "[DEBUG] Versuche Kategorien von API zu laden:",
        this.baseUrl,
      );
      const result = await apiService.get<StoerungKategorie[]>(this.baseUrl);
      console.log("[DEBUG] Kategorien erfolgreich von API geladen:", result);

      // Validate API response structure
      if (!Array.isArray(result)) {
        console.warn(
          "[DEBUG] API-Antwort ist kein Array, verwende Fallback-Daten",
        );
        throw new Error("Invalid API response format");
      }

      return result;
    } catch (error) {
      console.warn("API nicht verfügbar, verwende Fallback-Daten:", error);

      // Return fallback data if API is not available
      return [
        {
          name: "Systeme",
          systeme: [
            { name: "SAP" },
            { name: "ITM" },
            { name: "Wama<PERSON>" },
            { name: "Microsoft Application" },
            { name: "Citrix" },
            { name: "MFR" },
          ],
        },
        {
          name: "Infrastruktur",
          systeme: [{ name: "Netzwerk" }, { name: "WLAN" }],
        },
        {
          name: "Technische Komponenten",
          systeme: [
            { name: "Förderband" },
            { name: "FTS" },
            { name: "RBG" },
            { name: "Greifer" },
            { name: "Schrumpfanlage" },
            { name: "Ablängmaschinen" },
          ],
        },
        {
          name: "Kommissionierung",
          systeme: [
            { name: "ATrL" },
            { name: "ARiL" },
            { name: "SC)" },
          ],
        },
        {
          name: "Hardware",
          systeme: [
            { name: "Mobile Drucker" },
            { name: "Label Drucker" },
            { name: "Lieferschein Drucker" },
            { name: "Terminal" },
            { name: "PC" },
          ],
        },
      ];
    }
  }

  async getKategorieByName(name: string): Promise<StoerungKategorie | null> {
    try {
      const kategorien = await this.getKategorien();
      return kategorien.find((k) => k.name === name) || null;
    } catch (error) {
      console.error("Error fetching kategorie by name:", error);
      return null;
    }
  }

  async getSystemeForKategorie(kategorieName: string): Promise<string[]> {
    try {
      const kategorie = await this.getKategorieByName(kategorieName);
      if (!kategorie) return [];

      // Handle both object and string formats for systeme
      return kategorie.systeme.map((system) =>
        typeof system === "string" ? system : system.name,
      );
    } catch (error) {
      console.error("Error fetching systeme for kategorie:", error);
      return [];
    }
  }
}

const stoerungKategorienService = new StoerungKategorienService();
export default stoerungKategorienService;
