"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.categories = exports.embeddings = exports.chunks = exports.documents = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_orm_1 = require("drizzle-orm");
// RAG Database Schema for Drizzle ORM
// Separate schema for RAG knowledge base
// Documents table - stores document metadata
exports.documents = (0, pg_core_1.pgTable)("documents", {
    id: (0, pg_core_1.text)("id").primaryKey(),
    title: (0, pg_core_1.text)("title").notNull(),
    content: (0, pg_core_1.text)("content").notNull(),
    source: (0, pg_core_1.text)("source").notNull(),
    sourcePath: (0, pg_core_1.text)("source_path"),
    contentType: (0, pg_core_1.text)("content_type").default("text/plain"),
    language: (0, pg_core_1.text)("language").default("de"),
    createdAt: (0, pg_core_1.timestamp)("created_at", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    updatedAt: (0, pg_core_1.timestamp)("updated_at", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
    indexedAt: (0, pg_core_1.timestamp)("indexed_at", { mode: "date" }),
    status: (0, pg_core_1.text)("status").default("pending"),
}, (table) => ({
    sourceIdx: (0, pg_core_1.index)("documents_source_idx").on(table.source),
    statusIdx: (0, pg_core_1.index)("documents_status_idx").on(table.status),
    createdAtIdx: (0, pg_core_1.index)("documents_created_at_idx").on(table.createdAt),
}));
// Chunks table - stores document chunks
exports.chunks = (0, pg_core_1.pgTable)("chunks", {
    id: (0, pg_core_1.text)("id").primaryKey(),
    documentId: (0, pg_core_1.text)("document_id").notNull(),
    content: (0, pg_core_1.text)("content").notNull(),
    chunkIndex: (0, pg_core_1.integer)("chunk_index").notNull(),
    tokenCount: (0, pg_core_1.integer)("token_count"),
    startPosition: (0, pg_core_1.integer)("start_position"),
    endPosition: (0, pg_core_1.integer)("end_position"),
    createdAt: (0, pg_core_1.timestamp)("created_at", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
}, (table) => ({
    documentIdIdx: (0, pg_core_1.index)("chunks_document_id_idx").on(table.documentId),
    chunkIndexIdx: (0, pg_core_1.index)("chunks_chunk_index_idx").on(table.chunkIndex),
}));
// Embeddings table - stores vector embeddings
exports.embeddings = (0, pg_core_1.pgTable)("embeddings", {
    id: (0, pg_core_1.text)("id").primaryKey(),
    chunkId: (0, pg_core_1.text)("chunk_id").notNull(),
    vector: (0, pg_core_1.real)("vector").array().notNull(), // PostgreSQL array of floats
    modelName: (0, pg_core_1.text)("model_name").notNull().default("text-embedding-3-small"),
    dimension: (0, pg_core_1.integer)("dimension").notNull().default(1536),
    createdAt: (0, pg_core_1.timestamp)("created_at", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
}, (table) => ({
    chunkIdIdx: (0, pg_core_1.index)("embeddings_chunk_id_idx").on(table.chunkId),
}));
// Categories table - stores document categories
exports.categories = (0, pg_core_1.pgTable)("categories", {
    id: (0, pg_core_1.text)("id").primaryKey(),
    name: (0, pg_core_1.text)("name").notNull().unique(),
    description: (0, pg_core_1.text)("description"),
    parentId: (0, pg_core_1.text)("parent_id"),
    createdAt: (0, pg_core_1.timestamp)("created_at", { mode: "date" }).default((0, drizzle_orm_1.sql) `CURRENT_TIMESTAMP`),
}, (table) => ({
    nameIdx: (0, pg_core_1.index)("categories_name_idx").on(table.name),
    parentIdIdx: (0, pg_core_1.index)("categories_parent_id_idx").on(table.parentId),
}));
