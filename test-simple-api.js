/**
 * Test-Script für die einfache bereitschafts API
 * Testet die bereitschafts-Funktionalität mit der einfachen API
 */

const API_BASE_URL = 'http://localhost:3002/api';

async function testSimpleBereitschaftsAPI() {
  console.log('🧪 Starte einfache bereitschafts API Tests...\n');

  try {
    // 1. Test Health Check
    console.log('1️⃣ Teste Health Check...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health Check:', healthData);
    console.log('');

    // 2. Test bereitschafts Endpunkte
    console.log('2️⃣ Teste bereitschafts Endpunkte...');

    // Test getAllPersonen
    console.log('📋 Teste /bereitschafts/personen...');
    const personenResponse = await fetch(`${API_BASE_URL}/bereitschafts/personen`);
    const personenData = await personenResponse.json();
    console.log('✅ Personen Response Status:', personenResponse.status);
    console.log('✅ Personen Data:', JSON.stringify(personenData, null, 2));
    console.log('');

    // Test getWochenplan
    console.log('📅 Teste /bereitschafts/wochenplan...');
    const wochenplanResponse = await fetch(`${API_BASE_URL}/bereitschafts/wochenplan`);
    const wochenplanData = await wochenplanResponse.json();
    console.log('✅ Wochenplan Response Status:', wochenplanResponse.status);
    console.log('✅ Wochenplan Data:', JSON.stringify(wochenplanData, null, 2));
    console.log('');

    // Test getAktuelleBereitschaft
    console.log('🎯 Teste /bereitschafts/aktuelle-bereitschaft...');
    const aktuelleResponse = await fetch(`${API_BASE_URL}/bereitschafts/aktuelle-bereitschaft`);
    const aktuelleData = await aktuelleResponse.json();
    console.log('✅ Aktuelle Bereitschaft Response Status:', aktuelleResponse.status);
    console.log('✅ Aktuelle Bereitschaft Data:', JSON.stringify(aktuelleData, null, 2));
    console.log('');

    // Test getKonfiguration
    console.log('⚙️ Teste /bereitschafts/konfiguration...');
    const konfigurationResponse = await fetch(`${API_BASE_URL}/bereitschafts/konfiguration`);
    const konfigurationData = await konfigurationResponse.json();
    console.log('✅ Konfiguration Response Status:', konfigurationResponse.status);
    console.log('✅ Konfiguration Data:', JSON.stringify(konfigurationData, null, 2));
    console.log('');

    console.log('🎉 Alle Tests erfolgreich abgeschlossen!');
    console.log('✅ Die bereitschafts-Funktionalität funktioniert korrekt!');
    console.log('✅ Das Problem liegt am Backend-Server, nicht an der Datenbank oder API-Logik.');

  } catch (error) {
    console.error('❌ Fehler beim Testen der API:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Führe Tests aus
testSimpleBereitschaftsAPI();