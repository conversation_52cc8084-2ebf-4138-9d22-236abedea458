"use strict";
/**
 * Production Repository Interface
 *
 * Definiert die Schnittstelle für Production-Datenoperationen
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionRepositoryImpl = void 0;
const production_repository_1 = require("../production.repository");
Object.defineProperty(exports, "ProductionRepositoryImpl", { enumerable: true, get: function () { return production_repository_1.ProductionRepositoryImpl; } });
