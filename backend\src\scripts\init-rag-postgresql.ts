/**
 * Initialize RAG Database Script for PostgreSQL
 *
 * Creates and initializes the RAG tables in PostgreSQL database
 * with sample knowledge bases and documents
 */

import { createClient } from '@libsql/client';
import VectorDatabaseService from '../services/rag/VectorDatabaseService';
import EmbeddingService from '../services/rag/EmbeddingService';
import path from 'path';
import fs from 'fs';

interface SampleDocument {
  title: string;
  content: string;
  category: string;
  language: string;
}

class PostgreSQLRAGDatabaseInitializer {
  private vectorService: VectorDatabaseService;
  private embeddingService: EmbeddingService;
  private dbClient: any;

  constructor() {
    // Initialize with PostgreSQL connection
    this.dbClient = createClient({
      url: 'postgresql://leitstand_dashboard:dashboard_password@localhost:5434/rag_knowledge'
    });

    this.vectorService = new VectorDatabaseService();
    this.embeddingService = new EmbeddingService({
      provider: 'ollama',
      defaultModel: 'mxbai-embed-large:latest'
    });
  }

  /**
   * Initialize RAG database with sample data
   */
  async initialize(): Promise<void> {
    console.log('🚀 Initializing RAG Database for PostgreSQL...');

    try {
      // Create tables
      await this.createTables();

      // Check embedding service availability
      await this.checkEmbeddingService();

      // Create sample documents
      const sampleDocuments = this.getSampleDocuments();

      for (const doc of sampleDocuments) {
        await this.addSampleDocument(doc);
      }

      console.log('✅ RAG Database initialized successfully!');
      console.log(`📊 Added ${sampleDocuments.length} sample documents`);

      // Show statistics
      const stats = await this.vectorService.getStatistics();
      console.log('📈 Database Statistics:');
      console.log(`   - Documents: ${stats.totalDocuments}`);
      console.log(`   - Chunks: ${stats.totalChunks}`);
      console.log(`   - Embeddings: ${stats.totalEmbeddings}`);
      console.log(`   - Storage: ${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`);

    } catch (error) {
      console.error('❌ Error initializing RAG database:', error);
      throw error;
    }
  }

  /**
   * Create RAG tables in PostgreSQL
   */
  private async createTables(): Promise<void> {
    console.log('📋 Creating RAG tables...');

    const createTablesSQL = `
      -- Documents table
      CREATE TABLE IF NOT EXISTS documents (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          source TEXT NOT NULL,
          source_path TEXT,
          content_type TEXT DEFAULT 'text/plain',
          language TEXT DEFAULT 'de',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          indexed_at TIMESTAMP,
          status TEXT DEFAULT 'pending'
      );

      -- Chunks table
      CREATE TABLE IF NOT EXISTS chunks (
          id TEXT PRIMARY KEY,
          document_id TEXT NOT NULL,
          content TEXT NOT NULL,
          chunk_index INTEGER NOT NULL,
          token_count INTEGER,
          start_position INTEGER,
          end_position INTEGER,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
      );

      -- Embeddings table
      CREATE TABLE IF NOT EXISTS embeddings (
          id TEXT PRIMARY KEY,
          chunk_id TEXT NOT NULL,
          vector FLOAT[] NOT NULL,
          model_name TEXT NOT NULL DEFAULT 'text-embedding-3-small',
          dimension INTEGER NOT NULL DEFAULT 1536,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (chunk_id) REFERENCES chunks(id) ON DELETE CASCADE
      );

      -- Categories table
      CREATE TABLE IF NOT EXISTS categories (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL UNIQUE,
          description TEXT,
          parent_id TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (parent_id) REFERENCES categories(id)
      );

      -- Indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_documents_source ON documents(source);
      CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
      CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);
      CREATE INDEX IF NOT EXISTS idx_embeddings_chunk_id ON embeddings(chunk_id);
    `;

    // Split and execute statements
    const statements = createTablesSQL.split(';').filter(stmt => stmt.trim());
    for (const statement of statements) {
      if (statement.trim()) {
        await this.dbClient.execute(statement.trim());
      }
    }

    console.log('✅ Tables created successfully');
  }

  /**
   * Add a sample document to the database
   */
  private async addSampleDocument(doc: SampleDocument): Promise<void> {
    try {
      // Store document
      const documentId = this.generateId();
      const now = new Date().toISOString();

      await this.dbClient.execute({
        sql: `INSERT INTO documents (id, title, content, source, content_type, language, created_at, updated_at, status)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        args: [documentId, doc.title, doc.content, 'sample', 'text/plain', doc.language, now, now, 'indexed']
      });

      // Chunk the document
      const chunks = this.chunkDocument(doc.content);
      const chunkIds: string[] = [];

      // Store chunks
      for (let i = 0; i < chunks.length; i++) {
        const chunkId = this.generateId();
        const tokenCount = Math.ceil(chunks[i].length / 4); // Rough estimate

        await this.dbClient.execute({
          sql: `INSERT INTO chunks (id, document_id, content, chunk_index, token_count, start_position, end_position, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          args: [chunkId, documentId, chunks[i], i, tokenCount, 0, chunks[i].length, now]
        });

        chunkIds.push(chunkId);
      }

      // Generate and store embeddings
      for (const chunkId of chunkIds) {
        const chunkIndex = chunkIds.indexOf(chunkId);
        const chunk = chunks[chunkIndex];

        try {
          const providerInfo = this.embeddingService.getProviderInfo();
          const embeddingResponse = await this.embeddingService.generateEmbedding({
            text: chunk,
            model: providerInfo.model
          });

          // Convert Float32Array to PostgreSQL array format
          const vectorArray = Array.from(embeddingResponse.embedding);

          await this.dbClient.execute({
            sql: `INSERT INTO embeddings (id, chunk_id, vector, model_name, dimension, created_at)
                  VALUES (?, ?, ?, ?, ?, ?)`,
            args: [this.generateId(), chunkId, vectorArray, embeddingResponse.model, embeddingResponse.embedding.length, now]
          });

        } catch (error) {
          console.warn(`⚠️ Could not generate embedding for chunk ${chunkId}:`, error);
        }
      }

      console.log(`📄 Added document: ${doc.title} (${chunks.length} chunks)`);

    } catch (error) {
      console.error(`❌ Error adding document "${doc.title}":`, error);
    }
  }

  /**
   * Simple document chunking
   */
  private chunkDocument(content: string, maxChunkSize: number = 500): string[] {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence.trim();
      } else {
        currentChunk += (currentChunk.length > 0 ? '. ' : '') + sentence.trim();
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
    }

    return chunks.length > 0 ? chunks : [content]; // Fallback to full content
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Get sample documents for different categories
   */
  private getSampleDocuments(): SampleDocument[] {
    return [
      {
        title: 'Versand KPI Definitionen',
        content: `Servicegrad: Der Servicegrad misst den Anteil der termingerecht ausgelieferten Sendungen. Ein Servicegrad von 95% bedeutet, dass 95% aller Lieferungen pünktlich beim Kunden ankommen.

Kommissionierungsrate: Die Anzahl der pro Stunde kommissionierten Artikel. Eine hohe Kommissionierungsrate zeigt effiziente Lagerprozesse an.

Tonnage: Das Gesamtgewicht der versendeten Waren pro Tag oder Monat. Die Tonnage ist ein wichtiger Indikator für die Auslastung des Versandbereichs.

ATRL (Automatisches Transport- und Lagersystem): Verfügbarkeit und Performance des automatisierten Lagersystems. Eine hohe ATRL-Verfügbarkeit ist kritisch für den reibungslosen Betrieb.`,
        category: 'dispatch',
        language: 'de'
      },
      {
        title: 'Ablängerei Optimierungsverfahren',
        content: `Schnittoptimierung: Durch intelligente Schnittmuster kann der Materialverbrauch um bis zu 15% reduziert werden. Dabei werden Restlängen systematisch erfasst und wiederverwendet.

Maschinenauslastung: Die optimale Verteilung der Schnittaufträge auf H1 und H3 Maschinen sorgt für gleichmäßige Auslastung und reduziert Wartezeiten.

Qualitätskontrolle: Regelmäßige Kalibrierung der Schnittmaschinen gewährleistet präzise Schnitte und minimiert Ausschuss.

Effizienzsteigerung: Durch Batch-Processing ähnlicher Aufträge können Rüstzeiten reduziert und die Gesamteffizienz gesteigert werden.`,
        category: 'cutting',
        language: 'de'
      },
      {
        title: 'Wareneingang Prozesse',
        content: `Wareneingangskontrolle: Jede eingehende Lieferung wird auf Vollständigkeit und Qualität geprüft. Abweichungen werden sofort dokumentiert und an den Lieferanten gemeldet.

Lagerplatz-Zuordnung: Neue Waren werden basierend auf ABC-Analyse und Umschlagshäufigkeit optimal im Lager platziert. A-Artikel erhalten Plätze mit kurzen Wegen.

Bestandsführung: Alle Warenbewegungen werden in Echtzeit im System erfasst. Dies ermöglicht präzise Bestandsübersicht und verhindert Fehlbestände.

Auslastungsmanagement: Die Lagerauslastung wird kontinuierlich überwacht. Bei Überschreitung von 90% werden automatisch Umlagerungen eingeleitet.`,
        category: 'incoming-goods',
        language: 'de'
      },
      {
        title: 'System Verfügbarkeit und Monitoring',
        content: `Systemverfügbarkeit: Die Gesamtverfügbarkeit aller kritischen Systeme (ATrL, ARiL, FTS) sollte mindestens 95% betragen. Ausfälle werden sofort eskaliert.

Störungsmanagement: Alle Störungen werden kategorisiert nach Schweregrad (Critical, High, Medium, Low) und entsprechend priorisiert bearbeitet.

Präventive Wartung: Regelmäßige Wartungszyklen reduzieren ungeplante Ausfälle um bis zu 60%. Wartungsfenster werden außerhalb der Hauptbetriebszeiten geplant.

Performance Monitoring: Kontinuierliche Überwachung der Systemleistung ermöglicht frühzeitige Erkennung von Leistungseinbußen und proaktive Maßnahmen.`,
        category: 'system',
        language: 'de'
      },
      {
        title: 'Troubleshooting Guide',
        content: `Häufige Probleme und Lösungen:

Problem: Niedriger Servicegrad
Lösung: Prüfen Sie die Kapazitätsauslastung, Personalplanung und Systemverfügbarkeit. Oft hilft eine Umverteilung der Arbeitslasten.

Problem: Hohe Schnittabfälle
Lösung: Überprüfen Sie die Schnittmuster-Optimierung und stellen Sie sicher, dass Restlängen korrekt erfasst werden.

Problem: Lagerüberlauf
Lösung: Initiieren Sie sofortige Auslagerungen und prüfen Sie die Bestandsplanung. Kontaktieren Sie das Disposition-Team.

Problem: Systemausfall
Lösung: Folgen Sie dem Eskalationsplan, aktivieren Sie Backup-Systeme und informieren Sie alle betroffenen Bereiche.`,
        category: 'troubleshooting',
        language: 'de'
      }
    ];
  }

  /**
   * Check embedding service availability and provide feedback
   */
  private async checkEmbeddingService(): Promise<void> {
    const providerInfo = this.embeddingService.getProviderInfo();
    console.log(`🔧 Embedding Provider: ${providerInfo.provider}`);
    console.log(`📊 Model: ${providerInfo.model}`);

    if (providerInfo.provider === 'ollama') {
      const isAvailable = await this.embeddingService.isOllamaAvailable();
      if (isAvailable) {
        const modelAvailable = await this.embeddingService.isModelAvailable(providerInfo.model);
        if (modelAvailable) {
          console.log('✅ Ollama is available and model is ready');
        } else {
          console.log(`⚠️ Ollama is available but model '${providerInfo.model}' not found`);
          console.log(`   Run: ollama pull ${providerInfo.model}`);
        }
      } else {
        console.log('⚠️ Ollama not available, will use mock embeddings');
        console.log('   Install Ollama: https://ollama.ai/');
      }
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.vectorService.disconnect();
    if (this.dbClient) {
      await this.dbClient.close();
    }
  }
}

// Run initialization if called directly
if (require.main === module) {
  const initializer = new PostgreSQLRAGDatabaseInitializer();

  initializer.initialize()
    .then(() => {
      console.log('🎉 RAG Database initialization completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 RAG Database initialization failed:', error);
      process.exit(1);
    })
    .finally(() => {
      initializer.cleanup();
    });
}

export default PostgreSQLRAGDatabaseInitializer;