export interface StoerungCreateData {
  title: string;
  description?: string;
  severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  status: "NEW" | "IN_PROGRESS" | "RESOLVED";
  category?: string;
  assignedTo?: string;
  impact?: string;
  urgency?: string;
  priority?: string;
  tags?: string;
  relatedSystems?: string;
  rootCause?: string;
  preventiveMeasures?: string;
  customerImpact?: string;
  businessImpact?: string;
  communicationPlan?: string;
  escalationLevel?: number;
  isRecurring?: number;
  recurringPattern?: string;
  lastOccurrence?: string;
  nextReview?: string;
  attachments?: string;
  externalTicketId?: string;
  changeRequestId?: string;
  knowledgeBaseId?: string;
  slaDeadline?: string;
  slaStatus?: string;
  resolutionSteps?: string;
  testingRequired?: number;
  testingCompleted?: number;
  approvalRequired?: number;
  approvalReceived?: number;
  deploymentRequired?: number;
  deploymentCompleted?: number;
  monitoringRequired?: number;
  monitoringSetup?: number;
  postIncidentReview?: number;
  lessonsLearned?: string;
  improvementActions?: string;
  costImpact?: number;
  timeToDetection?: number;
  timeToResolution?: number;
  customerSatisfaction?: number;
  teamSatisfaction?: number;
  processEfficiency?: number;
  toolsUsed?: string;
  skillsRequired?: string;
  trainingNeeded?: string;
  vendorInvolved?: string;
  vendorResponse?: string;
  regulatoryImpact?: string;
  complianceIssues?: string;
  auditTrail?: string;
  dataBackup?: number;
  dataRecovery?: number;
  securityImpact?: string;
  privacyImpact?: string;
  environmentalImpact?: string;
  sustainabilityImpact?: string;
}

export interface StoerungUpdateData {
  title?: string;
  description?: string;
  severity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  status?: "NEW" | "IN_PROGRESS" | "RESOLVED";
  category?: string;
  assignedTo?: string;
  resolvedAt?: string;
  estimatedResolution?: string;
  actualResolution?: string;
  impact?: string;
  urgency?: string;
  priority?: string;
  tags?: string;
  relatedSystems?: string;
  rootCause?: string;
  preventiveMeasures?: string;
  customerImpact?: string;
  businessImpact?: string;
  communicationPlan?: string;
  escalationLevel?: number;
  isRecurring?: number;
  recurringPattern?: string;
  lastOccurrence?: string;
  nextReview?: string;
  attachments?: string;
  externalTicketId?: string;
  changeRequestId?: string;
  knowledgeBaseId?: string;
  slaDeadline?: string;
  slaStatus?: string;
  resolutionSteps?: string;
  testingRequired?: number;
  testingCompleted?: number;
  approvalRequired?: number;
  approvalReceived?: number;
  deploymentRequired?: number;
  deploymentCompleted?: number;
  monitoringRequired?: number;
  monitoringSetup?: number;
  postIncidentReview?: number;
  lessonsLearned?: string;
  improvementActions?: string;
  costImpact?: number;
  timeToDetection?: number;
  timeToResolution?: number;
  customerSatisfaction?: number;
  teamSatisfaction?: number;
  processEfficiency?: number;
  toolsUsed?: string;
  skillsRequired?: string;
  trainingNeeded?: string;
  vendorInvolved?: string;
  vendorResponse?: string;
  regulatoryImpact?: string;
  complianceIssues?: string;
  auditTrail?: string;
  dataBackup?: number;
  dataRecovery?: number;
  securityImpact?: string;
  privacyImpact?: string;
  environmentalImpact?: string;
  sustainabilityImpact?: string;
}

// StoerungWithComments interface - Angepasst für Frontend (snake_case Eigenschaften)
export interface StoerungWithComments {
  id: number;
  title: string;
  description?: string;
  severity: string;
  status: string;
  category?: string;
  affected_system?: string; // snake_case für Frontend
  location?: string;
  reported_by?: string; // snake_case für Frontend
  assigned_to?: string; // snake_case für Frontend
  created_at: string; // snake_case für Frontend
  updated_at: string; // snake_case für Frontend
  resolved_at?: string; // snake_case für Frontend
  acknowledged_at?: string; // snake_case für Frontend
  acknowledged_by?: string;
  escalation_level?: string; // snake_case für Frontend
  resolution_steps?: string; // snake_case für Frontend
  root_cause?: string; // snake_case für Frontend
  lessons_learned?: string; // snake_case für Frontend
  mttr_minutes?: number; // snake_case für Frontend
  mtta_minutes?: number; // snake_case für Frontend
  tags?: string[];
  comments: StoerungComment[];
}

// StoerungComment interface - Angepasst an existierende Datenbankstruktur
export interface StoerungComment {
  id: number;
  stoerungId: number;
  userId?: string; // Entspricht user_id in der DB
  comment: string; // Entspricht comment in der DB (nicht content)
  createdAt: string; // Entspricht created_at in der DB (nicht timestamp)
  caretakerId?: string; // Entspricht caretaker_id in der DB
}

// StoerungCommentCreateData interface - Angepasst an existierende Datenbankstruktur
export interface StoerungCommentCreateData {
  stoerungId: number;
  userId?: string; // Entspricht user_id in der DB
  comment: string; // Entspricht comment in der DB (nicht content)
  caretakerId?: string; // Entspricht caretaker_id in der DB
}

export interface StoerungsStats {
  total: number;
  active: number;
  resolved: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  avg_mttr_minutes: number;
  resolution_rate_24h: number;
}

export interface SystemStatusMessage {
  id: number;
  title: string;
  description: string;
  priority: number;
  category: string;
}

export interface SystemStatusData {
  id: number;
  system_name: string; // Use snake_case to match frontend interface
  status: "OK" | "WARNING" | "ERROR" | "OFF";
  last_check: string; // Use snake_case to match frontend interface
  metadata?: string;
  created_at: string; // Use snake_case to match frontend interface
  updated_at: string; // Use snake_case to match frontend interface
  statusMessages?: SystemStatusMessage[];
}

export interface SystemStatusUpdateData {
  system_name: string; // Use snake_case to match frontend interface
  status: "OK" | "WARNING" | "ERROR" | "OFF";
  metadata?: string;
}

// StoerungAttachment interface - Match actual PostgreSQL table structure
export interface StoerungAttachment {
  id: number;
  stoerungId: number;
  filename: string;
  storedName?: string | null; // Match actual DB field
  filePath?: string | null; // Match actual DB field
  fileSize: number;
  mimeType: string;
  fileType?: string | null; // Match actual DB field
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

// StoerungAttachmentCreateData interface - Match actual PostgreSQL table structure
export interface StoerungAttachmentCreateData {
  stoerungId: number;
  filename: string;
  storedName?: string | null;
  filePath?: string | null;
  fileSize: number;
  mimeType: string;
  fileType?: string | null;
  uploadedBy: string;
}
