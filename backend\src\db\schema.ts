import {
  pgTable,
  integer,
  text,
  real,
  index,
  timestamp,
  boolean,
  serial,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

// Drizzle ORM Schema - vollständig migriert von Prisma

// DispatchData table - main table causing the current error
export const dispatchData = pgTable(
  "dispatch_data",
  {
    id: serial("id").primaryKey(),
    datum: text("datum"), // Datum ist als Text gespeichert (YYYY-MM-DD)
    tag: integer("tag"),
    monat: integer("monat"),
    kw: integer("kw"),
    jahr: integer("jahr"),
    servicegrad: real("servicegrad"),
    ausgeliefert_lup: integer("ausgeliefert_lup"),
    rueckstaendig: integer("rueckstaendig"),
    produzierte_tonnagen: real("produzierte_tonnagen"),
    direktverladung_kiaa: integer("direktverladung_kiaa"),
    umschlag: integer("umschlag"),
    kg_pro_colli: real("kg_pro_colli"),
    elefanten: integer("elefanten"),
    atrl: integer("atrl"),
    aril: integer("aril"),
    fuellgrad_aril: real("fuellgrad_aril"),
    qm_angenommen: integer("qm_angenommen"),
    qm_abgelehnt: integer("qm_abgelehnt"),
    qm_offen: integer("qm_offen"),
    mitarbeiter_std: real("mitarbeiter_std"),
  },
  (table) => ({
    datumIdx: index("dispatch_data_datum_idx").on(table.datum),
  }),
);

// Alias für Kompatibilität
export const DispatchData = dispatchData;

// ARiL table
export const ariL = pgTable(
  "ARiL",
  {
    id: serial("id").primaryKey(),
    Datum: text("Datum"),
    waTaPositionen: integer("waTaPositionen"),
    Umlagerungen: integer("Umlagerungen"),
    belegtePlaetze: integer("belegtePlaetze"),
    systemtablareRuecksackStk: integer("systemtablareRuecksackStk"),
    systemtablareGesamtStk: integer("systemtablareGesamtStk"),
    systemtablareEinzelBelegt: integer("systemtablareEinzelBelegt"),
    belegtRinge: integer("belegtRinge"),
    auslastung: real("Auslastung"),
    alleBewegungen: integer("alleBewegungen"),
    cuttingLagerKunde: integer("cuttingLagerKunde"),
    cuttingLagerRest: integer("cuttingLagerRest"),
    lagerCutting: integer("lagerCutting"),
  },
  (table) => ({
    datumIdx: index("ARiL_datum_idx").on(table.Datum),
  }),
);

// Alias für Kompatibilität
export const ARiL = ariL;

// ATrL table - KORRIGIERT: Datum ist TEXT in der DB
export const atrL = pgTable(
  "ATrL",
  {
    id: serial("id").primaryKey(),
    Datum: text("Datum"), // TEXT in der DB und Großgeschrieben!
    umlagerungen: integer("umlagerungen"),
    waTaPositionen: integer("waTaPositionen"),
    belegtePlaetze: integer("belegtePlaetze"),
    davonSystempaletten: integer("davonSystempaletten"),
    SystempalettenstapelRucksackpaetzen: integer(
      "SystempalettenstapelRucksackpaetzen",
    ),
    SystempalettenstapelEinzel: integer("SystempalettenstapelEinzel"),
    PlaetzeSystempalettenstapelEinzel: integer(
      "PlaetzeSystempalettenstapelEinzel",
    ),
    plaetzeMitTrommelBelegt: integer("plaetzeMitTrommelBelegt"),
    Auslastung: real("Auslastung"),
    Bewegungen: integer("Bewegungen"),
    EinlagerungAblKunde: integer("EinlagerungAblKunde"),
    EinlagerungAblRest: integer("EinlagerungAblRest"),
    AuslagerungAbl: integer("AuslagerungAbl"),
    weAtrl: integer("weAtrl"),
  },
  (table) => ({
    datumIdx: index("ATrL_Datum_idx").on(table.Datum),
  }),
);

// Alias für Kompatibilität
export const ATrL = atrL;

// Ablaengerei table
export const ablaengerei = pgTable(
  "Ablaengerei",
  {
    id: serial("id").primaryKey(),
    Datum: text("Datum"), // WICHTIG: In der DB ist es als TEXT gespeichert, nicht als integer!
    cutLagerK220: integer("cutLagerK220").default(0),
    cutLagerR220: integer("cutLagerR220").default(0),
    lagerCut220: integer("lagerCut220").default(0),
    cutLagerK240: integer("cutLagerK240").default(0),
    cutLagerR240: integer("cutLagerR240").default(0),
    lagerCut240: integer("lagerCut240").default(0),
    cutTT: integer("cutTT").default(0), // Korrekt: cutTT mit großem TT
    cutTR: integer("cutTR").default(0),
    cutRR: integer("cutRR").default(0),
    cutGesamt: integer("cutGesamt").default(0),
    pickCut: integer("pickCut").default(0),
    cutLager200: integer("cutLager200").default(0),
    cutLagerK200: integer("cutLagerK200").default(0),
    lagerCut200: integer("lagerCut200").default(0),
  },
  (table) => ({
    datumIdx: index("Ablaengerei_datum_idx").on(table.Datum),
  }),
);

// Alias für Kompatibilität
export const Ablaengerei = ablaengerei;

// System table
export const system = pgTable(
  "System",
  {
    id: serial("id").primaryKey(),
    datum: timestamp("datum", { mode: "date" }),
    verfuegbarkeitFts: real("verfuegbarkeitFTS"),
  },
  (table) => ({
    datumIdx: index("System_datum_idx").on(table.datum),
  }),
);

// Alias für Kompatibilität
export const System = system;

// WE table (Wareneingang)
export const we = pgTable(
  "WE",
  {
    id: serial("id").primaryKey(),
    datum: text("Datum"), // WICHTIG: Spaltenname in DB ist "Datum" (großgeschrieben)
    weAtrl: integer("weAtrl").default(0),
    weManl: integer("weManl").default(0),
  },
  (table) => ({
    datumIdx: index("WE_datum_idx").on(table.datum),
  }),
);

// Alias für Kompatibilität
export const WE = we;

// Auslastung200 table - KORRIGIERT: aufnahmeDatum ist TEXT in der DB
export const auslastung200 = pgTable(
  "auslastung200",
  {
    id: serial("id").primaryKey(),
    aufnahmeDatum: text("aufnahmeDatum"), // TEXT in der DB, nicht integer!
    aufnahmeZeit: text("aufnahmeZeit"),
    maxPlaetze: text("maxPlaetze"),
    auslastung: text("auslastung"),
    maxA: text("maxA"),
    maxB: text("maxB"),
    maxC: text("maxC"),
    auslastungA: text("auslastungA"), // TEXT in der DB!
    auslastungB: text("auslastungB"), // TEXT in der DB!
    auslastungC: text("auslastungC"), // TEXT in der DB!
    importTimestamp: timestamp("import_timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => ({
    aufnahmeDatumIdx: index("auslastung200_aufnahmeDatum_idx").on(
      table.aufnahmeDatum,
    ),
    importTimestampIdx: index("auslastung200_import_timestamp_idx").on(
      table.importTimestamp,
    ),
  }),
);

// Auslastung240 table
export const auslastung240 = pgTable(
  "auslastung240",
  {
    id: serial("id").primaryKey(),
    aufnahmeDatum: text("aufnahmeDatum"), // TEXT in der DB, nicht integer!
    aufnahmeZeit: text("aufnahmeZeit"),
    maxPlaetze: text("maxPlaetze"),
    auslastung: text("auslastung"),
    maxA: text("maxA"),
    maxB: text("maxB"),
    maxC: text("maxC"),
    auslastungA: text("auslastungA"), // TEXT in der DB!
    auslastungB: text("auslastungB"), // TEXT in der DB!
    auslastungC: text("auslastungC"),
  },
  (table) => ({
    aufnahmeDatumIdx: index("auslastung240_aufnahmeDatum_idx").on(
      table.aufnahmeDatum,
    ),
  }),
);

// Maschinen table
export const maschinen = pgTable("maschinen", {
  id: serial("id").primaryKey(),
  machine: text("Machine").unique(),
  schnitteProStd: real("schnitteProStd"),
});

// Alias für Kompatibilität
export const Maschinen = maschinen;

// Schnitte table - vollständig mit allen Maschinen
export const schnitte = pgTable("schnitte", {
  id: serial("id").primaryKey(),
  datum: text("Datum"),
  // H1-Maschinen (Halle 1)
  m5RH1: integer("M5-R-H1"), // Maschine 5 - Ring - Halle 1
  m6TH1: integer("M6-T-H1"), // Maschine 6 - Trommel - Halle 1
  m7RH1: integer("M7-R-H1"), // Maschine 7 - Ring - Halle 1
  m8TH1: integer("M8-T-H1"), // Maschine 8 - Trommel - Halle 1
  m9RH1: integer("M9-R-H1"), // Maschine 9 - Ring - Halle 1
  m10TH1: integer("M10-T-H1"), // Maschine 10 - Trommel - Halle 1
  m11RH1: integer("M11-R-H1"), // Maschine 11 - Ring - Halle 1
  m12TH1: integer("M12-T-H1"), // Maschine 12 - Trommel - Halle 1
  m13RH1: integer("M13-R-H1"), // Maschine 13 - Ring - Halle 1
  m14TH1: integer("M14-T-H1"), // Maschine 14 - Trommel - Halle 1
  m15RH1: integer("M15-R-H1"), // Maschine 15 - Ring - Halle 1
  m16TH1: integer("M16-T-H1"), // Maschine 16 - Trommel - Halle 1
  m17RH1: integer("M17-R-H1"), // Maschine 17 - Ring - Halle 1
  m18TH1: integer("M18-T-H1"), // Maschine 18 - Trommel - Halle 1
  m19TH1: integer("M19-T-H1"), // Maschine 19 - Trommel - Halle 1
  m20TH1: integer("M20-T-H1"), // Maschine 20 - Trommel - Halle 1
  m21RH1: integer("M21-R-H1"), // Maschine 21 - Ring - Halle 1
  m23TH1: integer("M23-T-H1"), // Maschine 23 - Trommel - Halle 1
  m25RRH1: integer("M25-RR-H1"), // Maschine 25 - Ring-Ring - Halle 1
  m26TH1: integer("M26-T-H1"), // Maschine 26 - Trommel - Halle 1
  sumH1: integer("Sum-H1"), // Summe aller H1-Maschinen
  // H3-Maschinen (Halle 3)
  m1TH3: integer("M1-T-H3"), // Maschine 1 - Trommel - Halle 3
  m2TH3: integer("M2-T-H3"), // Maschine 2 - Trommel - Halle 3
  m3RH3: integer("M3-R-H3"), // Maschine 3 - Ring - Halle 3
  m4TH3: integer("M4-T-H3"), // Maschine 4 - Trommel - Halle 3
  m22TH3: integer("M22-T-H3"), // Maschine 22 - Trommel - Halle 3
  m24TH3: integer("M24-T-H3"), // Maschine 24 - Trommel - Halle 3
  m27RH3: integer("M27-R-H3"), // Maschine 27 - Ring - Halle 3
  sumH3: integer("Sum-H3"), // Summe aller H3-Maschinen
});

// Alias für Kompatibilität
export const Schnitte = schnitte;

// Materialdaten table
export const materialdaten = pgTable("materialdaten", {
  matnr: text("matnr").primaryKey(),
  materialkurztext: text("materialkurztext"),
  kabeldurchmesser: real("kabeldurchmesser"),
  zuschlagKabeldurchmesser: real("zuschlagKabeldurchmesser"),
  biegefaktor: real("biegefaktor"),
  kleinsterErlauberFreiraum: real("kleinsterErlauberFreiraum"),
  bruttogewicht: real("bruttogewicht"),
  created_at: text("created_at"),
  updated_at: text("updated_at"),
});

// Alias für Kompatibilität
export const Materialdaten = materialdaten;

// Trommeldaten table
export const trommeldaten = pgTable("trommeldaten", {
  trommeldaten: text("trommeldaten").primaryKey(),
  aussendurchmesser: real("aussendurchmesser"),
  kerndurchmesser: real("kerndurchmesser"),
});

// Alias für Kompatibilität
export const Trommeldaten = trommeldaten;

// Stoerungen table - Angepasst an existierende Datenbankstruktur
// Schema entspricht der tatsächlichen Tabelle in sfm_dashboard.db
export const stoerungen = pgTable("Stoerungen", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  severity: text("severity").notNull(),
  status: text("status").notNull(),
  category: text("category"),
  assignedTo: text("assigned_to"),
  affectedSystem: text("affected_system"),
  location: text("location"),
  reportedBy: text("reported_by"),
  createdAt: text("created_at").notNull(),
  updatedAt: text("updated_at").notNull(),
  resolvedAt: text("resolved_at"),
  mttrMinutes: integer("mttr_minutes"),
  tags: text("tags"),
  resolutionSteps: text("resolution_steps"),
  rootCause: text("root_cause"),
  lessonsLearned: text("lessons_learned"),
});

// Alias für Kompatibilität mit dem bestehenden Code
export const Stoerungen = stoerungen;

// StoerungsComments table - Angepasst an existierende Datenbankstruktur
// Schema entspricht der tatsächlichen Tabelle in sfm_dashboard.db
export const stoerungsComments = pgTable("StoerungsComments", {
  id: serial("id").primaryKey(),
  // Spaltenname entspricht der existierenden DB: stoerung_id (nicht stoerungId)
  stoerungId: integer("stoerung_id").notNull(),
  // Spaltenname entspricht der existierenden DB: user_id (nicht author)
  userId: text("user_id"),
  // Spaltenname entspricht der existierenden DB: comment (nicht content)
  comment: text("comment").notNull(),
  // Spaltenname entspricht der existierenden DB: created_at (nicht timestamp)
  createdAt: text("created_at").notNull(),
  // Zusätzliche Spalte aus der existierenden DB
  caretakerId: text("caretaker_id"),
});

// Alias für Kompatibilität
export const StoerungsComments = stoerungsComments;

// StoerungsAttachment table - Match existing PostgreSQL table structure
export const stoerungsAttachment = pgTable("stoerungsattachment", {
  id: serial("id").primaryKey(),
  stoerungId: integer("stoerung_id").notNull(),
  filename: text("filename").notNull(),
  storedName: text("stored_name"), // Match actual DB field
  filePath: text("file_path"), // Match actual DB field
  fileSize: integer("file_size").notNull(),
  mimeType: text("mime_type").notNull(),
  fileType: text("file_type"), // Match actual DB field
  uploadedBy: text("uploaded_by").notNull(),
  createdAt: text("created_at").notNull(),
  updatedAt: text("updated_at").notNull(),
});

// SystemStatus table - Aligned with actual database structure
export const systemStatus = pgTable("SystemStatus", {
  id: serial("id").primaryKey(),
  systemName: text("system_name").notNull(),
  status: text("status").notNull(),
  lastUpdated: text("last_check").notNull(),
  metadata: text("metadata"),
  createdAt: text("created_at").notNull(),
  updatedAt: text("updated_at").notNull(),
});

// Alias für Kompatibilität
export const SystemStatus = systemStatus;

// SystemStatusMessage table - Aligned with actual database structure
export const systemStatusMessage = pgTable("SystemStatusMessage", {
  id: serial("id").primaryKey(),
  systemStatusId: integer("system_status_id"),
  category: text("category").notNull(),
  status: text("status").notNull(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  priority: integer("priority").default(1),
  isActive: boolean("is_active").default(true),
  createdAt: text("created_at").notNull(),
  updatedAt: text("updated_at").notNull(),
});

// Alias für Kompatibilität
export const SystemStatusMessage = systemStatusMessage;

// User table
export const user = pgTable("User", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  passwordHash: text("passwordHash").notNull(),
  role: text("role").notNull().default("user"),
  isActive: boolean("isActive").default(true),
  createdAt: text("createdAt").notNull(),
  updatedAt: text("updatedAt").notNull(),
  lastLogin: text("lastLogin"),
  loginAttempts: integer("loginAttempts").default(0),
  lockedUntil: text("lockedUntil"),
  resetToken: text("resetToken"),
  resetTokenExpires: text("resetTokenExpires"),
  emailVerified: boolean("emailVerified").default(false),
  emailVerificationToken: text("emailVerificationToken"),
  twoFactorEnabled: boolean("twoFactorEnabled").default(false),
  twoFactorSecret: text("twoFactorSecret"),
  preferences: text("preferences"),
  avatar: text("avatar"),
  firstName: text("firstName"),
  lastName: text("lastName"),
  phone: text("phone"),
  department: text("department"),
  position: text("position"),
  manager: text("manager"),
  location: text("location"),
  timezone: text("timezone"),
  language: text("language").default("de"),
  theme: text("theme").default("light"),
  notifications: text("notifications"),
});

// Alias für Kompatibilität
export const User = user;

// Bereitschafts tables - Angepasst an die bestehende Datenbankstruktur
// BereitschaftsPersonen table - Angepasst an die tatsächliche Datenbankstruktur
export const bereitschaftsPersonen = pgTable("bereitschafts_personen", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  telefon: text("telefon"), // Optional in der DB
  email: text("email").notNull(),
  abteilung: text("abteilung"), // Optional in der DB
  aktiv: boolean("aktiv").default(true),
  reihenfolge: integer("reihenfolge").default(0),
  createdAt: text("created_at").notNull(),
  updatedAt: text("updated_at").notNull(),
});

// Alias für Kompatibilität
export const BereitschaftsPersonen = bereitschaftsPersonen;

// BereitschaftsWochen table - Angepasst an die tatsächliche Datenbankstruktur
export const bereitschaftsWochen = pgTable("bereitschafts_wochen", {
  id: serial("id").primaryKey(),
  personId: integer("person_id").notNull(),
  wochenStart: text("wochen_start").notNull(),
  wochenEnde: text("wochen_ende").notNull(),
  von: text("von").notNull(),
  bis: text("bis").notNull(),
  aktiv: boolean("aktiv").default(true),
  notiz: text("notiz"),
  createdAt: text("created_at").notNull(),
  updatedAt: text("updated_at").notNull(),
});

// Alias für Kompatibilität
export const BereitschaftsWochen = bereitschaftsWochen;

// BereitschaftsAusnahmen table
export const bereitschaftsAusnahmen = pgTable("bereitschafts_ausnahmen", {
  id: serial("id").primaryKey(),
  personId: integer("person_id").notNull(),
  von: text("von").notNull(), // KORRIGIERT: DATETIME in der DB
  bis: text("bis").notNull(), // KORRIGIERT: DATETIME in der DB
  grund: text("grund").notNull(),
  ersatzPersonId: integer("ersatz_person_id"), // Optional
  aktiv: boolean("aktiv").default(true), // KORRIGIERT: BOOLEAN in der DB
  createdAt: text("created_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
  updatedAt: text("updated_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
});

// Alias für Kompatibilität
export const BereitschaftsAusnahmen = bereitschaftsAusnahmen;

// BereitschaftsKonfiguration table
export const bereitschaftsKonfiguration = pgTable(
  "bereitschafts_konfiguration",
  {
    id: serial("id").primaryKey(),
    wechselTag: integer("wechsel_tag").notNull().default(5), // KORRIGIERT: NOT NULL in der DB
    wechselUhrzeit: text("wechsel_uhrzeit").notNull().default("08:00"), // KORRIGIERT: NOT NULL in der DB
    rotationAktiv: boolean("rotation_aktiv").notNull().default(true), // KORRIGIERT: BOOLEAN NOT NULL in der DB
    benachrichtigungTage: integer("benachrichtigung_tage").notNull().default(2), // KORRIGIERT: NOT NULL in der DB
    emailBenachrichtigung: boolean("email_benachrichtigung")
      .notNull()
      .default(true), // KORRIGIERT: BOOLEAN NOT NULL in der DB
    createdAt: text("created_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
    updatedAt: text("updated_at").notNull(), // KORRIGIERT: DATETIME mit CURRENT_TIMESTAMP
  },
);

// Alias für Kompatibilität
export const BereitschaftsKonfiguration = bereitschaftsKonfiguration;

// Runbook table
export const runbook = pgTable(
  "Runbook",
  {
    id: serial("id").primaryKey(),
    title: text("title").notNull(),
    content: text("content").notNull(), // Markdown content
    affected_systems: text("affected_systems"), // JSON array as string
    category: text("category"), // JSON array as string (formerly tags)
    steps: text("steps"), // JSON array of steps
    created_at: text("created_at").notNull(),
    updated_at: text("updated_at").notNull(),
  },
  (table) => ({
    createdAtIdx: index("Runbook_created_at_idx").on(table.created_at),
    updatedAtIdx: index("Runbook_updated_at_idx").on(table.updated_at),
  }),
);

// Alias für Kompatibilität
export const Runbook = runbook;

// Bestand200 table (Lagerbestand Lager 200)
export const bestand200 = pgTable(
  "bestand200",
  {
    id: serial("id").primaryKey(),
    lagertyp: text("Lagertyp"),
    lagerplatz: text("Lagerplatz"), // DB-Spalte heißt "Lagerplatz" (großes L)
    material: text("Material"),
    charge: text("Charge"),
    dauer: real("Dauer"),
    lagerbereich: text("Lagerbereich"),
    lagerplatztyp: text("Lagerplatztyp"),
    lagerplatzaufteilung: text("Lagerplatzaufteilung"),
    auslagerungssperre: text("Auslagerungssperre"),
    einlagerungssperre: text("Einlagerungssperre"),
    sperrgrund: text("Sperrgrund"),
    letzteBewegung: text("Letzte Bewegung"),
    uhrzeit: text("Uhrzeit"),
    taNummer: text("TA-Nummer"),
    taPosition: text("TA-Position"),
    letzterAenderer: text("Letzter Änderer"),
    letzteAenderung: text("Letzte Änderung"),
    wareneingangsdatum: text("Wareneingangsdatum"),
    weNummer: text("WE-Nummer"),
    wePosition: text("WE-Position"),
    lieferung: text("Lieferung"),
    position: text("Position"),
    lagereinheitentyp: text("Lagereinheitentyp"),
    gesamtbestand: real("Gesamtbestand"),
    lagereinheit: text("Lagereinheit"),
    aufnahmeDatum: text("aufnahmeDatum"),
    aufnahmeZeit: text("aufnahmeZeit"),
    maxPlaetze: text("maxPlaetze"),
    auslastung: text("auslastung"),
    maxA: text("maxA"),
    maxB: text("maxB"),
    maxC: text("maxC"),
    auslastungA: text("auslastungA"),
    auslastungB: text("auslastungB"),
    auslastungC: text("auslastungC"),
    importTimestamp: timestamp("import_timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => ({
    importTimestampIdx: index("bestand200_import_timestamp_idx").on(
      table.importTimestamp,
    ),
    aufnahmeDatumIdx: index("bestand200_aufnahmeDatum_idx").on(
      table.aufnahmeDatum,
    ),
  }),
);

// Alias für Kompatibilität
export const Bestand200 = bestand200;

// Performance Monitoring Tables
export const responseTimeMetric = pgTable(
  "response_time_metric",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    totalTime: real("total_time").notNull(),
    success: boolean("success").notNull(),
    enriched: boolean("enriched").notNull(),
    dataSize: integer("data_size"),
  },
  (table) => ({
    timestampIdx: index("response_time_metric_timestamp_idx").on(
      table.timestamp,
    ),
  }),
);

// Alias für Kompatibilität
export const ResponseTimeMetric = responseTimeMetric;

// QueryPerformanceMetric table
export const queryPerformanceMetric = pgTable(
  "query_performance_metric",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    queryType: text("query_type").notNull(),
    duration: real("duration").notNull(),
    success: boolean("success").notNull(),
    cacheHit: boolean("cache_hit").notNull(),
  },
  (table) => ({
    timestampIdx: index("query_performance_metric_timestamp_idx").on(
      table.timestamp,
    ),
  }),
);

// Alias für Kompatibilität
export const QueryPerformanceMetric = queryPerformanceMetric;

// IntentRecognitionMetric table
export const intentRecognitionMetric = pgTable(
  "intent_recognition_metric",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    accuracy: real("accuracy").notNull(),
    confidence: real("confidence").notNull(),
  },
  (table) => ({
    timestampIdx: index("intent_recognition_metric_timestamp_idx").on(
      table.timestamp,
    ),
  }),
);

// Alias für Kompatibilität
export const IntentRecognitionMetric = intentRecognitionMetric;

// EnrichmentPerformanceMetric table
export const enrichmentPerformanceMetric = pgTable(
  "enrichment_performance_metric",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    enrichmentTime: real("enrichment_time").notNull(),
    dataSourcesUsed: integer("data_sources_used").notNull(),
    success: boolean("success").notNull(),
  },
  (table) => ({
    timestampIdx: index("enrichment_performance_metric_timestamp_idx").on(
      table.timestamp,
    ),
  }),
);

// Alias für Kompatibilität
export const EnrichmentPerformanceMetric = enrichmentPerformanceMetric;

// PerformanceAlert table
export const performanceAlert = pgTable(
  "performance_alert",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    alertType: text("alert_type").notNull(),
    severity: text("severity").notNull(),
    message: text("message").notNull(),
    resolved: boolean("resolved").default(false),
  },
  (table) => ({
    timestampIdx: index("performance_alert_timestamp_idx").on(table.timestamp),
  }),
);

// Alias für Kompatibilität
export const PerformanceAlert = performanceAlert;

// Workflow Tables
export const workflowLog = pgTable(
  "workflow_log",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    level: text("level").notNull(),
    message: text("message").notNull(),
    workflowId: text("workflow_id"),
    executionId: text("execution_id"),
    details: text("details"),
  },
  (table) => ({
    timestampIdx: index("workflow_log_timestamp_idx").on(table.timestamp),
  }),
);

// Alias für Kompatibilität
export const WorkflowLog = workflowLog;

export const workflowExecution = pgTable(
  "workflow_execution",
  {
    id: serial("id").primaryKey(),
    timestamp: timestamp("timestamp", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    workflowId: text("workflow_id").notNull(),
    status: text("status").notNull(),
    startTime: timestamp("start_time", { mode: "date" }),
    endTime: timestamp("end_time", { mode: "date" }),
    result: text("result"),
  },
  (table) => ({
    timestampIdx: index("workflow_execution_timestamp_idx").on(table.timestamp),
  }),
);

// Alias für Kompatibilität
export const WorkflowExecution = workflowExecution;
