import React, { useEffect, useState, memo } from "react";
import {
  Bar,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  ComposedChart,
} from "recharts";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import apiService from "@/services/api.service";

/**
 * PickingChart Komponente
 * 
 * Zeigt die Picking-Kennzahlen als kombiniertes Balken- und Liniendiagramm an:
 * - ATRL (Balken)
 * - ARIL (Balken)
 * - Füllgrad ARIL (Linie)
 * 
 * @param data Optional: A<PERSON><PERSON> von <PERSON> mit den verschiedenen Kennzahlen
 */

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

// Definition des Datentyps für die Picking-Daten
interface PickingDataPoint {
  date: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
}

// Definition des Datentyps für Datenbankzeilen
interface DatabaseRow {
  date: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
}

interface PickingChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: PickingDataPoint[];
  dateRange?: DateRange;
}

// Footer-Komponente für die PickingChart
export function PickingChartFooter() {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<PickingDataPoint[]>([]);
   
  // Berechne Durchschnittswerte für den Footer
  const avgAtrl = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.atrl, 0) / chartData.length : 0;
  const avgAril = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.aril, 0) / chartData.length : 0;
  const avgFuellgradAril = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.fuellgrad_aril, 0) / chartData.length : 0;
  const avgDifference = avgAril - avgAtrl;
  
  return (
    <div className="w-full">
      <div className="flex justify-between w-full">
        <div className="flex items-center gap-2">
          <span className="font-medium">{t("atrl")}:</span> 
          <span className="text-muted-foreground">{avgAtrl.toFixed(1)}%</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">{t("aril")}:</span> 
          <span className="text-muted-foreground">{avgAril.toFixed(1)}%</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">{t("fillLevelAril")}:</span> 
          <span className="text-muted-foreground">{avgFuellgradAril.toFixed(1)}%</span>
        </div>
      </div>
      <div className="flex justify-between w-full mt-2">
        <div className="flex items-center gap-2">
          <span className="font-medium">{t("difference")}:</span> 
          <span className="text-muted-foreground">{avgDifference.toFixed(1)}%</span>
        </div>
        <div className="text-muted-foreground text-xs">
          {t("updated")}: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}

export const PickingChart = memo(function PickingChart({ data: propData, dateRange }: PickingChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<PickingDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm
  const chartConfig = {
    atrl: {
      label: t("atrl"),
      color: "var(--chart-1)",
    },
    aril: {
      label: t("aril"),
      color: "var(--chart-2)",
    },
    fuellgrad_aril: {
      label: t("fillLevelAril"),
      color: "var(--chart-3)",
    },
  };

  // Lade Daten aus CSV-Werten, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      // Filtere Daten basierend auf dem ausgewählten Datumsbereich
      let filteredData = propData;
      if (dateRange?.from || dateRange?.to) {
        filteredData = propData.filter((item) => {
          const itemDate = new Date(item.date);
          
          if (dateRange.from && itemDate < dateRange.from) return false;
          if (dateRange.to && itemDate > dateRange.to) return false;
          
          return true;
        });  
      }
      
      // Sortiere die Daten chronologisch nach Datum (älteste zuerst)
      try {
        filteredData.sort((a, b) => {
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          return dateA.getTime() - dateB.getTime();
        });
      } catch (error) {
        console.warn('PickingChart: Fehler beim Sortieren der Daten:', error);
      }
      
      setChartData(filteredData);
    } else {
      // Lade Daten aus der Datenbank
      const loadData = async () => {
        try {
          setLoading(true);
          setError(null);

          // Konvertiere dateRange zu API-Format
          const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
          const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

          // Versuche Daten aus der Datenbank zu laden
          const result = await apiService.getPickingData(startDate, endDate);

          if (result && Array.isArray(result)) {

            // Konvertiere Datenbankdaten zu Chart-Format
            let processedData = result.map((row: unknown) => {
              const dbRow = row as any; // Flexiblere Typisierung für Date-Handling

              // Backend liefert echte Datumswerte
              let dateValue: string;
              if (typeof dbRow.date === 'string') {
                dateValue = dbRow.date;
              } else if (dbRow.date instanceof Date) {
                dateValue = dbRow.date.toISOString().split('T')[0];
              } else if (typeof dbRow.date === 'number') {
                dateValue = new Date(dbRow.date).toISOString().split('T')[0];
              } else {
                console.warn('PickingChart: Null/undefined Datum - überspringe Datensatz:', dbRow);
                return { date: '', atrl: 0, aril: 0, fuellgrad_aril: 0 };
              }

              let fuellgradValue = Number(dbRow.fuellgrad_aril) || 0;

              // Wenn der Wert zwischen 0 und 1 liegt, handelt es sich um einen Dezimalwert (z.B. 0.9 = 90%)
              if (fuellgradValue > 0 && fuellgradValue <= 1) {
                fuellgradValue = fuellgradValue * 100;
              }

              return {
                date: dateValue,
                atrl: Number(dbRow.atrl) || 0,
                aril: Number(dbRow.aril) || 0,
                fuellgrad_aril: fuellgradValue
              };
            });

            // Filtere Daten basierend auf dem ausgewählten Datumsbereich
            if (dateRange?.from || dateRange?.to) {
              processedData = processedData.filter((item) => {
                const itemDate = new Date(item.date);

                if (dateRange.from && itemDate < dateRange.from) return false;
                if (dateRange.to && itemDate > dateRange.to) return false;

                return true;
              });
            }

            // Sortiere die Daten chronologisch nach Datum (älteste zuerst)
            try {
              processedData.sort((a, b) => {
                const dateA = new Date(a.date);
                const dateB = new Date(b.date);
                return dateA.getTime() - dateB.getTime();
              });
            } catch (error) {
              console.warn('PickingChart: Fehler beim Sortieren der Daten:', error);
            }

            setChartData(processedData);
          } else {
            throw new Error('Keine gültigen Daten erhalten');
          }
        } catch (err) {
          setError('Fehler beim Laden der Daten aus der Datenbank.');
          setChartData([]);
        } finally {
          setLoading(false);
        }
      };

      loadData();
    }
  }, [propData, dateRange]); // dateRange als Abhängigkeit hinzugefügt

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="text-red-500 text-4xl mb-2">⚠️</div>
        <p className="font-bold text-red-500">{error}</p>
      </div>
    );
  }

  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-105 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>KOMMISSIONIERUNG</CardTitle>
        <CardDescription>
          ATRL, ARIL und Füllgrad Kennzahlen
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="w-full h-60"
        >
              <ComposedChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="date" 
                  className="text-xs sm:text-sm"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={6}
                  height={40}
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    try {
                      // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                      const date = new Date(value);
                      if (!isNaN(date.getTime())) {
                        return date.toLocaleDateString('de-DE', { 
                          day: '2-digit', 
                          month: '2-digit' 
                        });
                      }
                      // Falls es kein gültiges Datum ist, zeige den String direkt
                      return String(value);
                    } catch {
                      return value;
                    }
                  }}
                />
                <YAxis 
                  yAxisId="left"
                  className="text-xs sm:text-sm"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={6}
                  tick={{ fontSize: 12 }}
                  width={35}
                  domain={[0, 100]}
                />
                <YAxis 
                  yAxisId="right"
                  orientation="right"
                  className="text-xs sm:text-sm"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={6}
                  tick={{ fontSize: 12 }}
                  width={35}
                  domain={[0, 100]}
                />
                <ChartTooltip
                  cursor={false}
                  content={
                    <ChartTooltipContent
                      indicator="dot"
                      labelClassName="font-bold"
                      labelFormatter={(label) => {
                        // Formatiere das Datum im Tooltip als DD.MM.YYYY
                        try {
                          const date = new Date(label);
                          return `${t("date")}: ${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
                        } catch {
                          return `${t("date")}: ${label}`;
                        }
                      }}
                    />
                  }
                />
                <ChartLegend content={({ payload }) => (
                  <ChartLegendContent 
                    payload={payload} 
                    className="p-2 rounded-md"
                  />
                )} />
                <Bar 
                  yAxisId="left"
                  dataKey="atrl" 
                  name={t("atrl")} 
                  stroke="#000000"
                  strokeWidth={2}
                  fill="var(--chart-1)" 
                  radius={4} 
                />
                <Bar 
                  yAxisId="left"
                  dataKey="aril" 
                  name={t("aril")}
                  stroke="#000000"
                  strokeWidth={2} 
                  fill="var(--chart-2)" 
                  radius={4} 
                />
                <Line 
                  yAxisId="right"
                  type="monotone"
                  dataKey="fuellgrad_aril" 
                  name={t("fillGradeAril")} 
                  stroke="var(--chart-3)" 
                  strokeWidth={3}
                  dot={{
                    fill: "var(--chart-3)",
                    stroke: "#000",
                    strokeWidth: 2,
                    r: 4,
                  }}
                />
              </ComposedChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Picking-Kennzahlen aus der Datenbank
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Letzte Aktualisierung: {new Date().toLocaleDateString()}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
