import { Router } from "express";
import { UserController } from "../controllers/user.controller";
import rateLimit from "express-rate-limit";

const router = Router();
const controller = new UserController();

// Rate limiting for user endpoints (stricter limits for auth-related endpoints)
const userLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === "development" ? 100 : 50, // Stricter limit for user operations
  message: "Too many requests to user API from this IP",
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all routes
router.use(userLimiter);

// User search routes
router.get('/search', controller.findByEmailOrUsername.bind(controller));
router.get('/email/:email', controller.findByEmail.bind(controller));
router.get('/username/:username', controller.findByUsername.bind(controller));
router.get('/:id', controller.findById.bind(controller));

// User management routes
router.post('/', controller.createUser.bind(controller));
router.get('/all', controller.getAllUsers.bind(controller));
router.get('/role/:roleName', controller.getUsersByRole.bind(controller));

export default router;