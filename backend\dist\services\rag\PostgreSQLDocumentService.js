"use strict";
/**
 * PostgreSQLDocumentService - Document management for PostgreSQL RAG
 *
 * Provides document management functionality using PostgreSQL
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostgreSQLDocumentService = void 0;
const pg_1 = require("pg");
const uuid_1 = require("uuid");
const crypto = __importStar(require("crypto"));
class PostgreSQLDocumentService {
    constructor() {
        this.client = new pg_1.Client({
            host: 'localhost',
            port: 5434,
            database: 'rag_knowledge',
            user: 'leitstand_dashboard',
            password: 'dashboard_password',
            connectionTimeoutMillis: 5000,
            query_timeout: 10000,
        });
        // Initialize database connection
        this.initializeDatabase().catch(error => {
            console.error('Failed to initialize PostgreSQLDocumentService:', error);
        });
    }
    /**
     * Initialize database connection
     */
    async initializeDatabase() {
        try {
            console.log('[PostgreSQLDocumentService] Attempting to connect to database...');
            await this.client.connect();
            console.log('[PostgreSQLDocumentService] Database connected successfully');
            // Test the connection
            const result = await this.client.query('SELECT 1 as test');
            console.log('[PostgreSQLDocumentService] Connection test successful:', result.rows[0]);
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Failed to connect to database:', error);
            throw error;
        }
    }
    /**
     * Store document in database
     */
    async storeDocument(request) {
        var _a;
        try {
            console.log(`[PostgreSQLDocumentService] Storing document: ${request.title}`);
            const id = (0, uuid_1.v4)();
            const now = new Date().toISOString();
            const contentHash = this.generateContentHash(request.content);
            // Prepare metadata
            const metadata = {
                ...request.metadata,
                uploadedAt: now,
                contentHash,
                knowledgeBaseId: request.knowledgeBaseId || 1
            };
            // Insert document using PostgreSQL
            await this.client.query(`INSERT INTO documents (id, title, content, source, source_path, content_type, language, created_at, updated_at, status)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`, [
                id,
                request.title,
                request.content,
                request.source || 'upload',
                request.source || '',
                request.contentType || 'text/plain',
                request.language || 'de',
                now,
                now,
                'indexed'
            ]);
            // Handle category relationship if provided
            if ((_a = request.metadata) === null || _a === void 0 ? void 0 : _a.category) {
                try {
                    await this.client.query(`INSERT INTO categories (id, name, description) VALUES ($1, $2, $3) ON CONFLICT (name) DO NOTHING`, [id + '_cat', request.metadata.category, '']);
                }
                catch (error) {
                    console.warn('Failed to associate document with category:', error);
                }
            }
            console.log(`[PostgreSQLDocumentService] Document stored successfully: ${id}`);
            return id;
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Error storing document:', error);
            throw error;
        }
    }
    /**
     * Store chunk in database
     */
    async storeChunk(documentId, content, index) {
        try {
            const chunkId = (0, uuid_1.v4)();
            const now = new Date().toISOString();
            const tokenCount = Math.ceil(content.length / 4); // Rough estimate
            await this.client.query(`INSERT INTO chunks (id, document_id, content, chunk_index, token_count, start_position, end_position, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`, [
                chunkId,
                documentId,
                content,
                index,
                tokenCount,
                0, // start_position - would need proper calculation
                content.length, // end_position
                now
            ]);
            return chunkId;
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Error storing chunk:', error);
            throw error;
        }
    }
    /**
     * Simple document chunking
     */
    chunkDocument(content, maxChunkSize = 500) {
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';
        for (const sentence of sentences) {
            if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
                chunks.push(currentChunk.trim());
                currentChunk = sentence.trim();
            }
            else {
                currentChunk += (currentChunk.length > 0 ? '. ' : '') + sentence.trim();
            }
        }
        if (currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
        }
        return chunks.length > 0 ? chunks : [content]; // Fallback to full content
    }
    /**
     * Get all documents
     */
    async getAllDocuments() {
        try {
            console.log('[PostgreSQLDocumentService] Getting all documents...');
            const result = await this.client.query(`SELECT
          d.id,
          d.title,
          d.source as filename,
          d.content_type,
          d.language,
          d.created_at,
          d.updated_at,
          LENGTH(d.content) as fileSize,
          COUNT(DISTINCT c.id) as chunksCount,
          COUNT(DISTINCT e.id) as embeddingsCount,
          COALESCE(cat.name, 'system') as category,
          cat.description
        FROM documents d
        LEFT JOIN chunks c ON d.id = c.document_id
        LEFT JOIN embeddings e ON c.id = e.chunk_id
        LEFT JOIN categories cat ON d.source = cat.name
        WHERE d.status = 'indexed'
        GROUP BY d.id, d.title, d.source, d.content_type, d.language, d.created_at, d.updated_at, cat.name, cat.description
        ORDER BY d.created_at DESC`);
            const documents = result.rows.map((row) => ({
                id: String(row.id),
                title: String(row.title),
                category: String(row.category) || 'system',
                description: String(row.description || ''),
                language: String(row.language),
                filename: String(row.filename),
                fileSize: Number(row.filesize),
                chunksCount: Number(row.chunkscount),
                embeddingsCount: Number(row.embeddingscount),
                createdAt: String(row.created_at),
                updatedAt: String(row.updated_at)
            }));
            console.log(`[PostgreSQLDocumentService] Retrieved ${documents.length} documents`);
            return documents;
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Error getting documents:', error);
            throw error;
        }
    }
    /**
     * Get chunks for a document
     */
    async getDocumentChunks(documentId) {
        try {
            const result = await this.client.query(`SELECT id, content, chunk_index
         FROM chunks
         WHERE document_id = $1
         ORDER BY chunk_index`, [documentId]);
            return result.rows.map((row) => ({
                id: String(row.id),
                content: String(row.content),
                chunk_index: Number(row.chunk_index)
            }));
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Error getting document chunks:', error);
            throw error;
        }
    }
    /**
     * Delete document chunks
     */
    async deleteDocumentChunks(documentId) {
        try {
            const result = await this.client.query('DELETE FROM chunks WHERE document_id = $1', [documentId]);
            return result.rowCount || 0;
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Error deleting document chunks:', error);
            throw error;
        }
    }
    /**
     * Delete document
     */
    async deleteDocument(documentId) {
        try {
            const result = await this.client.query('DELETE FROM documents WHERE id = $1', [documentId]);
            return (result.rowCount || 0) > 0;
        }
        catch (error) {
            console.error('[PostgreSQLDocumentService] Error deleting document:', error);
            throw error;
        }
    }
    /**
     * Generate content hash for deduplication
     */
    generateContentHash(content) {
        return crypto.createHash('sha256').update(content.trim()).digest('hex');
    }
    /**
     * Cleanup resources
     */
    async disconnect() {
        if (this.client) {
            await this.client.end();
        }
    }
}
exports.PostgreSQLDocumentService = PostgreSQLDocumentService;
exports.default = PostgreSQLDocumentService;
