/**
 * Interface für User-Repository
 */
export interface IUserRepository {
  /**
   * <PERSON>utzer nach E-Mail oder Benutzername suchen
   */
  findByEmailOrUsername(email: string, username: string): Promise<UserWithRoles | null>;

  /**
   * Benutzer nach E-Mail suchen
   */
  findByEmail(email: string): Promise<UserWithRoles | null>;

  /**
   * Benutzer nach Benutzername suchen
   */
  findByUsername(username: string): Promise<UserWithRoles | null>;

  /**
   * Benutzer nach ID suchen
   */
  findById(id: number): Promise<UserWithRoles | null>;

  /**
   * Neuen Benutzer erstellen
   */
  createUser(userData: UserCreateData): Promise<UserWithRoles>;

  /**
   * Benutzer aktualisieren
   */
  updateUser(id: number, userData: Partial<UserUpdateData>): Promise<UserWithRoles | null>;

  /**
   * <PERSON><PERSON><PERSON> l<PERSON>
   */
  deleteUser(id: number): Promise<boolean>;

  /**
   * Alle Benutzer abrufen
   */
  findAll(limit?: number, offset?: number): Promise<UserWithRoles[]>;

  /**
   * Benutzer nach Rolle filtern
   */
  findByRole(roleName: string): Promise<UserWithRoles[]>;
}

/**
 * Benutzer mit Rollen (vollständiges Benutzerobjekt)
 */
export interface UserWithRoles {
  id: number;
  email: string;
  username: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  passwordHash: string;
  createdAt: string;
  updatedAt: string;
  roles: UserRole[];
}

/**
 * Benutzer-Rolle
 */
export interface UserRole {
  id: number;
  name: string;
  description?: string;
  permissions?: string[];
}

/**
 * Daten für Benutzererstellung
 */
export interface UserCreateData {
  email: string;
  username: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  passwordHash: string;
}

/**
 * Daten für Benutzeraktualisierung
 */
export interface UserUpdateData {
  email?: string;
  username?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  passwordHash?: string;
}

/**
 * Filter-Interface für Benutzerabfragen
 */
export interface UserFilter {
  email?: string;
  username?: string;
  name?: string;
  role?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
}

/**
 * Authentifizierungsergebnis
 */
export interface AuthResult {
  user: UserWithRoles;
  success: boolean;
  message?: string;
}

/**
 * Passwort-Reset-Anfrage
 */
export interface PasswordResetRequest {
  email: string;
  resetToken: string;
  expiresAt: Date;
  used: boolean;
}
