"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bereitschaftsController = exports.BereitschaftsController = void 0;
const bereitschaftsService_1 = require("../services/bereitschaftsService");
const date_fns_1 = require("date-fns");
class BereitschaftsController {
    // Personen-Endpoints
    async getAllPersonen(req, res) {
        try {
            const personen = await bereitschaftsService_1.bereitschaftsService.getAllPersonen();
            res.json(personen);
        }
        catch (error) {
            console.error('Fehler beim Laden der Personen:', error);
            res.status(500).json({
                error: 'Personen konnten nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async getPersonById(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Person-ID' });
            }
            const person = await bereitschaftsService_1.bereitschaftsService.getPersonById(id);
            res.json(person);
        }
        catch (error) {
            console.error('Fehler beim Laden der Person:', error);
            if (error instanceof Error && error.message === 'Person nicht gefunden') {
                res.status(404).json({ error: error.message });
            }
            else {
                res.status(500).json({
                    error: 'Person konnte nicht geladen werden',
                    details: error instanceof Error ? error.message : 'Unbekannter Fehler'
                });
            }
        }
    }
    async createPerson(req, res) {
        try {
            const person = await bereitschaftsService_1.bereitschaftsService.createPerson(req.body);
            res.status(201).json(person);
        }
        catch (error) {
            console.error('Fehler beim Erstellen der Person:', error);
            res.status(400).json({
                error: 'Person konnte nicht erstellt werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async updatePerson(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Person-ID' });
            }
            const person = await bereitschaftsService_1.bereitschaftsService.updatePerson(id, req.body);
            res.json(person);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Person:', error);
            res.status(400).json({
                error: 'Person konnte nicht aktualisiert werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async deletePerson(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Person-ID' });
            }
            await bereitschaftsService_1.bereitschaftsService.deletePerson(id);
            res.status(204).send();
        }
        catch (error) {
            console.error('Fehler beim Löschen der Person:', error);
            res.status(400).json({
                error: 'Person konnte nicht gelöscht werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async updatePersonenReihenfolge(req, res) {
        try {
            console.log('[BEREITSCHAFTS] updatePersonenReihenfolge - Request body:', req.body);
            const { personenIds } = req.body;
            if (!personenIds) {
                console.log('[BEREITSCHAFTS] personenIds ist undefined');
                return res.status(400).json({ error: 'personenIds ist erforderlich' });
            }
            if (!Array.isArray(personenIds)) {
                console.log('[BEREITSCHAFTS] personenIds ist kein Array:', typeof personenIds, personenIds);
                return res.status(400).json({ error: 'personenIds muss ein Array sein' });
            }
            if (personenIds.length === 0) {
                console.log('[BEREITSCHAFTS] personenIds Array ist leer');
                return res.status(400).json({ error: 'personenIds Array darf nicht leer sein' });
            }
            console.log('[BEREITSCHAFTS] Aktualisiere Reihenfolge für IDs:', personenIds);
            const personen = await bereitschaftsService_1.bereitschaftsService.updatePersonenReihenfolge(personenIds);
            console.log('[BEREITSCHAFTS] Reihenfolge erfolgreich aktualisiert');
            res.json(personen);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Reihenfolge:', error);
            res.status(400).json({
                error: 'Reihenfolge konnte nicht aktualisiert werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    // Wochenplan-Endpoints
    async getWochenplan(req, res) {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const anzahlWochen = req.query.anzahlWochen ? parseInt(req.query.anzahlWochen) : 8;
            if (req.query.startDate && isNaN(startDate.getTime())) {
                return res.status(400).json({ error: 'Ungültiges Startdatum' });
            }
            if (isNaN(anzahlWochen) || anzahlWochen <= 0) {
                return res.status(400).json({ error: 'Ungültige Anzahl Wochen' });
            }
            const wochenplan = await bereitschaftsService_1.bereitschaftsService.getWochenplan(startDate, anzahlWochen);
            res.json(wochenplan);
        }
        catch (error) {
            console.error('Fehler beim Laden des Wochenplans:', error);
            res.status(500).json({
                error: 'Wochenplan konnte nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async getAllWochen(req, res) {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate) : undefined;
            const anzahlWochen = req.query.anzahlWochen ? parseInt(req.query.anzahlWochen) : 8;
            if (req.query.startDate && isNaN(startDate.getTime())) {
                return res.status(400).json({ error: 'Ungültiges Startdatum' });
            }
            if (isNaN(anzahlWochen) || anzahlWochen <= 0) {
                return res.status(400).json({ error: 'Ungültige Anzahl Wochen' });
            }
            const wochenplan = await bereitschaftsService_1.bereitschaftsService.getAllWochen(startDate, anzahlWochen);
            res.json(wochenplan);
        }
        catch (error) {
            console.error('Fehler beim Laden aller Wochen:', error);
            res.status(500).json({
                error: 'Alle Wochen konnten nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async getAktuelleBereitschaft(req, res) {
        try {
            const bereitschaft = await bereitschaftsService_1.bereitschaftsService.getAktuelleBereitschaft();
            res.json(bereitschaft);
        }
        catch (error) {
            console.error('Fehler beim Laden der aktuellen Bereitschaft:', error);
            res.status(500).json({
                error: 'Aktuelle Bereitschaft konnte nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async generiereWochenplan(req, res) {
        try {
            const { startDate, anzahlWochen } = req.body;
            if (!startDate) {
                return res.status(400).json({ error: 'Startdatum ist erforderlich' });
            }
            const start = new Date(startDate);
            if (isNaN(start.getTime())) {
                return res.status(400).json({ error: 'Ungültiges Startdatum' });
            }
            if (!anzahlWochen || isNaN(anzahlWochen) || anzahlWochen <= 0) {
                return res.status(400).json({ error: 'Ungültige Anzahl Wochen' });
            }
            const wochenplan = await bereitschaftsService_1.bereitschaftsService.generiereWochenplan(start, anzahlWochen);
            res.status(201).json(wochenplan);
        }
        catch (error) {
            console.error('Fehler beim Generieren des Wochenplans:', error);
            res.status(400).json({
                error: 'Wochenplan konnte nicht generiert werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async updateWoche(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Wochen-ID' });
            }
            const woche = await bereitschaftsService_1.bereitschaftsService.updateWoche(id, req.body);
            res.json(woche);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Woche:', error);
            res.status(400).json({
                error: 'Woche konnte nicht aktualisiert werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async deleteWoche(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Wochen-ID' });
            }
            await bereitschaftsService_1.bereitschaftsService.deleteWoche(id);
            res.status(204).send();
        }
        catch (error) {
            console.error('Fehler beim Löschen der Woche:', error);
            res.status(400).json({
                error: 'Woche konnte nicht gelöscht werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    // Ausnahmen-Endpoints
    async getAllAusnahmen(req, res) {
        try {
            const ausnahmen = await bereitschaftsService_1.bereitschaftsService.getAllAusnahmen();
            res.json(ausnahmen);
        }
        catch (error) {
            console.error('Fehler beim Laden der Ausnahmen:', error);
            res.status(500).json({
                error: 'Ausnahmen konnten nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async createAusnahme(req, res) {
        try {
            const ausnahme = await bereitschaftsService_1.bereitschaftsService.createAusnahme(req.body);
            res.status(201).json(ausnahme);
        }
        catch (error) {
            console.error('Fehler beim Erstellen der Ausnahme:', error);
            res.status(400).json({
                error: 'Ausnahme konnte nicht erstellt werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async updateAusnahme(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Ausnahme-ID' });
            }
            const ausnahme = await bereitschaftsService_1.bereitschaftsService.updateAusnahme(id, req.body);
            res.json(ausnahme);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Ausnahme:', error);
            res.status(400).json({
                error: 'Ausnahme konnte nicht aktualisiert werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async deleteAusnahme(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                return res.status(400).json({ error: 'Ungültige Ausnahme-ID' });
            }
            await bereitschaftsService_1.bereitschaftsService.deleteAusnahme(id);
            res.status(204).send();
        }
        catch (error) {
            console.error('Fehler beim Löschen der Ausnahme:', error);
            res.status(400).json({
                error: 'Ausnahme konnte nicht gelöscht werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    // Konfiguration-Endpoints
    async getKonfiguration(req, res) {
        try {
            const konfiguration = await bereitschaftsService_1.bereitschaftsService.getKonfiguration();
            res.json(konfiguration);
        }
        catch (error) {
            console.error('Fehler beim Laden der Konfiguration:', error);
            res.status(500).json({
                error: 'Konfiguration konnte nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async updateKonfiguration(req, res) {
        try {
            const konfiguration = await bereitschaftsService_1.bereitschaftsService.updateKonfiguration(req.body);
            res.json(konfiguration);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Konfiguration:', error);
            res.status(400).json({
                error: 'Konfiguration konnte nicht aktualisiert werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    // Erweiterte Endpoints
    async getBereitschaftsplanMitAusnahmen(req, res) {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date();
            const anzahlWochen = req.query.anzahlWochen ? parseInt(req.query.anzahlWochen) : 8;
            if (req.query.startDate && isNaN(startDate.getTime())) {
                return res.status(400).json({ error: 'Ungültiges Startdatum' });
            }
            const plan = await bereitschaftsService_1.bereitschaftsService.getBereitschaftsplanMitAusnahmen(startDate, anzahlWochen);
            res.json(plan);
        }
        catch (error) {
            console.error('Fehler beim Laden des Bereitschaftsplans mit Ausnahmen:', error);
            res.status(500).json({
                error: 'Bereitschaftsplan mit Ausnahmen konnte nicht geladen werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    async getBereitschaftsStatistiken(req, res) {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate) : (0, date_fns_1.addWeeks)(new Date(), -12);
            const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                return res.status(400).json({ error: 'Ungültige Datumsangaben' });
            }
            if (startDate >= endDate) {
                return res.status(400).json({ error: 'Startdatum muss vor Enddatum liegen' });
            }
            const statistiken = await bereitschaftsService_1.bereitschaftsService.getBereitschaftsStatistiken(startDate, endDate);
            res.json(statistiken);
        }
        catch (error) {
            console.error('Fehler beim Berechnen der Statistiken:', error);
            res.status(500).json({
                error: 'Statistiken konnten nicht berechnet werden',
                details: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
}
exports.BereitschaftsController = BereitschaftsController;
exports.bereitschaftsController = new BereitschaftsController();
