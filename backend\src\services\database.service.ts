/**
 * Zentrale Datenbankservice für das Backend
 * 
 * Dieser Service nutzt Drizzle ORM für Datenbankoperationen und stellt
 * eine einheitliche Schnittstelle für alle Datenbankzugriffe bereit.
 * 
 * V2: Erweitert um intelligentes Caching für Performance-Optimierung
 */

import { db } from '../db';
import { system, dispatchData, ablaengerei, we as wE, auslastung200, auslastung240, schnitte, maschinen, atrL as aTrL, ariL as aRiL, materialdaten, trommeldaten } from '../db/schema';
import { sql, eq, isNotNull, asc, and, gte, lte } from 'drizzle-orm';
import {
  ServiceLevelDataPoint,
  DailyPerformanceDataPoint,
  PickingDataPoint,
  ReturnsDataPoint,
  DeliveryPositionsDataPoint,
  TagesleistungDataPoint,
  AblaengereiDataPoint,
  WEDataPoint,
  Lagerauslastung200DataPoint,
  Lagerauslastung240DataPoint,
  ArilDataPoint,
  AtrlDataPoint,
  SystemFTSDataPoint
} from '../types/database.types';
import { getBackendCache, BackendCacheKeyGenerator } from './cache.service';

/**
 * Cache-TTL-Konfiguration für verschiedene Datentypen
 */
const CACHE_TTL = {
  // Statische/seltene Änderungen - Lange Cache-Zeit
  MACHINE_EFFICIENCY: 15 * 60 * 1000, // 15 Minuten
  SCHNITTE_DATA: 15 * 60 * 1000, // 15 Minuten
  
  // Moderate Änderungen - Mittlere Cache-Zeit  
  WAREHOUSE_DATA: 5 * 60 * 1000, // 5 Minuten
  ABLANGEREI_DATA: 5 * 60 * 1000, // 5 Minuten
  WE_DATA: 5 * 60 * 1000, // 5 Minuten
  
  // Häufige Änderungen - Kurze Cache-Zeit
  DISPATCH_DATA: 2 * 60 * 1000, // 2 Minuten
  PICKING_DATA: 2 * 60 * 1000, // 2 Minuten
  RETURNS_DATA: 2 * 60 * 1000, // 2 Minuten
  ARIL_ATRL_DATA: 3 * 60 * 1000, // 3 Minuten
  
  // Echtzeit-ähnliche Daten - Sehr kurze Cache-Zeit
  SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute
  DAILY_PERFORMANCE: 1 * 60 * 1000, // 1 Minute
  SYSTEM_FTS_DATA: 5 * 60 * 1000, // 5 Minuten - System-Verfügbarkeitsdaten
  SYSTEM_STATS: 30 * 1000 // 30 Sekunden
};

/**
 * Zentrale Datenbankservice-Klasse
 * 
 * Verwendet Drizzle ORM für alle Datenbankoperationen und bietet
 * eine einheitliche API für Frontend-Anfragen.
 * 
 * V2: Erweitert um intelligentes Caching für Performance-Optimierung
 */
export class DatabaseService {
  private cache = getBackendCache({
    defaultTTL: 5 * 60 * 1000, // 5 Minuten Standard
    maxMemoryMB: 100, // 100MB für Datenbankserver
    maxEntries: 2000, // Mehr Entries für Datenbank-Cache
    cleanupInterval: 2 * 60 * 1000, // 2 Minuten Cleanup
    enableLogging: process.env.NODE_ENV === 'development'
  });

  constructor() {
    try {
      console.log('Initialisiere Drizzle DB-Client mit Caching...');
      
      console.log('✅ Drizzle DB-Client mit Backend-Cache erfolgreich initialisiert');
    } catch (error) {
      console.error('Fehler bei der Initialisierung des DB-Clients:', error);
      throw error;
    }
  }

  /**
   * Initialisiert die Datenbankverbindung
   * Temporäre Lösung für Node.js v23 Kompatibilitätsproblem mit better-sqlite3
   */
  async connect(): Promise<boolean> {
    try {
      console.log('🔗 Stelle PostgreSQL-Datenbankverbindung her...');

      // TEMPORÄR DEAKTIVIERT: Teste Datenbankverbindung mit einfacher Query
      // const result = await db.select().from(system).limit(1);

      console.log('✅ PostgreSQL-Datenbankverbindung erfolgreich hergestellt (Test übersprungen)');
      // console.log(`📊 Gefundene System-Einträge: ${result.length}`);

      return true;
    } catch (error: unknown) {
      console.error('❌ Fehler beim Verbinden mit der Datenbank:');
      
      // Type Guard für Error-Objekte
      const isErrorWithMessage = (e: unknown): e is Error => 
        e !== null && typeof e === 'object' && 'message' in e;
        
      const isErrorWithCode = (e: unknown): e is NodeJS.ErrnoException => 
        isErrorWithMessage(e) && 'code' in e;
      
      // Fehlermeldung ausgeben
      if (isErrorWithMessage(error)) {
        console.error('Fehlermeldung:', error.message);
        
        // Spezifische Fehlercodes behandeln
        if (isErrorWithCode(error)) {
          switch (error.code) {
            case 'ENOENT':
              console.error('Die Datenbankdatei wurde nicht gefunden. Bitte überprüfen Sie den Pfad.');
              break;
            case 'EACCES':
              console.error('Keine Berechtigung zum Zugriff auf die Datenbank. Bitte überprüfen Sie die Dateiberechtigungen.');
              break;
            case 'SQLITE_CORRUPT':
              console.error('Die Datenbankdatei ist beschädigt. Bitte führen Sie eine Sicherung wiederher.');
              break;
            default:
              console.error('Fehlercode:', error.code);
          }
        }
        
        // Stack Trace ausgeben, falls verfügbar
        if (error.stack) {
          console.error('Stack Trace:', error.stack);
        }
      } else {
        console.error('Unbekannter Fehler aufgetreten:', String(error));
      }
      
      return false;
    }
  }

  /**
   * Schließt die Datenbankverbindung und beendet Cache
   */
  async disconnect(): Promise<void> {
    this.cache.destroy();
  }

  /**
   * Cache für bestimmte Datentypen invalidieren
   * @param dataTypes Array von Datentypen zum Invalidieren
   */
  invalidateCache(dataTypes: string[] = ['dispatch', 'warehouse', 'cutting']): number {
    return this.cache.invalidateByDataTypes(dataTypes);
  }

  /**
   * Kompletten Cache leeren (für Development/Testing)
   */
  clearAllCache(): void {
    this.cache.destroy();
    // Cache wird automatisch neu initialisiert beim nächsten Zugriff
  }

  /**
   * Ruft die Servicelevel-Daten für das Diagramm ab
   */
  async getServiceLevelData(): Promise<ServiceLevelDataPoint[]> {
    try {
      const result = await db.select({
        datum: dispatchData.datum,
        servicegrad: dispatchData.servicegrad,
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.servicegrad)
        )
      )
      .orderBy(asc(dispatchData.datum));

      return result.map((item: any) => ({
        datum: item.datum!,
        servicegrad: item.servicegrad || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der ServiceLevel-Daten:', error);
      throw error;
    }
  }

  /**
   * Ruft die täglichen Leistungsdaten für das Diagramm ab
   */
  async getDailyPerformanceData(): Promise<DailyPerformanceDataPoint[]> {
    try {
      const result = await db.select({
        datum: dispatchData.datum,
        ausgeliefertLup: dispatchData.ausgeliefert_lup,
        produzierteTonnagen: dispatchData.produzierte_tonnagen,
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.ausgeliefert_lup)
        )
      )
      .orderBy(asc(dispatchData.datum));

      return result.map((item: any) => ({
        datum: item.datum!,
        value: item.ausgeliefertLup || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der täglichen Leistungsdaten:', error);
      throw error;
    }
  }

  /**
   * Ruft die Kommissionierungsdaten für das Diagramm ab
   */
  async getPickingData(): Promise<PickingDataPoint[]> {
    try {
      const result = await db.select({
        datum: dispatchData.datum,
        atrl: dispatchData.atrl,
        aril: dispatchData.aril,
        fuellgrad_aril: dispatchData.fuellgrad_aril,
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.atrl),
          isNotNull(dispatchData.aril),
          isNotNull(dispatchData.fuellgrad_aril)
        )
      )
      .orderBy(asc(dispatchData.datum));

      return result.map((item: any) => ({
        date: item.datum!,
        atrl: item.atrl || 0,
        aril: item.aril || 0,
        fuellgrad_aril: item.fuellgrad_aril || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Kommissionierungsdaten:', error);
      throw error;
    }
  }

  /**
   * Ruft die Retourendaten für das Diagramm ab
   */
  async getReturnsData(): Promise<ReturnsDataPoint[]> {
    try {
      const result = await db.select({
        datum: dispatchData.datum,
        qmAngenommen: dispatchData.qm_angenommen,
        qmAbgelehnt: dispatchData.qm_abgelehnt,
        qmOffen: dispatchData.qm_offen,
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.qm_angenommen)
        )
      )
      .orderBy(asc(dispatchData.datum));

      return result.map((item: any) => ({
        name: item.datum!,
        value: (item.qmAngenommen || 0) + (item.qmAbgelehnt || 0) + (item.qmOffen || 0),
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Retourendaten:', error);
      throw error;
    }
  }

  /**
   * Ruft die Lieferpositionsdaten für das Diagramm ab
   */
  async getDeliveryPositionsData(): Promise<DeliveryPositionsDataPoint[]> {
    try {
      const result = await db.select({
        datum: dispatchData.datum,
        ausgeliefert_lup: dispatchData.ausgeliefert_lup,
        rueckstaendig: dispatchData.rueckstaendig,
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.ausgeliefert_lup),
          isNotNull(dispatchData.rueckstaendig)
        )
      )
      .orderBy(asc(dispatchData.datum));

      return result.map((item: any) => ({
        date: item.datum!,
        ausgeliefert_lup: item.ausgeliefert_lup || 0,
        rueckstaendig: item.rueckstaendig || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Lieferpositionsdaten:', error);
      throw error;
    }
  }

  /**
   * Ruft die Tagesleistungsdaten für das Diagramm ab
   */
  async getTagesleistungData(): Promise<TagesleistungDataPoint[]> {
    try {
      const result = await db.select({
        datum: dispatchData.datum,
        produzierte_tonnagen: dispatchData.produzierte_tonnagen,
        direktverladung_kiaa: dispatchData.direktverladung_kiaa,
        umschlag: dispatchData.umschlag,
        kg_pro_colli: dispatchData.kg_pro_colli,
        elefanten: dispatchData.elefanten,
      })
      .from(dispatchData)
      .where(
        and(
          isNotNull(dispatchData.datum),
          isNotNull(dispatchData.produzierte_tonnagen)
        )
      )
      .orderBy(asc(dispatchData.datum));

      return result.map((item: any) => ({
        date: item.datum!,
        produzierte_tonnagen: item.produzierte_tonnagen || 0,
        direktverladung_kiaa: item.direktverladung_kiaa || 0,
        umschlag: item.umschlag || 0,
        kg_pro_colli: item.kg_pro_colli || 0,
        elefanten: item.elefanten || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Tagesleistungsdaten:', error);
      throw error;
    }
  }

  /**
   * Importiert die Daten aus der Abl.csv-Datei in die Ablaengerei-Tabelle
   */
  async importAblaengereiCsvData(): Promise<boolean> {
    try {
      // CSV-Import-Logik mit Drizzle implementieren
      // Vorläufig: Erfolg zurückgeben
      return true;
    } catch (error) {
      console.error('Fehler beim Importieren der Ablaengerei-CSV-Daten:', error);
      return false;
    }
  }

  /**
   * Ruft die Daten aus der Ablaengerei-Tabelle ab
   */
  async getAblaengereiData(): Promise<AblaengereiDataPoint[]> {
    try {
      const result = await db.select()
      .from(ablaengerei)
      .orderBy(asc(ablaengerei.Datum));

      return result.map((item: any) => ({
        id: item.id,
        datum: item.datum || '',
        cutTT: item.cutTt || 0,
        cutTR: item.cutTr || 0,
        cutRR: item.cutRr || 0,
        pickCut: item.pickCut || 0,
        lagerCut220: item.lagerCut220 || 0,
        lagerCut240: item.lagerCut240 || 0,
        lagerCut200: item.lagerCut200 || 0,
        cutLagerK200: item.cutLagerK200 || 0,
        cutLagerK240: item.cutLagerK240 || 0,
        cutLagerK220: item.cutLagerK220 || 0,
        cutLager200: item.cutLager200 || 0,
        cutLagerR240: item.cutLagerR240 || 0,
        cutLagerR220: item.cutLagerR220 || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Ablaengerei-Daten:', error);
      throw error;
    }
  }

  /**
   * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
   */
  async getWEData(): Promise<WEDataPoint[]> {
    try {
      const result = await db.select()
      .from(wE)
      .orderBy(asc(wE.datum));

      return result.map((item: any) => ({
        id: item.id,
        datum: item.datum || '',
        weAtrl: item.weAtrl || 0,
        weManl: item.weManl || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der WE-Daten:', error);
      throw error;
    }
  }

  /**
   * Ruft die Lagerauslastung200-Daten für das Diagramm ab (mit Caching)
   * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
   * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
   */
  async getLagerauslastung200Data(startDate?: string, endDate?: string): Promise<Lagerauslastung200DataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forService('DatabaseService', 'getLagerauslastung200Data', {
      startDate,
      endDate
    });
    
    return await this.cache.cachedQuery(
      cacheKey,
      async () => {
        try {
          
          // Daten mit Drizzle abfragen
          const baseQuery = db.select({
            aufnahmeDatum: auslastung200.aufnahmeDatum,
            auslastungA: auslastung200.auslastungA,
            auslastungB: auslastung200.auslastungB,
            auslastungC: auslastung200.auslastungC
          }).from(auslastung200);
          
          let results;
          // Datumsfilter hinzufügen, wenn vorhanden
          if (startDate && endDate) {
            results = await baseQuery.where(
              and(
                gte(auslastung200.aufnahmeDatum, startDate),
                lte(auslastung200.aufnahmeDatum, endDate)
              )
            ).orderBy(asc(auslastung200.aufnahmeDatum));
          } else if (startDate) {
            results = await baseQuery.where(
              gte(auslastung200.aufnahmeDatum, startDate)
            ).orderBy(asc(auslastung200.aufnahmeDatum));
          } else if (endDate) {
            results = await baseQuery.where(
              lte(auslastung200.aufnahmeDatum, endDate)
            ).orderBy(asc(auslastung200.aufnahmeDatum));
          } else {
            results = await baseQuery.orderBy(asc(auslastung200.aufnahmeDatum));
          }
          
          // Daten in das erwartete Format umwandeln und Gesamt-Wert berechnen
          const mappedResults = results.map((item: any) => {
            // Auslastungswerte als Dezimalzahl (0-1) parsen
            const auslastungA = parseFloat(item.auslastungA || '0');
            const auslastungB = parseFloat(item.auslastungB || '0');
            const auslastungC = parseFloat(item.auslastungC || '0');
            
            // Gesamtdurchschnitt berechnen (als Dezimalzahl belassen)
            const gesamt = (auslastungA + auslastungB + auslastungC) / 3;
            
            return {
              aufnahmeDatum: item.aufnahmeDatum || '',
              auslastungA,
              auslastungB,
              auslastungC,
              gesamt
            };
          });
          
          return mappedResults;
        } catch (error) {
          console.error('Fehler beim Abrufen der Lagerauslastung200-Daten:', error);
          throw error;
        }
      },
      CACHE_TTL.WAREHOUSE_DATA
    );
  }

  /**
   * Ruft die Lagerauslastung240-Daten für das Diagramm ab
   * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
   * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
   */
  async getLagerauslastung240Data(startDate?: string, endDate?: string): Promise<Lagerauslastung240DataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forService('DatabaseService', 'getLagerauslastung240Data', {
      startDate,
      endDate
    });

    return await this.cache.cachedQuery(
      cacheKey,
      async () => {
        try {
          
          // Daten mit Drizzle abfragen
          const baseQuery = db.select({
            aufnahmeDatum: auslastung240.aufnahmeDatum,
            auslastungA: auslastung240.auslastungA,
            auslastungB: auslastung240.auslastungB,
            auslastungC: auslastung240.auslastungC
          }).from(auslastung240);
          
          let results;
          // Datumsfilter hinzufügen, wenn vorhanden
          if (startDate && endDate) {
            results = await baseQuery.where(
              and(
                gte(auslastung240.aufnahmeDatum, startDate),
                lte(auslastung240.aufnahmeDatum, endDate)
              )
            ).orderBy(asc(auslastung240.aufnahmeDatum));
          } else if (startDate) {
            results = await baseQuery.where(
              gte(auslastung240.aufnahmeDatum, startDate)
            ).orderBy(asc(auslastung240.aufnahmeDatum));
          } else if (endDate) {
            results = await baseQuery.where(
              lte(auslastung240.aufnahmeDatum, endDate)
            ).orderBy(asc(auslastung240.aufnahmeDatum));
          } else {
            results = await baseQuery.orderBy(asc(auslastung240.aufnahmeDatum));
          }
          
          // Daten in das erwartete Format umwandeln und Gesamt-Wert berechnen
          const mappedResults = results.map((item: any) => {
            // Auslastungswerte als Dezimalzahl (0-1) parsen
            const auslastungA = parseFloat(item.auslastungA || '0');
            const auslastungB = parseFloat(item.auslastungB || '0');
            const auslastungC = parseFloat(item.auslastungC || '0');
            
            // Gesamtdurchschnitt berechnen (als Dezimalzahl belassen)
            const gesamt = (auslastungA + auslastungB + auslastungC) / 3;
            
            return {
              aufnahmeDatum: item.aufnahmeDatum || '',
              auslastungA,
              auslastungB,
              auslastungC,
              gesamt
            };
          });
      
          return mappedResults;
        } catch (error) {
          console.error('Fehler beim Abrufen der Lagerauslastung240-Daten:', error);
          throw error;
        }
      },
      CACHE_TTL.WAREHOUSE_DATA
    );
  }

  async getSchnitteData(): Promise<any[]> {
    try {
      return await db.select().from(schnitte);
    } catch (error) {
      console.error('Fehler beim Abrufen der Schnitte-Daten:', error);
      throw error;
    }
  }
  async getAtrlData(startDate?: string, endDate?: string): Promise<AtrlDataPoint[]> {
      try {
  
        const baseQuery = db.select().from(aTrL);
        
        let result;
        if (startDate && endDate) {
          result = await baseQuery.where(
            and(
              gte(aTrL.Datum, startDate),
              lte(aTrL.Datum, endDate)
            )
          ).orderBy(asc(aTrL.Datum));
        } else if (startDate) {
          result = await baseQuery.where(
            gte(aTrL.Datum, startDate)
          ).orderBy(asc(aTrL.Datum));
        } else if (endDate) {
          result = await baseQuery.where(
            lte(aTrL.Datum, endDate)
          ).orderBy(asc(aTrL.Datum));
        } else {
          result = await baseQuery.orderBy(asc(aTrL.Datum));
        }
  
        return result.map((item: any) => ({
          Datum: item.datum ? new Date(item.datum).toISOString().split('T')[0] : '',
          weAtrl: item.weAtrl || 0,
          waTaPositionen: item.waTaPositionen || 0,
          EinlagerungAblKunde: item.einlagerungAblKunde || 0,
          EinlagerungAblRest: item.einlagerungAblRest || 0,
          umlagerungen: item.umlagerungen || 0,
          AuslagerungAbl: item.auslagerungAbl || 0,
        }));
    } catch (error) {
      console.error('Fehler beim Abrufen der ARiL-Daten:', error);
      throw error;
    }
  }
  async getArilData(startDate?: string, endDate?: string): Promise<ArilDataPoint[]> {
    try {
  
      const baseQuery = db.select().from(aRiL);
      
      let result;
      if (startDate && endDate) {
        result = await baseQuery.where(
          and(
            gte(aRiL.Datum, startDate),
            lte(aRiL.Datum, endDate)
          )
        ).orderBy(asc(aRiL.Datum));
      } else if (startDate) {
        result = await baseQuery.where(
          gte(aRiL.Datum, startDate)
        ).orderBy(asc(aRiL.Datum));
      } else if (endDate) {
        result = await baseQuery.where(
          lte(aRiL.Datum, endDate)
        ).orderBy(asc(aRiL.Datum));
      } else {
        result = await baseQuery.orderBy(asc(aRiL.Datum));
      }
  
      return result.map((item: any) => ({
        Datum: item.datum ? new Date(item.datum).toISOString().split('T')[0] : '',
        waTaPositionen: item.waTaPositionen || 0,
        cuttingLagerKunde: item.cuttingLagerKunde || 0,
        cuttingLagerRest: item.cuttingLagerRest || 0,
        Umlagerungen: item.Umlagerungen || 0,
        lagerCutting: item.lagerCutting || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der ARiL-Daten:', error);
      throw error;
    }
  }

  async getMaschinenEfficiency(): Promise<any[]> {
    try {
      const schnitteData = await db.select().from(schnitte);
      const maschinenData = await db.select().from(maschinen);

      const maschinenMap = new Map(maschinenData.map((m: any) => [m.machine, m.schnitteProStd]));

      const efficiencyData = schnitteData.flatMap((s: any) => {
        if (!s.datum) return [];
        return Object.entries(s)
          .map(([key, value]) => {
            if (key.startsWith('m') && typeof value === 'number') {
            // Konvertiere Drizzle-Key (z.B. m5RH1) zu Maschinen-Key (z.B. M5-R-H1)
              const machineKey = key.replace(/([a-z])([A-Z])/g, '$1-$2').replace(/([0-9])([A-Z])/g, '$1-$2').toUpperCase();
              const sollSchnitte = maschinenMap.get(machineKey);
              if (sollSchnitte === undefined || sollSchnitte === null) {
                console.log(`Warnung: Keine Soll-Schnitte für Maschine ${machineKey} gefunden`);
                return null; // Überspringe Maschinen ohne Soll-Werte
              }
              // Explizite Typzuweisung für TypeScript
              const sollSchnitteWert: number = sollSchnitte as number;
              const tagesSchnitte = value;
              // Annahme: Ein Standard-Arbeitstag hat 21 Stunden.
              const standardArbeitsstunden = 21;
              
              // Berechne die Soll-Schnitte für einen Standard-Arbeitstag.
              const sollSchnitteProTag = sollSchnitteWert * standardArbeitsstunden;
              
              // Die Effizienz vergleicht die tatsächlichen Tagesschnitte mit den Soll-Tagesschnitten.
              // Ein Wert > 100% bedeutet, dass mehr als das 21-Stunden-Soll produziert wurde,
              // was z.B. durch längere Laufzeiten (Mehrschichtbetrieb) zustande kommen kann.
              // Da wir oben bereits null-Werte ausfiltern, ist sollSchnitte hier immer definiert
              const effizienzProzent = (tagesSchnitte / sollSchnitteProTag) * 100;

              // Die "Ist-Schnitte pro Stunde" sind eine abgeleitete Metrik basierend auf der 21-Stunden-Annahme.
              const istSchnitteProStunde = tagesSchnitte / standardArbeitsstunden;
              
              return {
                Datum: s.datum,
                Machine: machineKey, // Verwende den korrigierten Key
                sollSchnitte: sollSchnitteWert,
                tagesSchnitte,
                istSchnitteProStunde,
                effizienzProzent,
              };
            }
            return null;
          })
          .filter((item): item is NonNullable<typeof item> => item !== null);
      });

      return efficiencyData;
    } catch (error) {
      console.error('Fehler beim Abrufen der Maschinen-Effizienz-Daten:', error);
      throw error;
    }
  }

  async getCuttingChartData(): Promise<any[]> {
    try {
      const result = await db.select({
        datum: ablaengerei.Datum,
        cutTT: ablaengerei.cutTT,
        cutTR: ablaengerei.cutTR,
        cutRR: ablaengerei.cutRR,
        pickCut: ablaengerei.pickCut,
      })
      .from(ablaengerei)
      .orderBy(asc(ablaengerei.Datum));

      return result.map((item: any) => ({
        name: item.datum,
        date: item.datum,
        cutTT: item.cutTT || 0,
        cutTR: item.cutTR || 0,
        cutRR: item.cutRR || 0,
        pickCut: item.pickCut || 0,
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Cutting-Chart-Daten:', error);
      throw error;
    }
  }

    async getLagerCutsChartData(): Promise<any[]> {
    try {
      const result = await db.select({
        datum: ablaengerei.Datum,
        lagerCut200: ablaengerei.lagerCut200,
        lagerCut220: ablaengerei.lagerCut220,
        lagerCut240: ablaengerei.lagerCut240,
        cutLagerK200: ablaengerei.cutLagerK200,
        cutLagerK220: ablaengerei.cutLagerK220,
        cutLagerK240: ablaengerei.cutLagerK240,
        cutLagerR220: ablaengerei.cutLagerR220,
        cutLagerR240: ablaengerei.cutLagerR240,
      })
      .from(ablaengerei)
      .orderBy(asc(ablaengerei.Datum));

      return result.map((item: any) => ({
        name: item.datum,
        date: item.datum,
        lagerSumme: (item.lagerCut200 || 0) + (item.lagerCut220 || 0) + (item.lagerCut240 || 0),
        cutLagerKSumme: (item.cutLagerK200 || 0) + (item.cutLagerK220 || 0) + (item.cutLagerK240 || 0),
        cutLagerRSumme: (item.cutLagerR220 || 0) + (item.cutLagerR240 || 0),
      }));
    } catch (error) {
      console.error('Fehler beim Abrufen der Lager-Cuts-Chart-Daten:', error);
      throw error;
    }
  }

  /**
   * System FTS Verfügbarkeitsdaten mit Cache-Support und Datums-Filterung
   */
  async getSystemFTSData(dateRange?: { startDate?: string; endDate?: string }): Promise<SystemFTSDataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forService('DatabaseService', 'getSystemFTSData', {
      startDate: dateRange?.startDate,
      endDate: dateRange?.endDate
    });
    
    return await this.cache.cachedQuery(
      cacheKey,
      async () => {
        try {
          console.log('System FTS Daten aus Datenbank laden...');


          const baseQuery = db.select({
            id: system.id,
            Datum: system.datum,
            verfuegbarkeitFTS: system.verfuegbarkeitFts,
          }).from(system);
          
          let result;
          // Baue Drizzle where-Kondition für Datums-Filterung (String-Format: YYYY-MM-DD zu Date konvertieren)
          if (dateRange?.startDate && dateRange?.endDate) {
            const startDate = new Date(dateRange.startDate);
            const endDate = new Date(dateRange.endDate);
            result = await baseQuery.where(
              and(
                gte(system.datum, startDate),
                lte(system.datum, endDate)
              )
            ).orderBy(asc(system.datum));
          } else if (dateRange?.startDate) {
            const startDate = new Date(dateRange.startDate);
            result = await baseQuery.where(
              gte(system.datum, startDate)
            ).orderBy(asc(system.datum));
          } else if (dateRange?.endDate) {
            const endDate = new Date(dateRange.endDate);
            result = await baseQuery.where(
              lte(system.datum, endDate)
            ).orderBy(asc(system.datum));
          } else {
            result = await baseQuery.orderBy(asc(system.datum));
          }

          // Transformiere Drizzle-Daten zu SystemFTSDataPoint
          const transformedData: SystemFTSDataPoint[] = result
            .filter((item: any) => item.verfuegbarkeitFTS !== null) // Nur Datensätze mit FTS-Werten
            .map((item: any) => ({
              id: item.id,
              Datum: item.Datum || '', // Datum ist bereits String im YYYY-MM-DD Format
              verfuegbarkeitFTS: item.verfuegbarkeitFTS || 0,
            }));

          console.log(`✅ ${transformedData.length} System FTS Datensätze aus Datenbank geladen`);
          return transformedData;
        } catch (error) {
          console.error('Fehler beim Abrufen der System FTS Daten:', error);
          throw error;
        }
      },
      CACHE_TTL.SYSTEM_FTS_DATA
    );
  }

  /**
   * Materialdaten aus der Datenbank abrufen
   * Lädt alle verfügbaren Materialdaten mit MATNR, Materialkurztext und Kabeldurchmesser
   */
  async getMaterialdaten(): Promise<any[]> {
    return this.cache.cachedQuery(
      BackendCacheKeyGenerator.forQuery('materialdaten', 'getAll'),
      async () => {
        try {
          console.log('🔍 Lade Materialdaten aus der Datenbank...');
          
          // Verwende Drizzle für Materialdaten
          const result = await db.select({
            matnr: materialdaten.matnr,
            materialkurztext: materialdaten.materialkurztext,
            kabeldurchmesser: materialdaten.kabeldurchmesser,
            zuschlagKabeldurchmesser: materialdaten.zuschlagKabeldurchmesser,
            biegefaktor: materialdaten.biegefaktor,
            kleinsterErlauberFreiraum: materialdaten.kleinsterErlauberFreiraum,
            bruttogewicht: materialdaten.bruttogewicht,
            created_at: materialdaten.created_at,
            updated_at: materialdaten.updated_at
          })
          .from(materialdaten)
          .orderBy(asc(materialdaten.matnr));

          // Daten für Frontend-Kompatibilität transformieren
          const transformedData = result.map((item: any) => ({
            MATNR: item.matnr,
            Materialkurztext: item.materialkurztext || '',
            Kabeldurchmesser: item.kabeldurchmesser || 0,
            ZuschlagKabeldurchmesser: item.zuschlagKabeldurchmesser || 0,
            Biegefaktor: item.biegefaktor || 0,
            KleinsterErlauberFreiraum: item.kleinsterErlauberFreiraum || 0,
            Bruttogewicht: item.bruttogewicht || 0
          }));

          console.log(`✅ ${transformedData.length} Materialdaten aus Datenbank geladen`);
          return transformedData;
        } catch (error) {
          console.error('Fehler beim Abrufen der Materialdaten:', error);
          throw error;
        }
      },
      CACHE_TTL.WAREHOUSE_DATA // 5 Minuten Cache für Stammdaten
    );
  }

  /**
   * Trommeldaten aus der Datenbank abrufen
   * Lädt alle verfügbaren Trommeldaten mit Trommelname und Außendurchmesser
   */
  async getTrommeldaten(): Promise<any[]> {
    return this.cache.cachedQuery(
      BackendCacheKeyGenerator.forQuery('trommeldaten', 'getAll'),
      async () => {
        try {
          console.log('🔍 Lade Trommeldaten aus der Datenbank...');
          
          // Verwende Drizzle für Trommeldaten
          const result = await db.select({
            trommelname: trommeldaten.trommeldaten,
            aussendurchmesser: trommeldaten.aussendurchmesser,
            kerndurchmesser: trommeldaten.kerndurchmesser,
            // Hinweis: freiraum_mm, wickelbreite_mm, maxTragkraft_Kg, max_Laenge, max_Gewicht, created_at und updated_at
            // existieren nicht in der trommeldaten Tabelle laut Schema
          })
          .from(trommeldaten)
          .orderBy(asc(trommeldaten.trommeldaten));

          // Daten für Frontend-Kompatibilität transformieren
          const transformedData = result.map((item: any) => ({
            Trommelname: item.trommelname,
            Außendurchmesser: item.aussendurchmesser || 0,
            Kerndurchmesser: item.kerndurchmesser || 0,
            Freiraum_mm: item.freiraum_mm || 0,
            Wickelbreite_mm: item.wickelbreite_mm || 0,
            MaxTragkraft_Kg: item.maxTragkraft_Kg || 0,
            Max_Laenge: item.max_Laenge || 0,
            Max_Gewicht: item.max_Gewicht || 0
          }));

          console.log(`✅ ${transformedData.length} Trommeldaten aus Datenbank geladen`);
          return transformedData;
        } catch (error) {
          console.error('Fehler beim Abrufen der Trommeldaten:', error);
          throw error;
        }
      },
      CACHE_TTL.WAREHOUSE_DATA // 5 Minuten Cache für Stammdaten
    );
  }

  /**
   * Service Health Status für Frontend API
   */
  async getServiceHealth(): Promise<any> {
    try {
      const startTime = Date.now();

      // TEMPORÄR DEAKTIVIERT: Test database connections
      // await db.select().from(system).limit(1);

      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime,
        cache: this.getCacheStats(),
        databases: ['sfm_dashboard'],
        version: 'drizzle-v1.0.0'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        version: 'drizzle-v1.0.0'
      };
    }
  }

  /**
   * System-Statistiken für Frontend API
   */
  async getSystemStats(): Promise<any> {
    return this.cache.cachedQuery(
      BackendCacheKeyGenerator.forService('DatabaseService', 'getSystemStats'),
      async () => {
        try {
          // Grundlegende Systemstatistiken
          const [systemCount, dispatchCount, stoerungenCount] = await Promise.all([
            db.select({ count: sql<number>`count(*)` }).from(system),
            db.select({ count: sql<number>`count(*)` }).from(dispatchData),
            db.select({ count: sql<number>`count(*)` }).from(system) // Placeholder
          ]);

          return {
            tables: {
              system: systemCount[0]?.count || 0,
              dispatchData: dispatchCount[0]?.count || 0,
              stoerungen: stoerungenCount[0]?.count || 0
            },
            timestamp: new Date().toISOString()
          };
        } catch (error) {
          console.error('Fehler beim Abrufen der Systemstatistiken:', error);
          throw error;
        }
      },
      CACHE_TTL.SYSTEM_FTS_DATA
    );
  }

  /**
   * Cache-Statistiken für Frontend API
   */
  getCacheStats(): any {
    return this.cache.getStats();
  }

  /**
   * Knowledge Bases für Frontend API (RAG Database)
   */
  async getKnowledgeBases(): Promise<any[]> {
    // Placeholder - RAG functionality would be implemented here
    return [];
  }
}
