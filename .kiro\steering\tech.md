# Technology Stack

## Core Framework
- **Electron** - Desktop application framework
- **Vite** - Build tool and dev server
- **TypeScript** - Primary language
- **React 18.2.0** - UI framework with JSX runtime

## UI & Styling
- **Tailwind CSS v4** - Utility-first CSS framework
- **shadcn/ui** - Component library built on Radix UI
- **Neobrutalism Design** - Bold colors, strong borders (4px shadows, 5px border radius)
- **Framer Motion** - Animations and transitions
- **Lucide React** - Icon library

## State Management & Data
- **TanStack Query** - Server state management
- **TanStack Router** - Type-safe routing
- **better-sqlite3** - SQLite database interface
- **Prisma** - Database ORM (backend)

## Charts & Visualization
- **Recharts** - Chart library for data visualization
- **React Table** - Data table management

## Development Tools
- **Electron Forge** - Packaging and distribution
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Vitest** - Unit testing
- **Playwright** - E2E testing

## Common Commands

### Development
```bash
pnpm start          # Start development Frontend server inc. backend
pnpm run dev           # Alias for start
pnpm run dev:frontend  # Frontend only
pnpm run dev:backend   # Backend only
```

### Building & Packaging
```bash
pnpm run package                    # Package app
pnpm run make                      # Create distributables
pnpm run build:portable           # Create portable Windows build
pnpm run build:portable-complete  # Full portable build
```

### Testing
```bash
pnpm run test:unit     # Run unit tests
pnpm run test:e2e      # Run E2E tests
pnpm run test:all      # Run all tests
pnpm run test:watch    # Watch mode for unit tests
```

### Code Quality
```bash
pnpm run lint          # Run ESLint
pnpm run format        # Run Prettier
```

## Platform Support
- Primary: Windows (win32)
- Build targets: Portable executables, installers
- Shell: PowerShell/CMD compatibility required