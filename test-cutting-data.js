/**
 * Test-Script für Cutting-Daten
 * Überprüft die Ablaengerei-Tabelle und Cutting-Chart-Daten
 */

const axios = require('axios');

// Backend API Base URL
const API_BASE_URL = 'http://localhost:3001/api';

async function testCuttingData() {
  console.log('🔍 Teste Cutting-Daten...\n');

  try {
    // 1. Test ohne Datumsfilter
    console.log('1️⃣ Teste Cutting-Chart-Daten ohne Datumsfilter...');
    const response1 = await axios.get(`${API_BASE_URL}/database/cutting-chart-data`);
    console.log('✅ Cutting-Chart-Antwort erhalten:', {
      status: response1.status,
      dataLength: response1.data?.data?.length || 0,
      firstFew: response1.data?.data?.slice(0, 3) || []
    });

    // 2. Test mit dem Zeitraum aus der CuttingPage (2025-04-30 bis 2025-05-29)
    console.log('\n2️⃣ Teste Cutting-Chart-Daten mit Zeitraum 2025-04-30 bis 2025-05-29...');
    const response2 = await axios.get(`${API_BASE_URL}/database/cutting-chart-data`, {
      params: {
        startDate: '2025-04-30',
        endDate: '2025-05-29'
      }
    });
    console.log('✅ Cutting-Chart-Antwort erhalten:', {
      status: response2.status,
      dataLength: response2.data?.data?.length || 0,
      firstFew: response2.data?.data?.slice(0, 3) || []
    });

    // 3. Test Lager-Cuts-Chart-Daten
    console.log('\n3️⃣ Teste Lager-Cuts-Chart-Daten mit Zeitraum 2025-04-30 bis 2025-05-29...');
    const response3 = await axios.get(`${API_BASE_URL}/database/lager-cuts-chart-data`, {
      params: {
        startDate: '2025-04-30',
        endDate: '2025-05-29'
      }
    });
    console.log('✅ Lager-Cuts-Chart-Antwort erhalten:', {
      status: response3.status,
      dataLength: response3.data?.data?.length || 0,
      firstFew: response3.data?.data?.slice(0, 3) || []
    });

    // 4. Test Schnitte-Daten
    console.log('\n4️⃣ Teste Schnitte-Daten...');
    const response4 = await axios.get(`${API_BASE_URL}/database/schnitte-data`);
    console.log('✅ Schnitte-Daten-Antwort erhalten:', {
      status: response4.status,
      dataLength: response4.data?.data?.length || 0,
      firstFew: response4.data?.data?.slice(0, 3) || []
    });

    // 5. Test Ablaengerei-Daten direkt
    console.log('\n5️⃣ Teste Ablaengerei-Daten direkt...');
    const response5 = await axios.get(`${API_BASE_URL}/database/ablaengerei`, {
      params: {
        startDate: '2025-04-30',
        endDate: '2025-05-29'
      }
    });
    console.log('✅ Ablaengerei-Daten-Antwort erhalten:', {
      status: response5.status,
      dataLength: response5.data?.data?.length || 0,
      firstFew: response5.data?.data?.slice(0, 3) || []
    });

    // 6. Test mit aktuellem Datum
    console.log('\n6️⃣ Teste mit aktuellem Datum...');
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const response6 = await axios.get(`${API_BASE_URL}/database/cutting-chart-data`, {
      params: {
        startDate: yesterday,
        endDate: today
      }
    });
    console.log('✅ Aktuelle Cutting-Daten erhalten:', {
      status: response6.status,
      dataLength: response6.data?.data?.length || 0,
      firstFew: response6.data?.data?.slice(0, 3) || []
    });

    // 7. Test mit verschiedenen Zeitbereichen
    console.log('\n7️⃣ Teste mit verschiedenen Zeitbereichen...');

    const testRanges = [
      { start: '2025-01-01', end: '2025-01-31', name: 'Januar 2025' },
      { start: '2025-02-01', end: '2025-02-28', name: 'Februar 2025' },
      { start: '2025-03-01', end: '2025-03-31', name: 'März 2025' },
      { start: '2025-04-01', end: '2025-04-30', name: 'April 2025' },
      { start: '2025-05-01', end: '2025-05-31', name: 'Mai 2025' },
      { start: '2025-06-01', end: '2025-06-30', name: 'Juni 2025' },
      { start: '2025-07-01', end: '2025-07-31', name: 'Juli 2025' }
    ];

    for (const range of testRanges) {
      try {
        const response = await axios.get(`${API_BASE_URL}/database/cutting-chart-data`, {
          params: {
            startDate: range.start,
            endDate: range.end
          }
        });
        console.log(`✅ ${range.name}: ${response.data?.data?.length || 0} Datensätze`);
      } catch (error) {
        console.log(`❌ ${range.name}: Fehler - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Fehler beim Testen:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

// Führe Test aus
testCuttingData();