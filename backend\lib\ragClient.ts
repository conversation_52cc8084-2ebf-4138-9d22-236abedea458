/**
 * RAG Drizzle Client
 * Separate Client für die RAG-Datenbank gemäß Drizzle ORM
 *
 * Dieser Client wird für alle RAG-bezogenen Datenbankoperationen verwendet.
 * Er nutzt das separate RAG Schema aus src/db/rag-schema.ts
 */

import ragClient from './ragClientDrizzle';

// Re-export the Drizzle client with the same interface as the old Prisma client
export { ragClient };
export default ragClient;