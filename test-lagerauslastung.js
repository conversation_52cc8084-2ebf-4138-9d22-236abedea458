const { Pool } = require('pg');

// Datenbankverbindung
const pool = new Pool({
  host: 'localhost',
  port: 5434,
  database: 'leitstand_dashboard',
  user: 'leitstand_dashboard',
  password: 'dashboard_password',
});

async function testLagerauslastungTables() {
  console.log('🔍 Prüfe Lagerauslastung-Tabellen...\n');

  try {
    // Prüfe auslastung200 Tabelle
    console.log('📊 Prüfe auslastung200 Tabelle:');
    const result200 = await pool.query(`
      SELECT COUNT(*) as count,
             MIN("aufnahmeDatum") as min_date,
             MAX("aufnahmeDatum") as max_date
      FROM auslastung200
    `);

    console.log(`   Anzahl Datensätze: ${result200.rows[0].count}`);
    console.log(`   Zeitraum: ${result200.rows[0].min_date} bis ${result200.rows[0].max_date}`);

    if (result200.rows[0].count > 0) {
      console.log('\n   Erste 3 Datensätze:');
      const sample200 = await pool.query(`
        SELECT * FROM auslastung200
        ORDER BY "aufnahmeDatum" DESC
        LIMIT 3
      `);
      sample200.rows.forEach((row, i) => {
        console.log(`   ${i + 1}. ${JSON.stringify(row)}`);
      });
    }

    // Prüfe auslastung240 Tabelle
    console.log('\n📊 Prüfe auslastung240 Tabelle:');
    const result240 = await pool.query(`
      SELECT COUNT(*) as count,
             MIN("aufnahmeDatum") as min_date,
             MAX("aufnahmeDatum") as max_date
      FROM auslastung240
    `);

    console.log(`   Anzahl Datensätze: ${result240.rows[0].count}`);
    console.log(`   Zeitraum: ${result240.rows[0].min_date} bis ${result240.rows[0].max_date}`);

    if (result240.rows[0].count > 0) {
      console.log('\n   Erste 3 Datensätze:');
      const sample240 = await pool.query(`
        SELECT * FROM auslastung240
        ORDER BY "aufnahmeDatum" DESC
        LIMIT 3
      `);
      sample240.rows.forEach((row, i) => {
        console.log(`   ${i + 1}. ${JSON.stringify(row)}`);
      });
    }

    // Prüfe aktuelle Daten (letzte 7 Tage)
    const today = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(today.getDate() - 7);

    const dateStr = today.toISOString().split('T')[0];
    const sevenDaysAgoStr = sevenDaysAgo.toISOString().split('T')[0];

    console.log(`\n📅 Prüfe Daten für Zeitraum ${sevenDaysAgoStr} bis ${dateStr}:`);

    const recent200 = await pool.query(`
      SELECT COUNT(*) as count
      FROM auslastung200
      WHERE "aufnahmeDatum" >= $1 AND "aufnahmeDatum" <= $2
    `, [sevenDaysAgoStr, dateStr]);

    const recent240 = await pool.query(`
      SELECT COUNT(*) as count
      FROM auslastung240
      WHERE "aufnahmeDatum" >= $1 AND "aufnahmeDatum" <= $2
    `, [sevenDaysAgoStr, dateStr]);

    console.log(`   auslastung200 (letzte 7 Tage): ${recent200.rows[0].count} Datensätze`);
    console.log(`   auslastung240 (letzte 7 Tage): ${recent240.rows[0].count} Datensätze`);

  } catch (error) {
    console.error('❌ Fehler beim Prüfen der Tabellen:', error);
  } finally {
    await pool.end();
  }
}

// Test-API-Calls
async function testApiCalls() {
  console.log('\n🌐 Teste API-Calls...\n');

  const baseUrl = 'http://localhost:3001/api';

  try {
    // Test Lagerauslastung200 API
    console.log('📡 Teste /database/lagerauslastung200:');
    const response200 = await fetch(`${baseUrl}/database/lagerauslastung200`);
    const data200 = await response200.json();

    if (data200.success) {
      console.log(`   ✅ Erfolg: ${data200.data.length} Datensätze erhalten`);
      if (data200.data.length > 0) {
        console.log(`   Erster Datensatz: ${JSON.stringify(data200.data[0])}`);
      }
    } else {
      console.log(`   ❌ Fehler: ${data200.error}`);
    }

    // Test Lagerauslastung240 API
    console.log('\n📡 Teste /database/lagerauslastung240:');
    const response240 = await fetch(`${baseUrl}/database/lagerauslastung240`);
    const data240 = await response240.json();

    if (data240.success) {
      console.log(`   ✅ Erfolg: ${data240.data.length} Datensätze erhalten`);
      if (data240.data.length > 0) {
        console.log(`   Erster Datensatz: ${JSON.stringify(data240.data[0])}`);
      }
    } else {
      console.log(`   ❌ Fehler: ${data240.error}`);
    }

  } catch (error) {
    console.error('❌ Fehler beim API-Test:', error.message);
  }
}

// Hauptfunktion
async function main() {
  await testLagerauslastungTables();
  await testApiCalls();
  console.log('\n✅ Test abgeschlossen!');
}

main();