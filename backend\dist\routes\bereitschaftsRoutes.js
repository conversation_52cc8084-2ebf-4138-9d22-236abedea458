"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const bereitschaftsController_1 = require("../controllers/bereitschaftsController");
const router = (0, express_1.Router)();
// Personen-Routes
router.get('/personen', bereitschaftsController_1.bereitschaftsController.getAllPersonen.bind(bereitschaftsController_1.bereitschaftsController));
router.put('/personen/reihenfolge', bereitschaftsController_1.bereitschaftsController.updatePersonenReihenfolge.bind(bereitschaftsController_1.bereitschaftsController));
router.get('/personen/:id', bereitschaftsController_1.bereitschaftsController.getPersonById.bind(bereitschaftsController_1.bereitschaftsController));
router.post('/personen', bereitschaftsController_1.bereitschaftsController.createPerson.bind(bereitschaftsController_1.bereitschaftsController));
router.put('/personen/:id', bereitschaftsController_1.bereitschaftsController.updatePerson.bind(bereitschaftsController_1.bereitschaftsController));
router.delete('/personen/:id', bereitschaftsController_1.bereitschaftsController.deletePerson.bind(bereitschaftsController_1.bereitschaftsController));
// Wochenplan-Routes
router.get('/wochenplan', bereitschaftsController_1.bereitschaftsController.getWochenplan.bind(bereitschaftsController_1.bereitschaftsController));
router.get('/wochenplan/all', bereitschaftsController_1.bereitschaftsController.getAllWochen.bind(bereitschaftsController_1.bereitschaftsController));
router.get('/aktuelle-bereitschaft', bereitschaftsController_1.bereitschaftsController.getAktuelleBereitschaft.bind(bereitschaftsController_1.bereitschaftsController));
router.post('/wochenplan/generieren', bereitschaftsController_1.bereitschaftsController.generiereWochenplan.bind(bereitschaftsController_1.bereitschaftsController));
router.put('/wochenplan/:id', bereitschaftsController_1.bereitschaftsController.updateWoche.bind(bereitschaftsController_1.bereitschaftsController));
router.delete('/wochenplan/:id', bereitschaftsController_1.bereitschaftsController.deleteWoche.bind(bereitschaftsController_1.bereitschaftsController));
// Ausnahmen-Routes
router.get('/ausnahmen', bereitschaftsController_1.bereitschaftsController.getAllAusnahmen.bind(bereitschaftsController_1.bereitschaftsController));
router.post('/ausnahmen', bereitschaftsController_1.bereitschaftsController.createAusnahme.bind(bereitschaftsController_1.bereitschaftsController));
router.put('/ausnahmen/:id', bereitschaftsController_1.bereitschaftsController.updateAusnahme.bind(bereitschaftsController_1.bereitschaftsController));
router.delete('/ausnahmen/:id', bereitschaftsController_1.bereitschaftsController.deleteAusnahme.bind(bereitschaftsController_1.bereitschaftsController));
// Konfiguration-Routes
router.get('/konfiguration', bereitschaftsController_1.bereitschaftsController.getKonfiguration.bind(bereitschaftsController_1.bereitschaftsController));
router.put('/konfiguration', bereitschaftsController_1.bereitschaftsController.updateKonfiguration.bind(bereitschaftsController_1.bereitschaftsController));
// Erweiterte Routes
router.get('/plan-mit-ausnahmen', bereitschaftsController_1.bereitschaftsController.getBereitschaftsplanMitAusnahmen.bind(bereitschaftsController_1.bereitschaftsController));
router.get('/statistiken', bereitschaftsController_1.bereitschaftsController.getBereitschaftsStatistiken.bind(bereitschaftsController_1.bereitschaftsController));
exports.default = router;
