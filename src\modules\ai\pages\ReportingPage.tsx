/**
 * Reporting Page
 * 
 * Main page for report generation, template management, and export functionality.
 * Integrates the ReportingService with the report builder U<PERSON>.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Settings, 
  Download,
  FileText,
  Plus,
  Calendar,
  Users,
  BarChart3
} from 'lucide-react';
import { ReportBuilder } from '../components/reporting/ReportBuilder';
import { ReportTemplateManager } from '../components/reporting/ReportTemplateManager';
import { ReportPreview } from '../components/reporting/ReportPreview';
import { ReportScheduler } from '../components/reporting/ReportScheduler';
import { ReportingService } from '../services/reporting/ReportingService';
import type { 
  ReportTemplate, 
  GeneratedReport,
  ReportGenerationRequest 
} from '@/types/reporting';

/**
 * Reporting Page Component
 */
export const ReportingPage: React.FC = () => {
  // State management
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [reportingService, setReportingService] = useState<ReportingService | null>(null);
  const [activeTab, setActiveTab] = useState('templates');
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [showReportBuilder, setShowReportBuilder] = useState(false);
  const [generatingReport, setGeneratingReport] = useState(false);

  /**
   * Initialize the reporting service
   */
  const initializeService = useCallback(async () => {
    try {
      const service = new ReportingService({
        enableVectorCache: false,
        vectorCacheTTL: 0
      });

      await service.initialize();
      setReportingService(service);
      return service;
    } catch (err) {
      console.error('Failed to initialize ReportingService:', err);
      setError('Fehler beim Initialisieren des Reporting-Service');
      throw err;
    }
  }, []);

  /**
   * Load templates and reports
   */
  const loadData = useCallback(async (service?: ReportingService) => {
    try {
      setLoading(true);
      setError(null);

      const activeService = service || reportingService;
      if (!activeService) {
        throw new Error('Reporting service not initialized');
      }

      // Load templates
      const templatesData = await activeService.getTemplates();
      setTemplates(templatesData);

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to load data:', err);
      setError('Fehler beim Laden der Daten');
    } finally {
      setLoading(false);
    }
  }, [reportingService]);

  /**
   * Handle refresh
   */
  const handleRefresh = useCallback(async () => {
    await loadData();
  }, [loadData]);

  /**
    * Handle template creation
    */
   const handleCreateTemplate = useCallback(async (templateData: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
     if (!reportingService) {
       setError('Reporting-Service ist nicht initialisiert');
       return;
     }

     try {
       const newTemplate = await reportingService.createTemplate(templateData);
       setTemplates(prev => [...prev, newTemplate]);
       setShowReportBuilder(false);
     } catch (err) {
       console.error('Failed to create template:', err);
       setError('Fehler beim Erstellen der Vorlage');
     }
   }, [reportingService]);

  /**
    * Handle template update
    */
   const handleUpdateTemplate = useCallback(async (templateId: string, updates: Partial<ReportTemplate>) => {
     if (!reportingService) {
       setError('Reporting-Service ist nicht initialisiert');
       return;
     }

     try {
       const updatedTemplate = await reportingService.updateTemplate(templateId, updates);
       setTemplates(prev => prev.map(t => t.id === templateId ? updatedTemplate : t));
     } catch (err) {
       console.error('Failed to update template:', err);
       setError('Fehler beim Aktualisieren der Vorlage');
     }
   }, [reportingService]);

  /**
    * Handle template deletion
    */
   const handleDeleteTemplate = useCallback(async (templateId: string) => {
     if (!reportingService) {
       setError('Reporting-Service ist nicht initialisiert');
       return;
     }

     try {
       const success = await reportingService.deleteTemplate(templateId);
       if (success) {
         setTemplates(prev => prev.filter(t => t.id !== templateId));
         if (selectedTemplate?.id === templateId) {
           setSelectedTemplate(null);
         }
       }
     } catch (err) {
       console.error('Failed to delete template:', err);
       setError('Fehler beim Löschen der Vorlage');
     }
   }, [reportingService, selectedTemplate]);

  /**
    * Handle report generation
    */
   const handleGenerateReport = useCallback(async (request: ReportGenerationRequest): Promise<GeneratedReport> => {
     if (!reportingService) {
       const error = new Error('Reporting service not initialized');
       setError('Reporting-Service ist nicht initialisiert');
       throw error;
     }

     try {
       setGeneratingReport(true);
       const report = await reportingService.generateReport(request);
       setGeneratedReports(prev => [report, ...prev]);
       return report;
     } catch (err) {
       console.error('Failed to generate report:', err);
       setError('Fehler beim Generieren des Berichts');
       throw err;
     } finally {
       setGeneratingReport(false);
     }
   }, [reportingService]);

  /**
   * Handle report export
   */
  const handleExportReport = useCallback((report: GeneratedReport) => {
    const exportData = {
      ...report,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title.replace(/[^a-zA-Z0-9]/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  // Initialize service on mount
  useEffect(() => {
    const init = async () => {
      try {
        const service = await initializeService();
        await loadData(service);
      } catch (err) {
        // Error already handled in initializeService
      }
    };

    init();

    // Cleanup on unmount
    return () => {
      if (reportingService) {
        reportingService.destroy();
      }
    };
  }, []);

  // Error state
  if (error && !templates.length) {
    return (
      <div className="w-full bg-bg min-h-screen p-8">
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-bg min-h-screen p-8 space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Berichtsgenerierung</h1>
          <p className="text-gray-600 mt-1">
            KI-gestützte Berichtserstellung und Automatisierung
          </p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 mt-1">
              Letzte Aktualisierung: {lastUpdated.toLocaleString('de-DE')}
            </p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setShowReportBuilder(true)}
            disabled={loading}
          >
            <Plus className="h-4 w-4 mr-2" />
            Neue Vorlage
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Aktualisieren
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Vorlagen</p>
                <p className="text-2xl font-bold text-blue-600">
                  {templates.length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Aktive Vorlagen</p>
                <p className="text-2xl font-bold text-green-600">
                  {templates.filter(t => t.isActive).length}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Geplante Berichte</p>
                <p className="text-2xl font-bold text-purple-600">
                  {templates.filter(t => t.schedule?.isActive).length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Generierte Berichte</p>
                <p className="text-2xl font-bold text-orange-600">
                  {generatedReports.length}
                </p>
              </div>
              <Download className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error Alert */}
      {error && templates.length > 0 && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error} - Zeige zuletzt geladene Daten an.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="templates">Vorlagen</TabsTrigger>
          <TabsTrigger value="preview">Vorschau</TabsTrigger>
          <TabsTrigger value="scheduler">Planung</TabsTrigger>
          <TabsTrigger value="reports">Berichte</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <ReportTemplateManager
            templates={templates}
            loading={loading}
            onSelectTemplate={setSelectedTemplate}
            onUpdateTemplate={handleUpdateTemplate}
            onDeleteTemplate={handleDeleteTemplate}
            onRefresh={handleRefresh}
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <ReportPreview
            template={selectedTemplate}
            reportingService={reportingService}
            onGenerateReport={handleGenerateReport}
            generating={generatingReport}
          />
        </TabsContent>

        <TabsContent value="scheduler" className="space-y-4">
          <ReportScheduler
            templates={templates}
            onUpdateTemplate={handleUpdateTemplate}
            loading={loading}
          />
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="space-y-4">
            {generatedReports.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Keine Berichte vorhanden
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Generieren Sie Ihren ersten Bericht aus einer Vorlage.
                  </p>
                  <Button onClick={() => setActiveTab('templates')}>
                    Vorlagen anzeigen
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {generatedReports.map((report) => (
                  <Card key={report.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">{report.title}</CardTitle>
                          <CardDescription>
                            Generiert am {report.generatedAt.toLocaleString('de-DE')}
                          </CardDescription>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {report.format.toUpperCase()}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleExportReport(report)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="font-medium text-gray-600">Datenpunkte</p>
                          <p>{report.metadata.dataPoints.toLocaleString('de-DE')}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-600">Verarbeitungszeit</p>
                          <p>{report.metadata.processingTime}ms</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-600">Erkenntnisse</p>
                          <p>{report.insights.length}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-600">Empfehlungen</p>
                          <p>{report.recommendations.length}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Report Builder Modal */}
      {showReportBuilder && (
        <ReportBuilder
          onSave={handleCreateTemplate}
          onCancel={() => setShowReportBuilder(false)}
          reportingService={reportingService}
        />
      )}

      {/* Service Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Service Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-600">Reporting Service</p>
              <Badge variant={reportingService ? "default" : "destructive"}>
                {reportingService ? "Aktiv" : "Inaktiv"}
              </Badge>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600">Vorlagen geladen</p>
              <Badge variant={templates.length > 0 ? "default" : "secondary"}>
                {templates.length > 0 ? "Ja" : "Nein"}
              </Badge>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600">Letzte Aktualisierung</p>
              <p className="text-sm text-gray-600">
                {lastUpdated ? lastUpdated.toLocaleTimeString('de-DE') : 'Nie'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportingPage;