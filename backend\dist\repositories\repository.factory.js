"use strict";
/**
 * Repository Factory
 *
 * Zentrale Factory-Klasse für die Erstellung und Verwaltung
 * aller Repository-Instanzen mit Singleton-Pattern.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryFactoryImpl = void 0;
exports.initializeRepositoryFactory = initializeRepositoryFactory;
exports.getRepositoryFactory = getRepositoryFactory;
exports.getDispatchRepository = getDispatchRepository;
exports.getWarehouseRepository = getWarehouseRepository;
exports.getCuttingRepository = getCuttingRepository;
exports.getDeliveryRepository = getDeliveryRepository;
exports.getProductionRepository = getProductionRepository;
exports.getSupplierRepository = getSupplierRepository;
exports.getSystemRepository = getSystemRepository;
exports.getUserRepository = getUserRepository;
const db_1 = require("../db");
const dispatch_repository_1 = require("./dispatch.repository");
const warehouse_repository_1 = require("./warehouse.repository");
const cutting_repository_1 = require("./cutting.repository");
const delivery_repository_1 = require("./delivery.repository");
const production_repository_1 = require("./production.repository");
const supplier_repository_1 = require("./supplier.repository");
const system_repository_1 = require("./system.repository");
const user_repository_1 = require("./user.repository");
/**
 * Repository Factory Implementation
 */
class RepositoryFactoryImpl {
    constructor() {
        this.db = db_1.db;
        // Drizzle DB wird direkt importiert
    }
    /**
     * Singleton Instance abrufen
     */
    static getInstance() {
        if (!RepositoryFactoryImpl.instance) {
            RepositoryFactoryImpl.instance = new RepositoryFactoryImpl();
        }
        return RepositoryFactoryImpl.instance;
    }
    /**
     * Dispatch Repository abrufen
     */
    dispatch() {
        if (!this._dispatchRepository) {
            this._dispatchRepository = new dispatch_repository_1.DispatchRepositoryImpl();
        }
        return this._dispatchRepository;
    }
    /**
     * Warehouse Repository abrufen
     */
    warehouse() {
        if (!this._warehouseRepository) {
            this._warehouseRepository = new warehouse_repository_1.WarehouseRepositoryImpl();
        }
        return this._warehouseRepository;
    }
    /**
     * Cutting Repository abrufen
     */
    cutting() {
        if (!this._cuttingRepository) {
            this._cuttingRepository = new cutting_repository_1.CuttingRepositoryImpl();
        }
        return this._cuttingRepository;
    }
    /**
     * Delivery Repository abrufen
     */
    delivery() {
        if (!this._deliveryRepository) {
            this._deliveryRepository = new delivery_repository_1.DeliveryRepositoryImpl();
        }
        return this._deliveryRepository;
    }
    /**
     * Production Repository abrufen
     */
    production() {
        if (!this._productionRepository) {
            this._productionRepository = new production_repository_1.ProductionRepositoryImpl();
        }
        return this._productionRepository;
    }
    /**
     * Supplier Repository abrufen
     */
    supplier() {
        if (!this._supplierRepository) {
            this._supplierRepository = new supplier_repository_1.SupplierRepositoryImpl();
        }
        return this._supplierRepository;
    }
    /**
     * System Repository abrufen
     */
    system() {
        if (!this._systemRepository) {
            this._systemRepository = new system_repository_1.SystemRepositoryImpl();
        }
        return this._systemRepository;
    }
    /**
     * User Repository abrufen
     */
    user() {
        if (!this._userRepository) {
            this._userRepository = new user_repository_1.UserRepository();
        }
        return this._userRepository;
    }
    /**
     * Repository Statistics für Monitoring
     */
    async getAllRepositoryStats() {
        const stats = {
            timestamp: new Date().toISOString(),
            repositories: {}
        };
        // Dispatch Repository Stats
        if (this._dispatchRepository) {
            stats.repositories.dispatch = await this._dispatchRepository.getStats();
        }
        // Warehouse Repository Stats
        if (this._warehouseRepository) {
            stats.repositories.warehouse = await this._warehouseRepository.getStats();
        }
        // Cutting Repository Stats
        if (this._cuttingRepository) {
            stats.repositories.cutting = await this._cuttingRepository.getStats();
        }
        // Delivery Repository Stats
        if (this._deliveryRepository) {
            stats.repositories.delivery = await this._deliveryRepository.getStats();
        }
        // Production Repository Stats
        if (this._productionRepository) {
            stats.repositories.production = await this._productionRepository.getOverallProductionStats();
        }
        // Supplier Repository Stats
        if (this._supplierRepository) {
            stats.repositories.supplier = await this._supplierRepository.getStats();
        }
        // System Repository Stats
        if (this._systemRepository) {
            stats.repositories.system = await this._systemRepository.getSystemDashboard();
        }
        // User Repository Stats
        if (this._userRepository) {
            stats.repositories.user = { totalUsers: 0, activeUsers: 0 };
        }
        return stats;
    }
    /**
     * Alle Repository-Caches invalidieren
     */
    async invalidateAllCaches() {
        const invalidationPromises = [];
        if (this._dispatchRepository) {
            invalidationPromises.push(this._dispatchRepository.invalidateCache());
        }
        if (this._warehouseRepository) {
            invalidationPromises.push(this._warehouseRepository.invalidateCache());
        }
        if (this._cuttingRepository) {
            invalidationPromises.push(this._cuttingRepository.invalidateCache());
        }
        if (this._deliveryRepository) {
            invalidationPromises.push(this._deliveryRepository.invalidateCache());
        }
        if (this._productionRepository) {
            invalidationPromises.push(this._productionRepository.invalidateAllCache());
        }
        if (this._supplierRepository) {
            invalidationPromises.push(this._supplierRepository.invalidateCache());
        }
        if (this._systemRepository) {
            invalidationPromises.push(this._systemRepository.invalidateAllCache());
        }
        if (this._userRepository) {
            // User repository doesn't have cache invalidation yet
        }
        await Promise.all(invalidationPromises);
    }
    /**
     * Factory zurücksetzen (für Testing)
     */
    static reset() {
        RepositoryFactoryImpl.instance = undefined;
    }
}
exports.RepositoryFactoryImpl = RepositoryFactoryImpl;
/**
 * Globale Repository Factory Instance
 */
let repositoryFactory;
/**
 * Repository Factory initialisieren
 */
function initializeRepositoryFactory() {
    repositoryFactory = RepositoryFactoryImpl.getInstance();
    return repositoryFactory;
}
/**
 * Repository Factory abrufen
 */
function getRepositoryFactory() {
    if (!repositoryFactory) {
        throw new Error('Repository factory not initialized. Call initializeRepositoryFactory first.');
    }
    return repositoryFactory;
}
/**
 * Direkte Repository-Zugriffe (Convenience Functions)
 */
function getDispatchRepository() {
    return getRepositoryFactory().dispatch();
}
function getWarehouseRepository() {
    return getRepositoryFactory().warehouse();
}
function getCuttingRepository() {
    return getRepositoryFactory().cutting();
}
function getDeliveryRepository() {
    return getRepositoryFactory().delivery();
}
function getProductionRepository() {
    return getRepositoryFactory().production();
}
function getSupplierRepository() {
    return getRepositoryFactory().supplier();
}
function getSystemRepository() {
    return getRepositoryFactory().system();
}
function getUserRepository() {
    return getRepositoryFactory().user();
}
