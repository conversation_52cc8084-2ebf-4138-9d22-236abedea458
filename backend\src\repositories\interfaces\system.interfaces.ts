/**
 * System Repository Interfaces
 *
 * Type definitions for system data management
 */

import {
  ServiceLevelDataPoint,
  PickingDataPoint,
  DeliveryDataPoint,
  TagesleistungDataPoint
} from '../../types/database.types';

/**
 * Filter-Interface für Datum-basierte Repositories
 */
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

/**
 * Filter für System-Daten
 */
export interface SystemFilter extends DateRangeFilter {
  systemType?: 'performance' | 'service-level' | 'picking' | 'returns' | 'delivery';
  minServiceLevel?: number;
  maxServiceLevel?: number;
}

/**
 * Daily Performance Datenpunkt
 */
export interface DailyPerformanceDataPoint {
  date: string;
  performance: number;
  target?: number;
}

/**
 * Returns Datenpunkt
 */
export interface ReturnsDataPoint {
  date: string;
  returns: number;
  category?: string;
}

/**
 * Delivery Positions Datenpunkt
 */
export interface DeliveryPositionsDataPoint {
  date: string;
  positions: number;
  onTime?: boolean;
  delayed?: boolean;
}

export interface ServiceLevelStats {
  average: number;
  current: number;
  trend: 'improving' | 'declining' | 'stable';
  aboveTarget: number;
  belowTarget: number;
  targetPercentage: number;
}

export interface PerformanceTrend {
  trend: 'improving' | 'declining' | 'stable';
  averageDaily: number;
  bestDay: DailyPerformanceDataPoint | null;
  worstDay: DailyPerformanceDataPoint | null;
  consistency: number;
}

export interface PickingEfficiency {
  averagePicksPerDay: number;
  totalPicks: number;
  efficiency: number;
  trends: { date: string; picks: number }[];
}

export interface ReturnsAnalysis {
  totalReturns: number;
  averageReturnsPerDay: number;
  returnRate: number;
  trends: { date: string; returns: number }[];
  categories: Record<string, number>;
}

export interface DeliveryAnalysis {
  totalPositions: number;
  averagePositionsPerDay: number;
  deliveryEfficiency: number;
  onTimeDeliveries: number;
  delays: number;
}

export interface DailyPerformanceStats {
  averageDaily: number;
  bestPerformance: TagesleistungDataPoint | null;
  worstPerformance: TagesleistungDataPoint | null;
  targetAchievement: number;
  consistency: number;
}

export interface SystemHealthDashboard {
  overall: 'healthy' | 'warning' | 'critical';
  components: Record<string, { status: string; value: number }>;
  recommendations: string[];
}

export interface SystemDashboard {
  serviceLevel: ServiceLevelStats;
  performance: PerformanceTrend;
  picking: PickingEfficiency;
  delivery: DeliveryAnalysis;
  systemHealth: SystemHealthDashboard;
}

/**
 * Service Level Repository Interface
 */
export interface IServiceLevelRepository {
  getAll(filter?: SystemFilter): Promise<ServiceLevelDataPoint[]>;
  getCurrentServiceLevel(): Promise<ServiceLevelDataPoint | null>;
  getServiceLevelStats(filter?: SystemFilter): Promise<ServiceLevelStats>;
}

/**
 * Daily Performance Repository Interface
 */
export interface IDailyPerformanceRepository {
  getAll(filter?: DateRangeFilter): Promise<DailyPerformanceDataPoint[]>;
  getPerformanceTrend(days?: number): Promise<PerformanceTrend>;
}

/**
 * Picking Repository Interface
 */
export interface IPickingRepository {
  getAll(): Promise<PickingDataPoint[]>;
  getPickingEfficiency(): Promise<PickingEfficiency>;
}

/**
 * Returns Repository Interface
 */
export interface IReturnsRepository {
  getAll(): Promise<ReturnsDataPoint[]>;
  getReturnsAnalysis(): Promise<ReturnsAnalysis>;
}

/**
 * Delivery Positions Repository Interface
 */
export interface IDeliveryPositionsRepository {
  getAll(): Promise<DeliveryPositionsDataPoint[]>;
  getDeliveryAnalysis(): Promise<DeliveryAnalysis>;
}

/**
 * Tagesleistung Repository Interface
 */
export interface ITagesleistungRepository {
  getAll(): Promise<TagesleistungDataPoint[]>;
  getDailyPerformanceStats(): Promise<DailyPerformanceStats>;
}

/**
 * System Stats Repository Interface
 */
export interface ISystemStatsRepository {
  getAll(filter?: DateRangeFilter): Promise<any>;
  getSystemHealthDashboard(): Promise<SystemHealthDashboard>;
}

/**
 * System Repository Interface
 */
export interface ISystemRepository {
  serviceLevel: IServiceLevelRepository;
  dailyPerformance: IDailyPerformanceRepository;
  picking: IPickingRepository;
  returns: IReturnsRepository;
  deliveryPositions: IDeliveryPositionsRepository;
  tagesleistung: ITagesleistungRepository;
  systemStats: ISystemStatsRepository;

  invalidateAllCache(): void;
  getSystemDashboard(filter?: DateRangeFilter): Promise<SystemDashboard>;
}