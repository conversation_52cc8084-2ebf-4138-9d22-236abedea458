/**
 * Production Repository Interface
 *
 * Definiert die Schnittstelle für Production-Datenoperationen
 */

import { ProductionRepositoryImpl } from '../production.repository';

export interface ProductionRepository {
  schnitte: any;
  maschinenEfficiency: any;
  ablaengerei: any;
  cuttingChart: any;
  lagerCuts: any;

  invalidateAllCache(): void;
  getOverallProductionStats(): Promise<any>;
}

export { ProductionRepositoryImpl };