/**
 * RAG Drizzle Client
 * Drizzle ORM Client für die RAG-Datenbank
 *
 * Dieser Client wird für alle RAG-bezogenen Datenbankoperationen verwendet.
 * Er nutzt das separate RAG Schema aus src/db/rag-schema.ts
 */

import { drizzle, NodePgDatabase } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from '../src/db/rag-schema';

// RAG-spezifischer Drizzle Client
class RAGDrizzleClient {
  private pool: Pool;
  public db: NodePgDatabase<typeof schema>;

  constructor() {
    // Initialize PostgreSQL connection pool for RAG database
    this.pool = new Pool({
      host: process.env.RAG_DB_HOST || 'localhost',
      port: Number(process.env.RAG_DB_PORT) || 5434,
      database: process.env.RAG_DB_NAME || 'rag_knowledge',
      user: process.env.RAG_DB_USER || 'leitstand_dashboard',
      password: process.env.RAG_DB_PASSWORD || 'dashboard_password',
      max: 20, // Maximum number of connections
      idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
      connectionTimeoutMillis: 5000, // Connection timeout
    });

    // Initialize Drizzle client with RAG schema
    this.db = drizzle(this.pool, { schema });

    // Graceful shutdown handler
    this.setupGracefulShutdown();
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('Shutting down RAG database connection...');
      await this.pool.end();
      console.log('RAG database connection closed');
    };

    process.on('beforeExit', shutdown);
    process.on('SIGINT', async () => {
      await shutdown();
      process.exit(0);
    });
    process.on('SIGTERM', async () => {
      await shutdown();
      process.exit(0);
    });
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      console.log('RAG database connection test successful');
      return true;
    } catch (error) {
      console.error('RAG database connection test failed:', error);
      return false;
    }
  }

  /**
   * Get database statistics
   */
  async getStatistics(): Promise<{
    totalDocuments: number;
    totalChunks: number;
    totalEmbeddings: number;
    storageSize: number;
  }> {
    try {
      const client = await this.pool.connect();

      const [docResult, chunkResult, embedResult] = await Promise.all([
        client.query('SELECT COUNT(*) as count FROM documents'),
        client.query('SELECT COUNT(*) as count FROM chunks'),
        client.query('SELECT COUNT(*) as count FROM embeddings'),
      ]);

      client.release();

      return {
        totalDocuments: parseInt(docResult.rows[0].count),
        totalChunks: parseInt(chunkResult.rows[0].count),
        totalEmbeddings: parseInt(embedResult.rows[0].count),
        storageSize: 0, // Would need pg_stat_user_tables for actual size
      };
    } catch (error) {
      console.error('Error getting RAG database statistics:', error);
      return {
        totalDocuments: 0,
        totalChunks: 0,
        totalEmbeddings: 0,
        storageSize: 0,
      };
    }
  }

  /**
   * Initialize RAG database tables
   */
  async initializeTables(): Promise<void> {
    try {
      console.log('Initializing RAG database tables...');
      const client = await this.pool.connect();

      // Create tables if they don't exist
      await client.query(`
        -- Documents table
        CREATE TABLE IF NOT EXISTS documents (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            source TEXT NOT NULL,
            source_path TEXT,
            content_type TEXT DEFAULT 'text/plain',
            language TEXT DEFAULT 'de',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            indexed_at TIMESTAMP,
            status TEXT DEFAULT 'pending'
        );

        -- Chunks table
        CREATE TABLE IF NOT EXISTS chunks (
            id TEXT PRIMARY KEY,
            document_id TEXT NOT NULL,
            content TEXT NOT NULL,
            chunk_index INTEGER NOT NULL,
            token_count INTEGER,
            start_position INTEGER,
            end_position INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
        );

        -- Embeddings table
        CREATE TABLE IF NOT EXISTS embeddings (
            id TEXT PRIMARY KEY,
            chunk_id TEXT NOT NULL,
            vector FLOAT[] NOT NULL,
            model_name TEXT NOT NULL DEFAULT 'text-embedding-3-small',
            dimension INTEGER NOT NULL DEFAULT 1536,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (chunk_id) REFERENCES chunks(id) ON DELETE CASCADE
        );

        -- Categories table
        CREATE TABLE IF NOT EXISTS categories (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            parent_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id)
        );

        -- Indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_documents_source ON documents(source);
        CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
        CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);
        CREATE INDEX IF NOT EXISTS idx_embeddings_chunk_id ON embeddings(chunk_id);
      `);

      client.release();
      console.log('RAG database tables initialized successfully');
    } catch (error) {
      console.error('Error initializing RAG database tables:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const ragClient = new RAGDrizzleClient();

// Initialize tables on startup
ragClient.initializeTables().catch(error => {
  console.error('Failed to initialize RAG database tables:', error);
});

export { ragClient };
export default ragClient;