/**
 * AI Service Health Hook
 * 
 * Custom hook for monitoring AI service health status and metrics
 */

import { useState, useEffect, useCallback } from 'react';
import { AIServiceStatus } from '../services/base/AIBaseService';

// Import AI services for health checks
import {
  RAGService,
  CuttingOptimizerService,
  InventoryIntelligenceService,
  WarehouseOptimizerService,
  SupplyChainOptimizerService,
  ReportingService
} from '../services';

// Import required services for RAGService
import { VectorDatabaseService } from '../services/vector/VectorDatabaseService';
import { EmbeddingService } from '../services/embedding/EmbeddingService';

interface UseAIServiceHealthReturn {
  serviceStatuses: Record<string, AIServiceStatus>;
  isLoading: boolean;
  error: string | null;
  refreshStatuses: () => Promise<void>;
  lastUpdated: Date | null;
}

/**
 * Hook for monitoring AI service health
 */
export const useAIServiceHealth = (): UseAIServiceHealthReturn => {
  const [serviceStatuses, setServiceStatuses] = useState<Record<string, AIServiceStatus>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Service instances (in a real implementation, these would be singletons)
  const services = {
    chat: new RAGService(
      new VectorDatabaseService(),
      new EmbeddingService()
    ),
    cutting: new CuttingOptimizerService(),
    inventory: new InventoryIntelligenceService(),
    warehouse: new WarehouseOptimizerService(),
    'supply-chain': new SupplyChainOptimizerService(),
    reporting: new ReportingService()
  };

  /**
   * Refresh all service statuses
   */
  const refreshStatuses = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const statusPromises = Object.entries(services).map(async ([serviceId, service]) => {
        try {
          const status = await service.healthCheck();
          return [serviceId, status] as [string, AIServiceStatus];
        } catch (serviceError) {
          console.error(`Health check failed for ${serviceId}:`, serviceError);
          // Return a failed status for this service
          return [serviceId, {
            isInitialized: false,
            isHealthy: false,
            lastChecked: new Date(),
            performance: {
              averageResponseTime: 0,
              successRate: 0,
              totalRequests: 0,
              cacheHitRate: 0
            }
          }] as [string, AIServiceStatus];
        }
      });

      const results = await Promise.all(statusPromises);
      const newStatuses = Object.fromEntries(results);
      
      setServiceStatuses(newStatuses);
      setLastUpdated(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(`Failed to refresh service statuses: ${errorMessage}`);
      console.error('Failed to refresh service statuses:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Initialize service health monitoring
   */
  useEffect(() => {
    // Initial load
    refreshStatuses();

    // Set up periodic refresh (every 5 minutes)
    const interval = setInterval(() => {
      refreshStatuses();
    }, 5 * 60 * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [refreshStatuses]);

  return {
    serviceStatuses,
    isLoading,
    error,
    refreshStatuses,
    lastUpdated
  };
};

/**
 * Hook for monitoring a specific AI service
 */
export const useAIServiceStatus = (serviceId: string) => {
  const { serviceStatuses, isLoading, error, refreshStatuses } = useAIServiceHealth();
  
  return {
    status: serviceStatuses[serviceId],
    isLoading,
    error,
    refreshStatus: refreshStatuses
  };
};

/**
 * Hook for getting overall system health metrics
 */
export const useAISystemHealth = () => {
  const { serviceStatuses, isLoading, error } = useAIServiceHealth();
  
  const systemHealth = {
    totalServices: Object.keys(serviceStatuses).length,
    healthyServices: Object.values(serviceStatuses).filter(status => status.isHealthy).length,
    unhealthyServices: Object.values(serviceStatuses).filter(status => !status.isHealthy).length,
    averageResponseTime: 0,
    totalRequests: 0,
    overallSuccessRate: 0
  };

  if (systemHealth.totalServices > 0) {
    const validStatuses = Object.values(serviceStatuses).filter(status => status.performance);
    
    if (validStatuses.length > 0) {
      systemHealth.averageResponseTime = validStatuses.reduce(
        (sum, status) => sum + (status.performance?.averageResponseTime || 0), 0
      ) / validStatuses.length;
      
      systemHealth.totalRequests = validStatuses.reduce(
        (sum, status) => sum + (status.performance?.totalRequests || 0), 0
      );
      
      systemHealth.overallSuccessRate = validStatuses.reduce(
        (sum, status) => sum + (status.performance?.successRate || 0), 0
      ) / validStatuses.length;
    }
  }

  return {
    systemHealth,
    isLoading,
    error,
    healthPercentage: systemHealth.totalServices > 0 
      ? (systemHealth.healthyServices / systemHealth.totalServices) * 100 
      : 0
  };
};