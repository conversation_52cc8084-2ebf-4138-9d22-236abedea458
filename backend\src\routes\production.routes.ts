import { Router } from 'express';
import { ProductionController } from '../controllers/production.controller';
import { rateLimit } from 'express-rate-limit';

const router = Router();
const controller = new ProductionController();

// Rate limiting für Production-Routen
const productionLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: 100, // Limit pro Window pro IP
  message: {
    success: false,
    error: '<PERSON>u viele Anfragen von dieser IP-Adresse',
    retryAfter: '15 Minuten'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Schnitte Routes
router.get('/schnitte', productionLimiter, controller.getSchnitteData.bind(controller));
router.get('/schnitte/grouped', productionLimiter, controller.getSchnitteGroupedByDate.bind(controller));
router.get('/schnitte/sums', productionLimiter, controller.getSchnitteDailySums.bind(controller));
router.get('/schnitte/performance', productionLimiter, controller.getMachinePerformance.bind(controller));

// Maschinen-Effizienz Routes
router.get('/efficiency', productionLimiter, controller.getMachineEfficiency.bind(controller));
router.get('/efficiency/top', productionLimiter, controller.getTopPerformers.bind(controller));
router.get('/efficiency/low', productionLimiter, controller.getLowPerformers.bind(controller));
router.get('/efficiency/average', productionLimiter, controller.getAverageEfficiency.bind(controller));

// Ablaengerei Routes
router.get('/ablaengerei', productionLimiter, controller.getAblaengereiData.bind(controller));
router.get('/ablaengerei/stats', productionLimiter, controller.getAblaengereiDailyStats.bind(controller));
router.get('/ablaengerei/warehouse-performance', productionLimiter, controller.getWarehousePerformance.bind(controller));

// Cutting Chart Routes
router.get('/cutting-chart', productionLimiter, controller.getCuttingChartData.bind(controller));
router.get('/cutting-chart/trends', productionLimiter, controller.getCuttingTrendAnalysis.bind(controller));

// Lager Cuts Routes
router.get('/lager-cuts', productionLimiter, controller.getLagerCutsData.bind(controller));
router.get('/lager-cuts/distribution', productionLimiter, controller.getWarehouseDistribution.bind(controller));

// Gesamtstatistiken
router.get('/stats', productionLimiter, controller.getOverallProductionStats.bind(controller));

export default router;