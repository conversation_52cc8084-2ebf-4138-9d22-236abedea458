import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, BookOpen, Search, Check } from 'lucide-react';
import { Runbook } from '@/types/runbooks.types';
import { runbookService } from '@/services/runbookService';
import { toast } from 'sonner';
import { RunbookEditor } from './RunbookEditor';
import { RunbookView } from './RunbookView';

interface RunbookListProps {
  onSelect?: (runbook: Runbook) => void;
}

export const RunbookList: React.FC<RunbookListProps> = ({ onSelect }) => {
  const [runbooks, setRunbooks] = useState<Runbook[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedRunbook, setSelectedRunbook] = useState<Runbook | null>(null);

  const isSelectionMode = !!onSelect;

  useEffect(() => {
    fetchRunbooks();
  }, []);

  const fetchRunbooks = async () => {
    try {
      setLoading(true);
      const data = await runbookService.getAllRunbooks();
      setRunbooks(data);
    } catch (error) {
      toast.error('Fehler beim Laden der Runbooks.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery) {
        fetchRunbooks();
        return;
    }
    try {
        setLoading(true);
        const data = await runbookService.searchRunbooks(searchQuery);
        setRunbooks(data);
    } catch (error) {
        toast.error('Fehler bei der Suche nach Runbooks.');
    } finally {
        setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
      if(confirm('Möchten Sie dieses Runbook wirklich löschen?')){
          try {
              await runbookService.deleteRunbook(id);
              toast.success('Runbook erfolgreich gelöscht.');
              fetchRunbooks();
          } catch (error) {
              toast.error('Fehler beim Löschen des Runbooks.');
          }
      }
  }

  const handleOpenEditor = (runbook: Runbook | null) => {
      setSelectedRunbook(runbook);
      setIsEditorOpen(true);
  }

  const handleEditorClose = () => {
      setIsEditorOpen(false);
      setSelectedRunbook(null);
      fetchRunbooks();
  }

  const handleOpenView = (runbook: Runbook) => {
      setSelectedRunbook(runbook);
      setIsViewOpen(true);
  }

  const handleViewClose = () => {
      setIsViewOpen(false);
      setSelectedRunbook(null);
  }

  return (
    <Card className="border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            Runbooks / Wissensdatenbank
          </CardTitle>
          {!isSelectionMode && (
            <Button onClick={() => handleOpenEditor(null)}>
              <Plus className="h-4 w-4 mr-2" />
              Neues Runbook
            </Button>
          )}
        </div>
        {!isSelectionMode && (
            <div className="flex gap-2 mt-4">
                <Input placeholder="Suchen..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
                <Button onClick={handleSearch}><Search className="h-4 w-4"/></Button>
            </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <div>Lade Runbooks...</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Titel</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead className="text-right">{isSelectionMode ? 'Auswählen' : 'Aktionen'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {runbooks.map((runbook) => (
                <TableRow key={runbook.id}>
                  <TableCell>{runbook.title}</TableCell>
                  <TableCell>{runbook.category?.join(', ')}</TableCell>
                  <TableCell className="text-right">
                    {isSelectionMode ? (
                        <Button variant="outline" size="sm" onClick={() => onSelect(runbook)}><Check className="h-4 w-4" /></Button>
                    ) : (
                        <div className="flex gap-2 justify-end">
                            <Button variant="ghost" size="sm" onClick={() => handleOpenView(runbook)} title="Runbook anzeigen"><BookOpen className="h-4 w-4" /></Button>
                            <Button variant="ghost" size="sm" onClick={() => handleOpenEditor(runbook)} title="Runbook bearbeiten"><Edit className="h-4 w-4" /></Button>
                            <Button variant="ghost" size="sm" onClick={() => handleDelete(runbook.id)} className="text-red-600 hover:text-red-700" title="Runbook löschen"><Trash2 className="h-4 w-4" /></Button>
                        </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
      <RunbookEditor
          runbook={selectedRunbook}
          onClose={handleEditorClose}
          open={isEditorOpen}
          onOpenChange={setIsEditorOpen}
          onSubmit={handleEditorClose}
      />
      <RunbookView
          runbook={selectedRunbook}
          open={isViewOpen}
          onOpenChange={setIsViewOpen}
      />
    </Card>
  );
};
