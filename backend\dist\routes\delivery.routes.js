"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const delivery_controller_1 = require("../controllers/delivery.controller");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const router = (0, express_1.Router)();
const controller = new delivery_controller_1.DeliveryController();
// Rate limiting for delivery endpoints
const deliveryLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.NODE_ENV === "development" ? 500 : 100, // Limit each IP
    message: "Too many requests to delivery API from this IP",
    standardHeaders: true,
    legacyHeaders: false,
});
// Apply rate limiting to all routes
router.use(deliveryLimiter);
// Delivery history routes
router.get('/history', controller.getDeliveryHistory.bind(controller));
router.get('/supplier/:supplierId', controller.getSupplierDeliveryHistory.bind(controller));
router.get('/route/:routeId', controller.getDeliveriesByRoute.bind(controller));
// Statistics and analytics routes
router.get('/stats', controller.getDeliveryStats.bind(controller));
router.get('/performance', controller.getDeliveryPerformanceMetrics.bind(controller));
router.get('/trends', controller.getDeliveryTrends.bind(controller));
router.get('/delay-reasons', controller.getDelayReasons.bind(controller));
router.get('/seasonal-patterns', controller.getSeasonalPatterns.bind(controller));
// Active routes
router.get('/routes/active', controller.getActiveRoutes.bind(controller));
// CRUD operations
router.post('/', controller.createDelivery.bind(controller));
router.put('/:deliveryId/status', controller.updateDeliveryStatus.bind(controller));
exports.default = router;
