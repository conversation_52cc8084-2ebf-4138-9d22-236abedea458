"use strict";
/**
 * E-Mail Controller
 *
 * Controller für E-Mail-Versand mit Anhängen.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailController = void 0;
const zod_1 = require("zod");
const email_service_1 = require("../services/email.service");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
// Validation Schemas
const sendEmailSchema = zod_1.z.object({
    to: zod_1.z.union([zod_1.z.string().email(), zod_1.z.array(zod_1.z.string().email())]),
    cc: zod_1.z.union([zod_1.z.string().email(), zod_1.z.array(zod_1.z.string().email())]).optional(),
    bcc: zod_1.z.union([zod_1.z.string().email(), zod_1.z.array(zod_1.z.string().email())]).optional(),
    subject: zod_1.z.string().min(1, 'Betreff ist erforderlich'),
    text: zod_1.z.string().optional(),
    html: zod_1.z.string().optional(),
    attachmentPaths: zod_1.z.array(zod_1.z.string()).optional()
});
const sendStoerungEmailSchema = zod_1.z.object({
    to: zod_1.z.union([zod_1.z.string().email(), zod_1.z.array(zod_1.z.string().email())]),
    stoerungId: zod_1.z.string().min(1, 'Störungs-ID ist erforderlich'),
    stoerungData: zod_1.z.object({
        title: zod_1.z.string().min(1),
        description: zod_1.z.string().min(1),
        severity: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
        affected_system: zod_1.z.string().min(1),
        location: zod_1.z.string().optional(),
        reported_by: zod_1.z.string().optional()
    }),
    includeAttachments: zod_1.z.boolean().default(true)
});
class EmailController {
    constructor() {
        this.emailService = (0, email_service_1.getEmailService)();
    }
    /**
     * Sendet eine allgemeine E-Mail
     */
    async sendEmail(req, res) {
        try {
            const validatedData = sendEmailSchema.parse(req.body);
            // Überprüfe E-Mail-Service-Konfiguration
            const isConfigured = await this.emailService.verifyConnection();
            if (!isConfigured) {
                return res.status(500).json({
                    success: false,
                    error: 'E-Mail-Service ist nicht konfiguriert oder nicht erreichbar'
                });
            }
            // Bereite Anhänge vor
            let attachments = [];
            if (validatedData.attachmentPaths && validatedData.attachmentPaths.length > 0) {
                for (const filePath of validatedData.attachmentPaths) {
                    if (fs.existsSync(filePath)) {
                        attachments.push({
                            filename: path.basename(filePath),
                            path: filePath,
                            contentType: this.getContentType(filePath)
                        });
                    }
                    else {
                        console.warn(`Anhang nicht gefunden: ${filePath}`);
                    }
                }
            }
            // Verarbeite Anhänge aus der Anfrage
            if (req.files && Array.isArray(req.files)) {
                for (const file of req.files) {
                    attachments.push({
                        filename: file.originalname,
                        path: file.path,
                        contentType: file.mimetype
                    });
                }
            }
            // Sende E-Mail
            const success = await this.emailService.sendEmail({
                to: validatedData.to,
                cc: validatedData.cc,
                bcc: validatedData.bcc,
                subject: validatedData.subject,
                text: validatedData.text,
                html: validatedData.html,
                attachments: attachments.length > 0 ? attachments : undefined
            });
            if (success) {
                res.json({
                    success: true,
                    message: 'E-Mail erfolgreich gesendet',
                    attachmentCount: attachments.length
                });
            }
            else {
                res.status(500).json({
                    success: false,
                    error: 'E-Mail konnte nicht gesendet werden'
                });
            }
        }
        catch (error) {
            console.error('Fehler beim E-Mail-Versand:', error);
            if (error instanceof zod_1.z.ZodError) {
                return res.status(400).json({
                    success: false,
                    error: 'Validierungsfehler',
                    details: error.errors
                });
            }
            res.status(500).json({
                success: false,
                error: 'Interner Server-Fehler beim E-Mail-Versand'
            });
        }
    }
    /**
     * Sendet eine Störungs-E-Mail mit Anhängen
     */
    async sendStoerungEmail(req, res) {
        try {
            const validatedData = sendStoerungEmailSchema.parse(req.body);
            // Überprüfe E-Mail-Service-Konfiguration
            const isConfigured = await this.emailService.verifyConnection();
            if (!isConfigured) {
                return res.status(500).json({
                    success: false,
                    error: 'E-Mail-Service ist nicht konfiguriert oder nicht erreichbar'
                });
            }
            // Lade Anhänge der Störung, falls gewünscht
            let attachments = [];
            if (validatedData.includeAttachments) {
                try {
                    // Hier würden wir die Anhänge aus der Datenbank laden
                    const uploadDir = path.join(process.cwd(), 'uploads', 'stoerungen');
                    const stoerungAttachments = await this.getStoerungAttachments(validatedData.stoerungId);
                    for (const attachment of stoerungAttachments) {
                        const filePath = path.join(uploadDir, attachment.stored_name || attachment.filename);
                        if (fs.existsSync(filePath)) {
                            attachments.push({
                                filename: attachment.filename,
                                path: filePath,
                                contentType: attachment.mime_type || 'application/octet-stream'
                            });
                        }
                    }
                }
                catch (attachmentError) {
                    console.warn('Fehler beim Laden der Störungs-Anhänge:', attachmentError);
                }
            }
            // Sende Störungs-E-Mail
            const success = await this.emailService.sendStoerungEmail(validatedData.to, validatedData.stoerungData, attachments.length > 0 ? attachments : undefined);
            if (success) {
                res.json({
                    success: true,
                    message: 'Störungs-E-Mail erfolgreich gesendet',
                    attachmentCount: attachments.length
                });
            }
            else {
                res.status(500).json({
                    success: false,
                    error: 'Störungs-E-Mail konnte nicht gesendet werden'
                });
            }
        }
        catch (error) {
            console.error('Fehler beim Störungs-E-Mail-Versand:', error);
            if (error instanceof zod_1.z.ZodError) {
                return res.status(400).json({
                    success: false,
                    error: 'Validierungsfehler',
                    details: error.errors
                });
            }
            res.status(500).json({
                success: false,
                error: 'Interner Server-Fehler beim Störungs-E-Mail-Versand'
            });
        }
    }
    /**
     * Überprüft die E-Mail-Konfiguration
     */
    async checkConfig(req, res) {
        try {
            const isConfigured = await this.emailService.verifyConnection();
            res.json({
                success: true,
                configured: isConfigured,
                message: isConfigured
                    ? 'E-Mail-Service ist konfiguriert und erreichbar'
                    : 'E-Mail-Service ist nicht konfiguriert oder nicht erreichbar'
            });
        }
        catch (error) {
            console.error('Fehler bei der Konfigurationsprüfung:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler bei der Konfigurationsprüfung'
            });
        }
    }
    /**
     * Hilfsmethode: Lädt Anhänge einer Störung aus der Datenbank
     */
    async getStoerungAttachments(stoerungId) {
        // Hier würde normalerweise eine Datenbankabfrage stehen
        // Für jetzt geben wir ein leeres Array zurück
        try {
            // TODO: Implementiere Datenbankabfrage für Störungs-Anhänge
            // TODO: Implementierung mit Drizzle ORM nach vollständiger Migration
            // return await db.select().from(stoerungsAttachment).where({
            //   where: { stoerung_id: stoerungId }
            // });
            return [];
        }
        catch (error) {
            console.error('Fehler beim Laden der Störungs-Anhänge:', error);
            return [];
        }
    }
    /**
     * Hilfsmethode: Bestimmt den Content-Type basierend auf der Dateiendung
     */
    getContentType(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const contentTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.txt': 'text/plain'
        };
        return contentTypes[ext] || 'application/octet-stream';
    }
}
exports.EmailController = EmailController;
