"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const stoerungen_controller_1 = require("../controllers/stoerungen.controller");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const router = (0, express_1.Router)();
const controller = new stoerungen_controller_1.StoerungenController();
// Rate limiting for störungen endpoints
const stoerungenLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.NODE_ENV === "development" ? 500 : 100, // Limit each IP
    message: "Too many requests to störungen API from this IP",
    standardHeaders: true,
    legacyHeaders: false,
});
// Apply rate limiting to all routes
router.use(stoerungenLimiter);
// Static routes FIRST (before parametrized routes)
router.get("/kategorien", controller.getStoerungKategorien.bind(controller));
router.get("/stats", controller.getStoerungsStats.bind(controller));
router.get("/active", controller.getActiveStoerungen.bind(controller));
// System status routes
router.get("/system/status", controller.getSystemStatus.bind(controller));
router.post("/system/status", controller.updateSystemStatus.bind(controller));
// Störungen CRUD routes (parametrized routes AFTER static routes)
router.get("/", controller.getStoerungen.bind(controller));
router.get("/:id", controller.getStoerungById.bind(controller));
router.post("/", controller.createStoerung.bind(controller));
router.put("/:id", controller.updateStoerung.bind(controller));
router.delete("/:id", controller.deleteStoerung.bind(controller));
// Comments routes
router.post("/comments", controller.addComment.bind(controller));
// Image upload routes
router.post("/:id/upload-image", stoerungen_controller_1.imageUploadMiddleware, controller.uploadImage.bind(controller));
router.get("/:id/attachments", controller.getAttachments.bind(controller));
router.get("/attachments/:attachmentId/file", controller.getAttachmentFile.bind(controller));
router.delete("/attachments/:attachmentId", controller.deleteAttachment.bind(controller));
exports.default = router;
