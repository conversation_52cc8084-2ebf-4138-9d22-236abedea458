{"id": "32e704e0-a184-40bb-8b43-a474cf34308e", "prevId": "57301cdf-17ef-406b-a42f-cef434b968e9", "version": "7", "dialect": "postgresql", "tables": {"public.ARiL": {"name": "ARiL", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Datum": {"name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "waTaPositionen": {"name": "waTaPositionen", "type": "integer", "primaryKey": false, "notNull": false}, "Umlagerungen": {"name": "Umlagerungen", "type": "integer", "primaryKey": false, "notNull": false}, "belegtePlaetze": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "systemtablareRuecksackStk": {"name": "systemtablareRuecksackStk", "type": "integer", "primaryKey": false, "notNull": false}, "systemtablareGesamtStk": {"name": "systemtablareGesamtStk", "type": "integer", "primaryKey": false, "notNull": false}, "systemtablareEinzelBelegt": {"name": "systemtablareEinzelBelegt", "type": "integer", "primaryKey": false, "notNull": false}, "belegtRinge": {"name": "belegtRinge", "type": "integer", "primaryKey": false, "notNull": false}, "Auslastung": {"name": "Auslastung", "type": "real", "primaryKey": false, "notNull": false}, "alleBewegungen": {"name": "alleBewegungen", "type": "integer", "primaryKey": false, "notNull": false}, "cuttingLagerKunde": {"name": "cuttingLagerKunde", "type": "integer", "primaryKey": false, "notNull": false}, "cuttingLagerRest": {"name": "cuttingLagerRest", "type": "integer", "primaryKey": false, "notNull": false}, "lagerCutting": {"name": "lagerCutting", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"ARiL_datum_idx": {"name": "ARiL_datum_idx", "columns": [{"expression": "Datum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ATrL": {"name": "ATrL", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Datum": {"name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "umlagerungen": {"name": "umlagerungen", "type": "integer", "primaryKey": false, "notNull": false}, "waTaPositionen": {"name": "waTaPositionen", "type": "integer", "primaryKey": false, "notNull": false}, "belegtePlaetze": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "davonSystempaletten": {"name": "davonSystempaletten", "type": "integer", "primaryKey": false, "notNull": false}, "SystempalettenstapelRucksackpaetzen": {"name": "SystempalettenstapelRucksackpaetzen", "type": "integer", "primaryKey": false, "notNull": false}, "SystempalettenstapelEinzel": {"name": "SystempalettenstapelEinzel", "type": "integer", "primaryKey": false, "notNull": false}, "PlaetzeSystempalettenstapelEinzel": {"name": "PlaetzeSystempalettenstapelEinzel", "type": "integer", "primaryKey": false, "notNull": false}, "plaetzeMitTrommelBelegt": {"name": "plaetzeMitTrommelBelegt", "type": "integer", "primaryKey": false, "notNull": false}, "Auslastung": {"name": "Auslastung", "type": "real", "primaryKey": false, "notNull": false}, "Bewegungen": {"name": "Bewegungen", "type": "integer", "primaryKey": false, "notNull": false}, "EinlagerungAblKunde": {"name": "EinlagerungAblKunde", "type": "integer", "primaryKey": false, "notNull": false}, "EinlagerungAblRest": {"name": "EinlagerungAblRest", "type": "integer", "primaryKey": false, "notNull": false}, "AuslagerungAbl": {"name": "AuslagerungAbl", "type": "integer", "primaryKey": false, "notNull": false}, "weAtrl": {"name": "weAtrl", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"ATrL_Datum_idx": {"name": "ATrL_Datum_idx", "columns": [{"expression": "Datum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Ablaengerei": {"name": "Ablaengerei", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Datum": {"name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "cutLagerK220": {"name": "cutLagerK220", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutLagerR220": {"name": "cutLagerR220", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "lagerCut220": {"name": "lagerCut220", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutLagerK240": {"name": "cutLagerK240", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutLagerR240": {"name": "cutLagerR240", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "lagerCut240": {"name": "lagerCut240", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutTT": {"name": "cutTT", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutTR": {"name": "cutTR", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutRR": {"name": "cutRR", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutGesamt": {"name": "cutGesamt", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "pickCut": {"name": "pickCut", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutLager200": {"name": "cutLager200", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cutLagerK200": {"name": "cutLagerK200", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "lagerCut200": {"name": "lagerCut200", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {"Ablaengerei_datum_idx": {"name": "Ablaengerei_datum_idx", "columns": [{"expression": "Datum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bereitschafts_ausnahmen": {"name": "bereitschafts_ausnahmen", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "person_id": {"name": "person_id", "type": "integer", "primaryKey": false, "notNull": true}, "von": {"name": "von", "type": "text", "primaryKey": false, "notNull": true}, "bis": {"name": "bis", "type": "text", "primaryKey": false, "notNull": true}, "grund": {"name": "grund", "type": "text", "primaryKey": false, "notNull": true}, "ersatz_person_id": {"name": "ersatz_person_id", "type": "integer", "primaryKey": false, "notNull": false}, "aktiv": {"name": "aktiv", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bereitschafts_konfiguration": {"name": "bereitschafts_konfiguration", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "wechsel_tag": {"name": "wechsel_tag", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "wechsel_uhrzeit": {"name": "wechsel_uhrzeit", "type": "text", "primaryKey": false, "notNull": true, "default": "'08:00'"}, "rotation_aktiv": {"name": "rotation_aktiv", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "benachrichtigung_tage": {"name": "benachrichtigung_tage", "type": "integer", "primaryKey": false, "notNull": true, "default": 2}, "email_benachrichtigung": {"name": "email_benachrichtigung", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bereitschafts_personen": {"name": "bereitschafts_personen", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "telefon": {"name": "telefon", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "abteilung": {"name": "abteilung", "type": "text", "primaryKey": false, "notNull": true}, "aktiv": {"name": "aktiv", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "reihenfolge": {"name": "reihenfolge", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bereitschafts_wochen": {"name": "bereitschafts_wochen", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "person_id": {"name": "person_id", "type": "integer", "primaryKey": false, "notNull": true}, "wochen_start": {"name": "wochen_start", "type": "text", "primaryKey": false, "notNull": true}, "wochen_ende": {"name": "wochen_ende", "type": "text", "primaryKey": false, "notNull": true}, "von": {"name": "von", "type": "text", "primaryKey": false, "notNull": true}, "bis": {"name": "bis", "type": "text", "primaryKey": false, "notNull": true}, "aktiv": {"name": "aktiv", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "notiz": {"name": "notiz", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bestand200": {"name": "bestand200", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Lagertyp": {"name": "Lagertyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatz": {"name": "Lagerplatz", "type": "text", "primaryKey": false, "notNull": false}, "Material": {"name": "Material", "type": "text", "primaryKey": false, "notNull": false}, "Charge": {"name": "Charge", "type": "text", "primaryKey": false, "notNull": false}, "Dauer": {"name": "<PERSON><PERSON>", "type": "real", "primaryKey": false, "notNull": false}, "Lagerbereich": {"name": "Lagerbereich", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatztyp": {"name": "Lagerplatztyp", "type": "text", "primaryKey": false, "notNull": false}, "Lagerplatzaufteilung": {"name": "Lagerplatzaufteilung", "type": "text", "primaryKey": false, "notNull": false}, "Auslagerungssperre": {"name": "Auslagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Einlagerungssperre": {"name": "Einlagerungssperre", "type": "text", "primaryKey": false, "notNull": false}, "Sperrgrund": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte Bewegung": {"name": "Letzte Bewegung", "type": "text", "primaryKey": false, "notNull": false}, "Uhrzeit": {"name": "Uhrzeit", "type": "text", "primaryKey": false, "notNull": false}, "TA-Nummer": {"name": "TA-Nummer", "type": "text", "primaryKey": false, "notNull": false}, "TA-Position": {"name": "TA-Position", "type": "text", "primaryKey": false, "notNull": false}, "Letzter Änderer": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "Letzte Änderung": {"name": "Letzte Änderung", "type": "text", "primaryKey": false, "notNull": false}, "Wareneingangsdatum": {"name": "Wareneingangsdatum", "type": "text", "primaryKey": false, "notNull": false}, "WE-Nummer": {"name": "WE-<PERSON><PERSON>mer", "type": "text", "primaryKey": false, "notNull": false}, "WE-Position": {"name": "WE-Position", "type": "text", "primaryKey": false, "notNull": false}, "Lieferung": {"name": "Lieferung", "type": "text", "primaryKey": false, "notNull": false}, "Position": {"name": "Position", "type": "text", "primaryKey": false, "notNull": false}, "Lagereinheitentyp": {"name": "Lagereinheitentyp", "type": "text", "primaryKey": false, "notNull": false}, "Gesamtbestand": {"name": "Gesamtbestand", "type": "real", "primaryKey": false, "notNull": false}, "Lagereinheit": {"name": "Lagereinheit", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeDatum": {"name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"name": "import_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"bestand200_import_timestamp_idx": {"name": "bestand200_import_timestamp_idx", "columns": [{"expression": "import_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bestand200_aufnahmeDatum_idx": {"name": "bestand200_aufnahmeDatum_idx", "columns": [{"expression": "aufnahmeDatum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.dispatch_data": {"name": "dispatch_data", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "datum": {"name": "datum", "type": "text", "primaryKey": false, "notNull": false}, "tag": {"name": "tag", "type": "integer", "primaryKey": false, "notNull": false}, "monat": {"name": "monat", "type": "integer", "primaryKey": false, "notNull": false}, "kw": {"name": "kw", "type": "integer", "primaryKey": false, "notNull": false}, "jahr": {"name": "jahr", "type": "integer", "primaryKey": false, "notNull": false}, "servicegrad": {"name": "servicegrad", "type": "real", "primaryKey": false, "notNull": false}, "ausgeliefert_lup": {"name": "ausgeliefert_lup", "type": "integer", "primaryKey": false, "notNull": false}, "rueckstaendig": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": false}, "produzierte_tonnagen": {"name": "produzierte_tonnagen", "type": "real", "primaryKey": false, "notNull": false}, "direktverladung_kiaa": {"name": "direktverladung_kiaa", "type": "integer", "primaryKey": false, "notNull": false}, "umschlag": {"name": "umschlag", "type": "integer", "primaryKey": false, "notNull": false}, "kg_pro_colli": {"name": "kg_pro_colli", "type": "real", "primaryKey": false, "notNull": false}, "elefanten": {"name": "elefanten", "type": "integer", "primaryKey": false, "notNull": false}, "atrl": {"name": "atrl", "type": "integer", "primaryKey": false, "notNull": false}, "aril": {"name": "aril", "type": "integer", "primaryKey": false, "notNull": false}, "fuellgrad_aril": {"name": "fuellgrad_aril", "type": "real", "primaryKey": false, "notNull": false}, "qm_angenommen": {"name": "qm_angenommen", "type": "integer", "primaryKey": false, "notNull": false}, "qm_abgelehnt": {"name": "qm_ab<PERSON><PERSON>nt", "type": "integer", "primaryKey": false, "notNull": false}, "qm_offen": {"name": "qm_offen", "type": "integer", "primaryKey": false, "notNull": false}, "mitarbeiter_std": {"name": "mitarbeiter_std", "type": "real", "primaryKey": false, "notNull": false}}, "indexes": {"dispatch_data_datum_idx": {"name": "dispatch_data_datum_idx", "columns": [{"expression": "datum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.enrichment_performance_metric": {"name": "enrichment_performance_metric", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "enrichment_time": {"name": "enrichment_time", "type": "real", "primaryKey": false, "notNull": true}, "data_sources_used": {"name": "data_sources_used", "type": "integer", "primaryKey": false, "notNull": true}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {"enrichment_performance_metric_timestamp_idx": {"name": "enrichment_performance_metric_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.intent_recognition_metric": {"name": "intent_recognition_metric", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "accuracy": {"name": "accuracy", "type": "real", "primaryKey": false, "notNull": true}, "confidence": {"name": "confidence", "type": "real", "primaryKey": false, "notNull": true}}, "indexes": {"intent_recognition_metric_timestamp_idx": {"name": "intent_recognition_metric_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.maschinen": {"name": "<PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Machine": {"name": "Machine", "type": "text", "primaryKey": false, "notNull": false}, "schnitteProStd": {"name": "schnitteProStd", "type": "real", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"maschinen_Machine_unique": {"name": "maschinen_Machine_unique", "nullsNotDistinct": false, "columns": ["Machine"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.materialdaten": {"name": "materialdaten", "schema": "", "columns": {"matnr": {"name": "matnr", "type": "text", "primaryKey": true, "notNull": true}, "materialkurztext": {"name": "materialkurztext", "type": "text", "primaryKey": false, "notNull": false}, "kabeldurchmesser": {"name": "kabeldurchmesser", "type": "real", "primaryKey": false, "notNull": false}, "zuschlagKabeldurchmesser": {"name": "zuschlagKabeldurchmesser", "type": "real", "primaryKey": false, "notNull": false}, "biegefaktor": {"name": "biegefaktor", "type": "real", "primaryKey": false, "notNull": false}, "kleinsterErlauberFreiraum": {"name": "kleinsterErlauberFreiraum", "type": "real", "primaryKey": false, "notNull": false}, "bruttogewicht": {"name": "bruttog<PERSON><PERSON>t", "type": "real", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.performance_alert": {"name": "performance_alert", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "alert_type": {"name": "alert_type", "type": "text", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "resolved": {"name": "resolved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"performance_alert_timestamp_idx": {"name": "performance_alert_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.query_performance_metric": {"name": "query_performance_metric", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "query_type": {"name": "query_type", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "real", "primaryKey": false, "notNull": true}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true}, "cache_hit": {"name": "cache_hit", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {"query_performance_metric_timestamp_idx": {"name": "query_performance_metric_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.response_time_metric": {"name": "response_time_metric", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "total_time": {"name": "total_time", "type": "real", "primaryKey": false, "notNull": true}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true}, "enriched": {"name": "enriched", "type": "boolean", "primaryKey": false, "notNull": true}, "data_size": {"name": "data_size", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"response_time_metric_timestamp_idx": {"name": "response_time_metric_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Runbook": {"name": "Runbook", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "affected_systems": {"name": "affected_systems", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Runbook_created_at_idx": {"name": "Runbook_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Runbook_updated_at_idx": {"name": "Runbook_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schnitte": {"name": "schnitte", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Datum": {"name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "M5-R-H1": {"name": "M5-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M6-T-H1": {"name": "M6-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M7-R-H1": {"name": "M7-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M8-T-H1": {"name": "M8-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M9-R-H1": {"name": "M9-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M10-T-H1": {"name": "M10-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M11-R-H1": {"name": "M11-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M12-T-H1": {"name": "M12-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M13-R-H1": {"name": "M13-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M14-T-H1": {"name": "M14-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M15-R-H1": {"name": "M15-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M16-T-H1": {"name": "M16-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M17-R-H1": {"name": "M17-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M18-T-H1": {"name": "M18-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M19-T-H1": {"name": "M19-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M20-T-H1": {"name": "M20-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M21-R-H1": {"name": "M21-R-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M23-T-H1": {"name": "M23-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M25-RR-H1": {"name": "M25-RR-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M26-T-H1": {"name": "M26-T-H1", "type": "integer", "primaryKey": false, "notNull": false}, "Sum-H1": {"name": "Sum-H1", "type": "integer", "primaryKey": false, "notNull": false}, "M1-T-H3": {"name": "M1-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M2-T-H3": {"name": "M2-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M3-R-H3": {"name": "M3-R-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M4-T-H3": {"name": "M4-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M22-T-H3": {"name": "M22-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M24-T-H3": {"name": "M24-T-H3", "type": "integer", "primaryKey": false, "notNull": false}, "M27-R-H3": {"name": "M27-R-H3", "type": "integer", "primaryKey": false, "notNull": false}, "Sum-H3": {"name": "Sum-H3", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Stoerungen": {"name": "St<PERSON>rung<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false}, "affected_system": {"name": "affected_system", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "reported_by": {"name": "reported_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}, "resolved_at": {"name": "resolved_at", "type": "text", "primaryKey": false, "notNull": false}, "mttr_minutes": {"name": "mttr_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "resolution_steps": {"name": "resolution_steps", "type": "text", "primaryKey": false, "notNull": false}, "root_cause": {"name": "root_cause", "type": "text", "primaryKey": false, "notNull": false}, "lessons_learned": {"name": "lessons_learned", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.StoerungsComments": {"name": "StoerungsComments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "stoerung_id": {"name": "stoerung_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "caretaker_id": {"name": "caretaker_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.System": {"name": "System", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "datum": {"name": "datum", "type": "timestamp", "primaryKey": false, "notNull": false}, "verfuegbarkeitFTS": {"name": "verfuegbarkeitFTS", "type": "real", "primaryKey": false, "notNull": false}}, "indexes": {"System_datum_idx": {"name": "System_datum_idx", "columns": [{"expression": "datum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.SystemStatus": {"name": "SystemStatus", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "system_name": {"name": "system_name", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "last_check": {"name": "last_check", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.SystemStatusMessage": {"name": "SystemStatusMessage", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "system_status_id": {"name": "system_status_id", "type": "integer", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.trommeldaten": {"name": "trommeldaten", "schema": "", "columns": {"trommeldaten": {"name": "trommeldaten", "type": "text", "primaryKey": true, "notNull": true}, "aussendurchmesser": {"name": "aussendurchmesser", "type": "real", "primaryKey": false, "notNull": false}, "kerndurchmesser": {"name": "kerndurchmesser", "type": "real", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "passwordHash": {"name": "passwordHash", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'user'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true}, "lastLogin": {"name": "lastLogin", "type": "text", "primaryKey": false, "notNull": false}, "loginAttempts": {"name": "loginAttempts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "lockedUntil": {"name": "lockedUntil", "type": "text", "primaryKey": false, "notNull": false}, "resetToken": {"name": "resetToken", "type": "text", "primaryKey": false, "notNull": false}, "resetTokenExpires": {"name": "resetTokenExpires", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "emailVerificationToken": {"name": "emailVerificationToken", "type": "text", "primaryKey": false, "notNull": false}, "twoFactorEnabled": {"name": "twoFactorEnabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "twoFactorSecret": {"name": "twoFactorSecret", "type": "text", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "text", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": false}, "manager": {"name": "manager", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'de'"}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "default": "'light'"}, "notifications": {"name": "notifications", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"User_username_unique": {"name": "User_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "User_email_unique": {"name": "User_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.WE": {"name": "WE", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "Datum": {"name": "Datum", "type": "text", "primaryKey": false, "notNull": false}, "weAtrl": {"name": "weAtrl", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "weManl": {"name": "weManl", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}}, "indexes": {"WE_datum_idx": {"name": "WE_datum_idx", "columns": [{"expression": "Datum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_execution": {"name": "workflow_execution", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"workflow_execution_timestamp_idx": {"name": "workflow_execution_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_log": {"name": "workflow_log", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "level": {"name": "level", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": false}, "execution_id": {"name": "execution_id", "type": "text", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"workflow_log_timestamp_idx": {"name": "workflow_log_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.auslastung200": {"name": "auslastung200", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "aufnahmeDatum": {"name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}, "import_timestamp": {"name": "import_timestamp", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"auslastung200_aufnahmeDatum_idx": {"name": "auslastung200_aufnahmeDatum_idx", "columns": [{"expression": "aufnahmeDatum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "auslastung200_import_timestamp_idx": {"name": "auslastung200_import_timestamp_idx", "columns": [{"expression": "import_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.auslastung240": {"name": "auslastung240", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "aufnahmeDatum": {"name": "aufnahmeDatum", "type": "text", "primaryKey": false, "notNull": false}, "aufnahmeZeit": {"name": "aufnahmeZeit", "type": "text", "primaryKey": false, "notNull": false}, "maxPlaetze": {"name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "auslastung": {"name": "auslastung", "type": "text", "primaryKey": false, "notNull": false}, "maxA": {"name": "maxA", "type": "text", "primaryKey": false, "notNull": false}, "maxB": {"name": "maxB", "type": "text", "primaryKey": false, "notNull": false}, "maxC": {"name": "maxC", "type": "text", "primaryKey": false, "notNull": false}, "auslastungA": {"name": "auslastungA", "type": "text", "primaryKey": false, "notNull": false}, "auslastungB": {"name": "auslastungB", "type": "text", "primaryKey": false, "notNull": false}, "auslastungC": {"name": "auslastungC", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"auslastung240_aufnahmeDatum_idx": {"name": "auslastung240_aufnahmeDatum_idx", "columns": [{"expression": "aufnahmeDatum", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stoerungsattachment": {"name": "stoerungsattachment", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "stoerung_id": {"name": "stoerung_id", "type": "integer", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "stored_name": {"name": "stored_name", "type": "text", "primaryKey": false, "notNull": false}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}