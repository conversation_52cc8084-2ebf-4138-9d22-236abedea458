/**
 * Supplier Repository Interfaces
 *
 * Type definitions for supplier data and performance management
 */

import { Supplier, SupplierPerformanceMetrics } from '@/types/supply-chain-optimization';

export interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  averagePerformanceScore: number;
  topPerformers: number;
  riskSuppliers: number;
}

export interface SupplierPerformanceData {
  supplierId: string;
  onTimeDeliveryRate: number;
  qualityRate: number;
  responseTime: number;
  financialStabilityScore: number;
  reliabilityScore: number;
  costCompetitiveness: number;
  totalOrders: number;
  totalValue: number;
  lastOrderDate: Date;
  performanceTrend: 'improving' | 'stable' | 'declining';
}

export interface SupplierDeliveryHistory {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number;
  deliveryTime: number;
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number;
}

/**
 * Supplier Repository Interface
 * Defines the contract for supplier data operations
 */
export interface ISupplierRepository {
  /**
   * Get all suppliers
   */
  getAll(): Promise<Supplier[]>;

  /**
   * Get overall supplier statistics
   */
  getSupplierStats(): Promise<SupplierStats>;

  /**
   * Get supplier performance data
   */
  getSupplierPerformance(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<SupplierPerformanceData>;

  /**
   * Get all suppliers
   */
  getAllSuppliers(): Promise<Supplier[]>;

  /**
   * Get supplier by ID
   */
  getSupplierById(supplierId: string): Promise<Supplier | null>;

  /**
   * Get suppliers by category
   */
  getSuppliersByCategory(category: string): Promise<Supplier[]>;

  /**
   * Get top performing suppliers
   */
  getTopPerformingSuppliers(limit?: number): Promise<Supplier[]>;

  /**
   * Get suppliers with risk issues
   */
  getRiskSuppliers(): Promise<Supplier[]>;

  /**
   * Update supplier performance metrics
   */
  updateSupplierPerformance(
    supplierId: string,
    metrics: Partial<SupplierPerformanceMetrics>
  ): Promise<void>;

  /**
   * Search suppliers by name or category
   */
  searchSuppliers(query: string): Promise<Supplier[]>;

  /**
   * Get supplier delivery history summary
   */
  getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<SupplierDeliveryHistory[]>;
}