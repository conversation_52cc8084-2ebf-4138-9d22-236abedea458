/**
 * PostgreSQLDocumentService - Document management for PostgreSQL RAG
 *
 * Provides document management functionality using PostgreSQL
 */

import { Client } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import { CreateDocumentRequest } from '../../types/rag.types';

export class PostgreSQLDocumentService {
  private client: Client;

  constructor() {
    this.client = new Client({
      host: 'localhost',
      port: 5434,
      database: 'rag_knowledge',
      user: 'leitstand_dashboard',
      password: 'dashboard_password',
      connectionTimeoutMillis: 5000,
      query_timeout: 10000,
    });

    // Initialize database connection
    this.initializeDatabase().catch(error => {
      console.error('Failed to initialize PostgreSQLDocumentService:', error);
    });
  }

  /**
   * Initialize database connection
   */
  private async initializeDatabase(): Promise<void> {
    try {
      console.log('[PostgreSQLDocumentService] Attempting to connect to database...');
      await this.client.connect();
      console.log('[PostgreSQLDocumentService] Database connected successfully');

      // Test the connection
      const result = await this.client.query('SELECT 1 as test');
      console.log('[PostgreSQLDocumentService] Connection test successful:', result.rows[0]);
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Failed to connect to database:', error);
      throw error;
    }
  }

  /**
   * Store document in database
   */
  async storeDocument(request: CreateDocumentRequest): Promise<string> {
    try {
      console.log(`[PostgreSQLDocumentService] Storing document: ${request.title}`);
      const id = uuidv4();
      const now = new Date().toISOString();
      const contentHash = this.generateContentHash(request.content);

      // Prepare metadata
      const metadata = {
        ...request.metadata,
        uploadedAt: now,
        contentHash,
        knowledgeBaseId: request.knowledgeBaseId || 1
      };

      // Insert document using PostgreSQL
      await this.client.query(
        `INSERT INTO documents (id, title, content, source, source_path, content_type, language, created_at, updated_at, status)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          id,
          request.title,
          request.content,
          request.source || 'upload',
          request.source || '',
          request.contentType || 'text/plain',
          request.language || 'de',
          now,
          now,
          'indexed'
        ]
      );

      // Handle category relationship if provided
      if (request.metadata?.category) {
        try {
          await this.client.query(
            `INSERT INTO categories (id, name, description) VALUES ($1, $2, $3) ON CONFLICT (name) DO NOTHING`,
            [id + '_cat', request.metadata.category, '']
          );
        } catch (error) {
          console.warn('Failed to associate document with category:', error);
        }
      }

      console.log(`[PostgreSQLDocumentService] Document stored successfully: ${id}`);
      return id;
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Error storing document:', error);
      throw error;
    }
  }

  /**
   * Store chunk in database
   */
  async storeChunk(documentId: string, content: string, index: number): Promise<string> {
    try {
      const chunkId = uuidv4();
      const now = new Date().toISOString();
      const tokenCount = Math.ceil(content.length / 4); // Rough estimate

      await this.client.query(
        `INSERT INTO chunks (id, document_id, content, chunk_index, token_count, start_position, end_position, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        [
          chunkId,
          documentId,
          content,
          index,
          tokenCount,
          0, // start_position - would need proper calculation
          content.length, // end_position
          now
        ]
      );

      return chunkId;
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Error storing chunk:', error);
      throw error;
    }
  }

  /**
   * Simple document chunking
   */
  chunkDocument(content: string, maxChunkSize: number = 500): string[] {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence.trim();
      } else {
        currentChunk += (currentChunk.length > 0 ? '. ' : '') + sentence.trim();
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
    }

    return chunks.length > 0 ? chunks : [content]; // Fallback to full content
  }

  /**
   * Get all documents
   */
  async getAllDocuments() {
    try {
      console.log('[PostgreSQLDocumentService] Getting all documents...');
      const result = await this.client.query(
        `SELECT
          d.id,
          d.title,
          d.source as filename,
          d.content_type,
          d.language,
          d.created_at,
          d.updated_at,
          LENGTH(d.content) as fileSize,
          COUNT(DISTINCT c.id) as chunksCount,
          COUNT(DISTINCT e.id) as embeddingsCount,
          COALESCE(cat.name, 'system') as category,
          cat.description
        FROM documents d
        LEFT JOIN chunks c ON d.id = c.document_id
        LEFT JOIN embeddings e ON c.id = e.chunk_id
        LEFT JOIN categories cat ON d.source = cat.name
        WHERE d.status = 'indexed'
        GROUP BY d.id, d.title, d.source, d.content_type, d.language, d.created_at, d.updated_at, cat.name, cat.description
        ORDER BY d.created_at DESC`
      );

      const documents = result.rows.map((row: any) => ({
        id: String(row.id),
        title: String(row.title),
        category: String(row.category) || 'system',
        description: String(row.description || ''),
        language: String(row.language),
        filename: String(row.filename),
        fileSize: Number(row.filesize),
        chunksCount: Number(row.chunkscount),
        embeddingsCount: Number(row.embeddingscount),
        createdAt: String(row.created_at),
        updatedAt: String(row.updated_at)
      }));

      console.log(`[PostgreSQLDocumentService] Retrieved ${documents.length} documents`);
      return documents;
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Error getting documents:', error);
      throw error;
    }
  }

  /**
   * Get chunks for a document
   */
  async getDocumentChunks(documentId: string): Promise<Array<{id: string, content: string, chunk_index: number}>> {
    try {
      const result = await this.client.query(
        `SELECT id, content, chunk_index
         FROM chunks
         WHERE document_id = $1
         ORDER BY chunk_index`,
        [documentId]
      );

      return result.rows.map((row: any) => ({
        id: String(row.id),
        content: String(row.content),
        chunk_index: Number(row.chunk_index)
      }));
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Error getting document chunks:', error);
      throw error;
    }
  }

  /**
   * Delete document chunks
   */
  async deleteDocumentChunks(documentId: string): Promise<number> {
    try {
      const result = await this.client.query(
        'DELETE FROM chunks WHERE document_id = $1',
        [documentId]
      );
      return result.rowCount || 0;
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Error deleting document chunks:', error);
      throw error;
    }
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    try {
      const result = await this.client.query(
        'DELETE FROM documents WHERE id = $1',
        [documentId]
      );
      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error('[PostgreSQLDocumentService] Error deleting document:', error);
      throw error;
    }
  }

  /**
   * Generate content hash for deduplication
   */
  private generateContentHash(content: string): string {
    return crypto.createHash('sha256').update(content.trim()).digest('hex');
  }

  /**
   * Cleanup resources
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.end();
    }
  }
}

export default PostgreSQLDocumentService;