/**
 * Delivery Repository Interfaces
 *
 * Type definitions for delivery and logistics data management
 */

import { DeliveryRequest, DeliveryRoute } from '@/types/supply-chain-optimization';

export interface DeliveryStats {
  totalDeliveries: number;
  onTimeDeliveries: number;
  averageDeliveryTime: number; // days
  totalDistance: number; // km
  totalCost: number; // euros
  activeRoutes: number;
}

export interface DeliveryTimeRangeFilter {
  days: number;
}

export interface DeliveryHistoryRecord {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number; // days
  actualTime: number; // days
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number; // euros
  distance?: number; // km
  route?: string;
  delayReason?: string;
}

export interface DeliveryPerformanceMetrics {
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  delayFrequency: number;
  averageDelay: number;
}

export interface DeliveryTrendData {
  week: Date;
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  totalDistance: number;
  totalCost: number;
}

export interface DelayReasonAnalysis {
  reason: string;
  frequency: number;
  percentage: number;
  averageDelay: number;
  impact: 'high' | 'medium' | 'low';
}

export interface SeasonalPattern {
  season: string;
  months: number[];
  deliveryTimeFactor: number;
  riskFactor: number;
  description: string;
}

/**
 * Delivery Repository Interface
 * Defines the contract for delivery data operations
 */
export interface IDeliveryRepository {
  /**
   * Get all delivery history
   */
  getAll(filter?: DeliveryTimeRangeFilter): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get overall delivery statistics
   */
  getDeliveryStats(): Promise<DeliveryStats>;

  /**
   * Get delivery history for a specific supplier
   */
  getSupplierDeliveryHistory(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get all delivery history within time range
   */
  getDeliveryHistory(timeRange: { days: number }): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get active delivery routes
   */
  getActiveRoutes(): Promise<DeliveryRoute[]>;

  /**
   * Get delivery performance metrics
   */
  getDeliveryPerformanceMetrics(timeRange: { days: number }): Promise<DeliveryPerformanceMetrics>;

  /**
   * Get delivery trends
   */
  getDeliveryTrends(timeRange: { days: number }): Promise<DeliveryTrendData[]>;

  /**
   * Get common delay reasons
   */
  getDelayReasons(timeRange: { days: number }): Promise<DelayReasonAnalysis[]>;

  /**
   * Create new delivery record
   */
  createDelivery(delivery: Partial<DeliveryHistoryRecord>): Promise<string>;

  /**
   * Update delivery status
   */
  updateDeliveryStatus(deliveryId: string, status: string, actualDeliveryTime?: number): Promise<void>;

  /**
   * Get deliveries by route
   */
  getDeliveriesByRoute(routeId: string): Promise<DeliveryHistoryRecord[]>;

  /**
   * Get seasonal delivery patterns
   */
  getSeasonalPatterns(): Promise<SeasonalPattern[]>;
}