"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bereitschaftsRepository = exports.BereitschaftsRepository = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const date_fns_1 = require("date-fns");
class BereitschaftsRepository {
    constructor() {
        this.db = db_1.db;
    }
    // Personen verwalten
    async getAllPersonen() {
        const personen = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.aktiv, true))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.bereitschaftsPersonen.reihenfolge));
        // Fetch bereitschaftsWochen for each person separately
        const personenWithWochen = await Promise.all(personen.map(async (person) => {
            const wochen = await this.db.select().from(schema_1.bereitschaftsWochen)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.personId, person.id), (0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.aktiv, true)))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsWochen.wochenStart))
                .limit(5);
            return { ...person, bereitschaftsWochen: wochen };
        }));
        return personenWithWochen;
    }
    async getPersonById(id) {
        const result = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, id))
            .limit(1);
        const person = result[0];
        if (!person)
            return null;
        const [wochen, ausnahmen] = await Promise.all([
            this.db.select().from(schema_1.bereitschaftsWochen)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.personId, id), (0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.aktiv, true)))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsWochen.wochenStart)),
            this.db.select().from(schema_1.bereitschaftsAusnahmen)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.bereitschaftsAusnahmen.personId, id), (0, drizzle_orm_1.eq)(schema_1.bereitschaftsAusnahmen.aktiv, true)))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsAusnahmen.von))
        ]);
        return {
            ...person,
            bereitschaftsWochen: wochen,
            bereitschaftsAusnahmen: ausnahmen
        };
    }
    async createPerson(data) {
        var _a, _b, _c;
        // Get max reihenfolge
        const maxResult = await this.db.select({
            maxReihenfolge: schema_1.bereitschaftsPersonen.reihenfolge
        })
            .from(schema_1.bereitschaftsPersonen)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsPersonen.reihenfolge))
            .limit(1);
        const maxReihenfolge = (_b = (_a = maxResult[0]) === null || _a === void 0 ? void 0 : _a.maxReihenfolge) !== null && _b !== void 0 ? _b : 0;
        const now = new Date().toISOString();
        const result = await this.db.insert(schema_1.bereitschaftsPersonen)
            .values({
            name: data.name,
            telefon: data.telefon,
            email: data.email,
            abteilung: data.abteilung,
            aktiv: !!data.aktiv,
            reihenfolge: (_c = data.reihenfolge) !== null && _c !== void 0 ? _c : maxReihenfolge + 1,
            createdAt: now,
            updatedAt: now
        })
            .returning();
        return result[0];
    }
    async updatePerson(id, data) {
        const updateData = { updatedAt: new Date().toISOString() };
        if (data.name !== undefined)
            updateData.name = data.name;
        if (data.telefon !== undefined)
            updateData.telefon = data.telefon;
        if (data.email !== undefined)
            updateData.email = data.email;
        if (data.abteilung !== undefined)
            updateData.abteilung = data.abteilung;
        if (data.aktiv !== undefined)
            updateData.aktiv = !!data.aktiv;
        if (data.reihenfolge !== undefined)
            updateData.reihenfolge = data.reihenfolge;
        const result = await this.db.update(schema_1.bereitschaftsPersonen)
            .set(updateData)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, id))
            .returning();
        return result[0];
    }
    async deletePerson(id) {
        const result = await this.db.update(schema_1.bereitschaftsPersonen)
            .set({ aktiv: false, updatedAt: new Date().toISOString() })
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, id))
            .returning();
        return result[0];
    }
    async updatePersonenReihenfolge(personenIds) {
        const updates = personenIds.map(async (id, index) => this.db.update(schema_1.bereitschaftsPersonen)
            .set({ reihenfolge: index + 1 })
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, id))
            .returning());
        return await Promise.all(updates);
    }
    // Wochen verwalten
    async getWochenPlan(startDate, anzahlWochen) {
        const endDate = (0, date_fns_1.addWeeks)(startDate, anzahlWochen);
        // Konvertiere Dates zu ISO-Strings für DATETIME-Vergleich
        const startString = startDate.toISOString();
        const endString = endDate.toISOString();
        const wochen = await this.db.select().from(schema_1.bereitschaftsWochen)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.aktiv, true), (0, drizzle_orm_1.gte)(schema_1.bereitschaftsWochen.wochenStart, startString), (0, drizzle_orm_1.lt)(schema_1.bereitschaftsWochen.wochenStart, endString)))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.bereitschaftsWochen.wochenStart));
        // Fetch person data for each week
        const wochenWithPerson = await Promise.all(wochen.map(async (woche) => {
            const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
                .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, woche.personId))
                .limit(1);
            return { ...woche, person: person[0] || null };
        }));
        return wochenWithPerson;
    }
    // Alle Wochen laden (inklusive inaktive) für vollständige Anzeige
    async getAllWochen(startDate, anzahlWochen) {
        const endDate = (0, date_fns_1.addWeeks)(startDate, anzahlWochen);
        // Konvertiere Dates zu ISO-Strings für DATETIME-Vergleich
        const startString = startDate.toISOString();
        const endString = endDate.toISOString();
        const wochen = await this.db.select().from(schema_1.bereitschaftsWochen)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.bereitschaftsWochen.wochenStart, startString), (0, drizzle_orm_1.lt)(schema_1.bereitschaftsWochen.wochenStart, endString)))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.bereitschaftsWochen.wochenStart));
        // Fetch person data for each week
        const wochenWithPerson = await Promise.all(wochen.map(async (woche) => {
            const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
                .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, woche.personId))
                .limit(1);
            return { ...woche, person: person[0] || null };
        }));
        return wochenWithPerson;
    }
    async getAktuelleBereitschaft() {
        // Konvertiere aktuelles Datum zu ISO-String für DATETIME-Vergleich
        const heute = new Date().toISOString();
        const result = await this.db.select().from(schema_1.bereitschaftsWochen)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.aktiv, true), (0, drizzle_orm_1.lte)(schema_1.bereitschaftsWochen.von, heute), (0, drizzle_orm_1.gt)(schema_1.bereitschaftsWochen.bis, heute)))
            .limit(1);
        const woche = result[0];
        if (!woche)
            return null;
        const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, woche.personId))
            .limit(1);
        return { ...woche, person: person[0] || null };
    }
    async createWoche(data) {
        const now = new Date().toISOString();
        const startDate = new Date(data.wochenStart);
        // Calculate kalenderwoche and jahr from wochenStart
        const kalenderwoche = this.getWeekNumber(startDate);
        const jahr = startDate.getFullYear();
        // Convert Date objects to ISO strings for DATETIME storage
        const wocheData = {
            personId: data.personId,
            wochenStart: data.wochenStart.toISOString(),
            wochenEnde: data.wochenEnde.toISOString(),
            von: data.von.toISOString(),
            bis: data.bis.toISOString(),
            aktiv: !!data.aktiv,
            notiz: data.notiz || null,
            createdAt: now,
            updatedAt: now
        };
        const result = await this.db.insert(schema_1.bereitschaftsWochen)
            .values(wocheData)
            .returning();
        const woche = result[0];
        // Fetch person data
        const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, woche.personId))
            .limit(1);
        return { ...woche, person: person[0] || null };
    }
    // Helper method to calculate week number
    getWeekNumber(date) {
        const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
        const dayNum = d.getUTCDay() || 7;
        d.setUTCDate(d.getUTCDate() + 4 - dayNum);
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
    }
    async updateWoche(id, data) {
        // Convert Date objects to ISO strings for DATETIME storage
        const updateData = { updatedAt: new Date().toISOString() };
        if (data.personId !== undefined)
            updateData.personId = data.personId;
        if (data.wochenStart) {
            updateData.wochenStart = data.wochenStart.toISOString();
        }
        if (data.wochenEnde)
            updateData.wochenEnde = data.wochenEnde.toISOString();
        if (data.von)
            updateData.von = data.von.toISOString();
        if (data.bis)
            updateData.bis = data.bis.toISOString();
        if (data.aktiv !== undefined)
            updateData.aktiv = !!data.aktiv;
        if (data.notiz !== undefined)
            updateData.notiz = data.notiz;
        const result = await this.db.update(schema_1.bereitschaftsWochen)
            .set(updateData)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.id, id))
            .returning();
        const woche = result[0];
        // Fetch person data
        const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, woche.personId))
            .limit(1);
        return { ...woche, person: person[0] || null };
    }
    async deleteWoche(id) {
        const result = await this.db.update(schema_1.bereitschaftsWochen)
            .set({ aktiv: false, updatedAt: new Date().toISOString() })
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.id, id))
            .returning();
        return result[0];
    }
    // Automatische Wochenplanung generieren
    async generiereWochenplan(startDate, anzahlWochen) {
        var _a, _b;
        const personen = await this.getAllPersonen();
        if (personen.length === 0) {
            throw new Error('Keine aktiven Bereitschaftspersonen gefunden');
        }
        const konfiguration = await this.getKonfiguration();
        const wechselTag = (_a = konfiguration === null || konfiguration === void 0 ? void 0 : konfiguration.wechselTag) !== null && _a !== void 0 ? _a : 5; // Freitag
        const wechselUhrzeit = (_b = konfiguration === null || konfiguration === void 0 ? void 0 : konfiguration.wechselUhrzeit) !== null && _b !== void 0 ? _b : '08:00';
        const wochen = [];
        for (let i = 0; i < anzahlWochen; i++) {
            const wochenStart = (0, date_fns_1.addWeeks)((0, date_fns_1.startOfWeek)(startDate, { weekStartsOn: 1 }), i);
            const wochenEnde = (0, date_fns_1.addWeeks)(wochenStart, 1);
            // Berechne Freitag der Woche
            const freitag = (0, date_fns_1.addDays)(wochenStart, wechselTag - 1);
            const naechsterFreitag = (0, date_fns_1.addDays)(freitag, 7);
            // Rotiere durch die Personen
            const personIndex = i % personen.length;
            const person = personen[personIndex];
            wochen.push({
                personId: person.id,
                wochenStart,
                wochenEnde,
                von: freitag,
                bis: naechsterFreitag,
                aktiv: true
            });
        }
        // Lösche bestehende Wochen im Zeitraum
        const startString = (0, date_fns_1.startOfWeek)(startDate, { weekStartsOn: 1 }).toISOString();
        const endString = (0, date_fns_1.addWeeks)((0, date_fns_1.startOfWeek)(startDate, { weekStartsOn: 1 }), anzahlWochen).toISOString();
        await this.db.update(schema_1.bereitschaftsWochen)
            .set({ aktiv: false, updatedAt: new Date().toISOString() })
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.bereitschaftsWochen.wochenStart, startString), (0, drizzle_orm_1.lt)(schema_1.bereitschaftsWochen.wochenStart, endString)));
        // Erstelle neue Wochen
        const erstellteWochen = await Promise.all(wochen.map(woche => this.createWoche(woche)));
        return erstellteWochen;
    }
    // Ausnahmen verwalten
    async getAllAusnahmen() {
        const ausnahmen = await this.db.select().from(schema_1.bereitschaftsAusnahmen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsAusnahmen.aktiv, true))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsAusnahmen.von));
        // Fetch person data for each ausnahme
        const ausnahmenWithPerson = await Promise.all(ausnahmen.map(async (ausnahme) => {
            const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
                .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, ausnahme.personId))
                .limit(1);
            return { ...ausnahme, person: person[0] || null };
        }));
        return ausnahmenWithPerson;
    }
    async createAusnahme(data) {
        const now = new Date().toISOString();
        const result = await this.db.insert(schema_1.bereitschaftsAusnahmen)
            .values({
            personId: data.personId,
            von: data.von.toISOString(),
            bis: data.bis.toISOString(),
            grund: data.grund,
            ersatzPersonId: data.ersatzPersonId || null,
            aktiv: !!data.aktiv,
            createdAt: now,
            updatedAt: now
        })
            .returning();
        const ausnahme = result[0];
        // Fetch person data
        const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, ausnahme.personId))
            .limit(1);
        return { ...ausnahme, person: person[0] || null };
    }
    async updateAusnahme(id, data) {
        const updateData = { updatedAt: new Date().toISOString() };
        if (data.personId !== undefined)
            updateData.personId = data.personId;
        if (data.von !== undefined)
            updateData.von = data.von.toISOString();
        if (data.bis !== undefined)
            updateData.bis = data.bis.toISOString();
        if (data.grund !== undefined)
            updateData.grund = data.grund;
        if (data.ersatzPersonId !== undefined)
            updateData.ersatzPersonId = data.ersatzPersonId;
        if (data.aktiv !== undefined)
            updateData.aktiv = !!data.aktiv;
        const result = await this.db.update(schema_1.bereitschaftsAusnahmen)
            .set(updateData)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsAusnahmen.id, id))
            .returning();
        const ausnahme = result[0];
        // Fetch person data
        const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, ausnahme.personId))
            .limit(1);
        return { ...ausnahme, person: person[0] || null };
    }
    async deleteAusnahme(id) {
        const result = await this.db.update(schema_1.bereitschaftsAusnahmen)
            .set({ aktiv: false, updatedAt: new Date().toISOString() })
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsAusnahmen.id, id))
            .returning();
        return result[0];
    }
    // Konfiguration verwalten
    async getKonfiguration() {
        const result = await this.db.select().from(schema_1.bereitschaftsKonfiguration)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsKonfiguration.createdAt))
            .limit(1);
        const config = result[0];
        // Standardkonfiguration falls keine existiert
        if (!config) {
            return await this.createKonfiguration({});
        }
        // Return config with proper field mapping
        return {
            id: config.id,
            wechselTag: config.wechselTag,
            wechselUhrzeit: config.wechselUhrzeit,
            rotationAktiv: !!config.rotationAktiv,
            benachrichtigungTage: config.benachrichtigungTage,
            emailBenachrichtigung: !!config.emailBenachrichtigung,
            createdAt: config.createdAt,
            updatedAt: config.updatedAt
        };
    }
    async createKonfiguration(data) {
        var _a, _b, _c;
        const now = new Date().toISOString();
        const result = await this.db.insert(schema_1.bereitschaftsKonfiguration)
            .values({
            wechselTag: (_a = data.wechselTag) !== null && _a !== void 0 ? _a : 5,
            wechselUhrzeit: (_b = data.wechselUhrzeit) !== null && _b !== void 0 ? _b : '08:00',
            rotationAktiv: !!data.rotationAktiv,
            benachrichtigungTage: (_c = data.benachrichtigungTage) !== null && _c !== void 0 ? _c : 2,
            emailBenachrichtigung: !!data.emailBenachrichtigung,
            createdAt: now,
            updatedAt: now
        })
            .returning();
        const created = result[0];
        // Return mapped result
        return {
            id: created.id,
            wechselTag: created.wechselTag,
            wechselUhrzeit: created.wechselUhrzeit,
            rotationAktiv: !!created.rotationAktiv,
            benachrichtigungTage: created.benachrichtigungTage,
            emailBenachrichtigung: !!created.emailBenachrichtigung,
            createdAt: created.createdAt,
            updatedAt: created.updatedAt
        };
    }
    async updateKonfiguration(data) {
        const existingResult = await this.db.select().from(schema_1.bereitschaftsKonfiguration)
            .orderBy((0, drizzle_orm_1.desc)(schema_1.bereitschaftsKonfiguration.createdAt))
            .limit(1);
        const existingConfig = existingResult[0];
        if (!existingConfig) {
            throw new Error('Keine Konfiguration gefunden');
        }
        // Map frontend fields to database fields
        const updateData = { updatedAt: new Date().toISOString() };
        if (data.wechselTag !== undefined)
            updateData.wechselTag = data.wechselTag;
        if (data.wechselUhrzeit !== undefined)
            updateData.wechselUhrzeit = data.wechselUhrzeit;
        if (data.rotationAktiv !== undefined)
            updateData.rotationAktiv = data.rotationAktiv ? 1 : 0;
        if (data.benachrichtigungTage !== undefined)
            updateData.benachrichtigungTage = data.benachrichtigungTage;
        if (data.emailBenachrichtigung !== undefined)
            updateData.emailBenachrichtigung = data.emailBenachrichtigung ? 1 : 0;
        const result = await this.db.update(schema_1.bereitschaftsKonfiguration)
            .set(updateData)
            .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsKonfiguration.id, existingConfig.id))
            .returning();
        const updated = result[0];
        // Return mapped result
        return {
            id: updated.id,
            wechselTag: updated.wechselTag,
            wechselUhrzeit: updated.wechselUhrzeit,
            rotationAktiv: !!updated.rotationAktiv,
            benachrichtigungTage: updated.benachrichtigungTage,
            emailBenachrichtigung: !!updated.emailBenachrichtigung,
            createdAt: updated.createdAt,
            updatedAt: updated.updatedAt
        };
    }
    // Hilfsmethoden
    async getPersonenInZeitraum(von, bis) {
        // Konvertiere Dates zu ISO-Strings für DATETIME-Vergleich in der Datenbank
        const vonString = von.toISOString();
        const bisString = bis.toISOString();
        const wochen = await this.db.select().from(schema_1.bereitschaftsWochen)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.bereitschaftsWochen.aktiv, true), (0, drizzle_orm_1.or)((0, drizzle_orm_1.and)((0, drizzle_orm_1.lte)(schema_1.bereitschaftsWochen.von, bisString), (0, drizzle_orm_1.gte)(schema_1.bereitschaftsWochen.bis, vonString)))))
            .orderBy((0, drizzle_orm_1.asc)(schema_1.bereitschaftsWochen.von));
        // Fetch person data for each woche
        const wochenWithPerson = await Promise.all(wochen.map(async (woche) => {
            const person = await this.db.select().from(schema_1.bereitschaftsPersonen)
                .where((0, drizzle_orm_1.eq)(schema_1.bereitschaftsPersonen.id, woche.personId))
                .limit(1);
            return { ...woche, person: person[0] || null };
        }));
        return wochenWithPerson;
    }
    async validateWochenplan(wochen) {
        const errors = [];
        // Prüfe auf Überschneidungen
        for (let i = 0; i < wochen.length; i++) {
            for (let j = i + 1; j < wochen.length; j++) {
                const woche1 = wochen[i];
                const woche2 = wochen[j];
                if (woche1.von < woche2.bis && woche1.bis > woche2.von) {
                    errors.push(`Überschneidung zwischen Woche ${i + 1} und ${j + 1}`);
                }
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.BereitschaftsRepository = BereitschaftsRepository;
exports.bereitschaftsRepository = new BereitschaftsRepository();
