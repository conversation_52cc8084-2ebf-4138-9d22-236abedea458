/**
 * User Repository Interface
 *
 * Definiert die Schnittstelle für User-Datenoperationen
 */

import { UserRepository } from '../user.repository';

export interface UserRepositoryInterface {
  findByEmailOrUsername(email: string, username: string): Promise<any | null>;
  findByEmail(email: string): Promise<any | null>;
  findByUsername(username: string): Promise<any | null>;
  createUser(userData: any): Promise<any>;
  findById(id: number): Promise<any | null>;
}

export { UserRepository };