/**
 * Repository Interfaces Export
 * 
 * Zentrale Exportdatei für alle Repository-Interfaces
 */

// Base Repository Interface
export * from './base.repository.interface';

// Domain-specific Repository Interfaces
export * from './dispatch.repository.interface';
export * from './warehouse.repository.interface';
export * from './cutting.repository.interface';
export * from './delivery.repository.interface';
export * from './production.repository.interface';
export * from './supplier.repository.interface';

// Repository Factory Interface
export interface RepositoryFactory {
  dispatch(): DispatchRepository;
  warehouse(): WarehouseRepository;
  cutting(): CuttingRepository;
  delivery(): DeliveryRepository;
  production(): ProductionRepository;
  supplier(): SupplierRepository;
  system(): ISystemRepository;
  user(): UserRepositoryInterface;
}

// Import types for re-export
import { DispatchRepository } from './dispatch.repository.interface';
import { WarehouseRepository } from './warehouse.repository.interface';
import { CuttingRepository } from './cutting.repository.interface';
import { DeliveryRepository } from './delivery.repository.interface';
import { ProductionRepository } from './production.repository.interface';
import { SupplierRepository } from './supplier.repository.interface';
import { ISystemRepository } from './system.interfaces';
import { UserRepository, UserRepositoryInterface } from './user.repository.interface';