"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRepository = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
class UserRepository {
    constructor() {
        this.db = db_1.db;
        // Drizzle DB wird direkt importiert
    }
    async findByEmailOrUsername(email, username) {
        const result = await this.db.select().from(schema_1.user)
            .where((0, drizzle_orm_1.or)((0, drizzle_orm_1.eq)(schema_1.user.email, email), (0, drizzle_orm_1.eq)(schema_1.user.username, username)))
            .limit(1);
        return result[0] || null;
    }
    async findByEmail(email) {
        const result = await this.db.select().from(schema_1.user)
            .where((0, drizzle_orm_1.eq)(schema_1.user.email, email))
            .limit(1);
        return result[0] || null;
    }
    async findByUsername(username) {
        const result = await this.db.select().from(schema_1.user)
            .where((0, drizzle_orm_1.eq)(schema_1.user.username, username))
            .limit(1);
        return result[0] || null;
    }
    async createUser(userData) {
        const result = await this.db.insert(schema_1.user).values({
            email: userData.email,
            username: userData.username,
            firstName: userData.firstName,
            lastName: userData.lastName,
            password: userData.passwordHash,
            passwordHash: userData.passwordHash,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }).returning();
        return result[0];
    }
    async findById(id) {
        const result = await this.db.select().from(schema_1.user)
            .where((0, drizzle_orm_1.eq)(schema_1.user.id, id))
            .limit(1);
        return result[0] || null;
    }
}
exports.UserRepository = UserRepository;
