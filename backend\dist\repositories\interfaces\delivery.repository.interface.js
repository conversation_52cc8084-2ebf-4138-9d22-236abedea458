"use strict";
/**
 * Delivery Repository Interface
 *
 * Definiert die Schnittstelle für Delivery-Datenoperationen
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryRepositoryImpl = void 0;
const delivery_repository_1 = require("../delivery.repository");
Object.defineProperty(exports, "DeliveryRepositoryImpl", { enumerable: true, get: function () { return delivery_repository_1.DeliveryRepositoryImpl; } });
