/**
 * Error Handling Decorators for AI Services
 * Provides method-level error handling and logging
 */

import { AIErrorHandler } from '../services/error-handling/AIErrorHandler';
import { AIOperationLogger } from '../services/error-handling/AIOperationLogger';
import { AIServiceErrorCode } from '../types/errors';

/**
 * Decorator for automatic error handling and logging of AI service methods
 */
export function withErrorHandling(
  serviceName: string,
  operationType: 'query' | 'optimization' | 'prediction' | 'analysis' | 'configuration' = 'query',
  fallbackValue?: any
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const errorHandler = AIErrorHandler.getInstance();
      const logger = AIOperationLogger.getInstance();
      
      const operationId = logger.startOperation(
        serviceName,
        propertyName,
        operationType,
        { args: args.length > 0 ? args[0] : undefined }
      );

      try {
        const result = await method.apply(this, args);
        logger.completeOperation(operationId, true, { result });
        return result;

      } catch (error) {
        // Create structured AI error
        const aiError = errorHandler.createError(
          AIServiceErrorCode.SERVICE_UNAVAILABLE, // Default, can be overridden
          serviceName,
          propertyName,
          error as Error,
          { args: args.length > 0 ? args[0] : undefined }
        );

        logger.completeOperation(operationId, false, undefined, aiError);

        // Attempt error handling and recovery
        const recoveredResult = await errorHandler.handleError(
          aiError,
          args.length > 0 ? args[0] : undefined,
          serviceName
        );

        if (recoveredResult !== null) {
          return recoveredResult;
        }

        // Return fallback value if provided
        if (fallbackValue !== undefined) {
          return fallbackValue;
        }

        // Re-throw the structured error
        throw aiError;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator for retry logic with exponential backoff
 */
export function withRetry(
  maxRetries: number = 3,
  baseDelay: number = 1000,
  exponentialBase: number = 2
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      let lastError: any;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await method.apply(this, args);
        } catch (error) {
          lastError = error;
          
          if (attempt === maxRetries) {
            break;
          }

          // Calculate delay with exponential backoff
          const delay = baseDelay * Math.pow(exponentialBase, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
          
          console.warn(`[Retry] Attempt ${attempt} failed for ${propertyName}, retrying in ${delay}ms`);
        }
      }

      throw lastError;
    };

    return descriptor;
  };
}

/**
 * Decorator for caching results with error handling
 */
export function withCache(
  ttlMs: number = 300000, // 5 minutes default
  keyGenerator?: (...args: any[]) => string
) {
  const cache = new Map<string, { value: any; expiry: number }>();

  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // Generate cache key
      const cacheKey = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyName}.${JSON.stringify(args)}`;

      // Check cache
      const cached = cache.get(cacheKey);
      if (cached && cached.expiry > Date.now()) {
        return cached.value;
      }

      try {
        const result = await method.apply(this, args);
        
        // Cache successful result
        cache.set(cacheKey, {
          value: result,
          expiry: Date.now() + ttlMs
        });

        return result;

      } catch (error) {
        // Don't cache errors, but clean up expired entries
        target.cleanupExpiredCache(cache);
        throw error;
      }
    };

    // Add cache cleanup method
    if (!target.cleanupExpiredCache) {
      target.cleanupExpiredCache = function (cacheMap: Map<string, any>) {
        const now = Date.now();
        for (const [key, entry] of cacheMap.entries()) {
          if (entry.expiry <= now) {
            cacheMap.delete(key);
          }
        }
      };
    }

    return descriptor;
  };
}

/**
 * Decorator for input validation
 */
export function withValidation(validator: (args: any[]) => boolean | string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const validationResult = validator(args);
      
      if (validationResult !== true) {
        const errorHandler = AIErrorHandler.getInstance();
        const errorMessage = typeof validationResult === 'string' 
          ? validationResult 
          : 'Invalid input parameters';
        
        const error = errorHandler.createError(
          AIServiceErrorCode.INVALID_PARAMETERS,
          target.constructor.name,
          propertyName,
          new Error(errorMessage),
          { args }
        );

        throw error;
      }

      return await method.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * Decorator for performance monitoring
 */
export function withPerformanceMonitoring(
  warningThresholdMs: number = 5000,
  errorThresholdMs: number = 30000
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;

        if (duration > errorThresholdMs) {
          console.error(`[Performance] ${target.constructor.name}.${propertyName} took ${duration}ms (ERROR threshold: ${errorThresholdMs}ms)`);
        } else if (duration > warningThresholdMs) {
          console.warn(`[Performance] ${target.constructor.name}.${propertyName} took ${duration}ms (WARNING threshold: ${warningThresholdMs}ms)`);
        }

        return result;

      } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`[Performance] ${target.constructor.name}.${propertyName} failed after ${duration}ms`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Composite decorator that combines error handling, retry, and performance monitoring
 */
export function withAIServiceProtection(
  serviceName: string,
  operationType: 'query' | 'optimization' | 'prediction' | 'analysis' | 'configuration' = 'query',
  options: {
    maxRetries?: number;
    retryDelay?: number;
    performanceWarningMs?: number;
    performanceErrorMs?: number;
    fallbackValue?: any;
  } = {}
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    // Apply decorators in order
    withPerformanceMonitoring(
      options.performanceWarningMs || 5000,
      options.performanceErrorMs || 30000
    )(target, propertyName, descriptor);

    withRetry(
      options.maxRetries || 3,
      options.retryDelay || 1000
    )(target, propertyName, descriptor);

    withErrorHandling(
      serviceName,
      operationType,
      options.fallbackValue
    )(target, propertyName, descriptor);

    return descriptor;
  };
}