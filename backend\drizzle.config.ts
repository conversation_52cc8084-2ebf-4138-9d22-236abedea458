import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

/**
 * Drizzle Kit Konfiguration für PostgreSQL
 *
 * Diese Konfiguration verwendet PostgreSQL für die zentrale Datenbank.
 *
 * Dokumentation: https://orm.drizzle.team/docs/get-started/postgresql
 */
export default defineConfig({
  // PostgreSQL-Dialekt
  dialect: 'postgresql',
  
  // Pfad zur Schema-Datei
  schema: './src/db/schema.ts',
  
  // Ausgabeverzeichnis für Migrationen
  out: './drizzle',
  
  // Datenbankverbindungseinstellungen
  dbCredentials: {
    // PostgreSQL-Verbindungsdaten - korrigiert für bereitschafts-Tabellen
    host: process.env.DB_HOST || 'localhost',
    port: Number(process.env.DB_PORT) || 5434, // Port 5434 für leitstand_dashboard
    user: process.env.DB_USER || 'leitstand_dashboard',
    password: process.env.DB_PASSWORD || 'dashboard_password',
    database: process.env.DB_NAME || 'leitstand_dashboard', // Korrigierte Datenbank
    ssl: false, // SSL deaktivieren für lokale Entwicklung
  },
  
  // Erweiterte Konfigurationsoptionen
  breakpoints: true,  // Aktiviert Breakpoints in Migrationen
  verbose: true,      // Detaillierte Ausgabe
  strict: true,       // Strenge Validierung
});