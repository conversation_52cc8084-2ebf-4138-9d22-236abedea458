import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StoerungsKpiCards } from './StoerungsKpiCards';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, RadialBarChart, RadialBar, PolarAngleAxis } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '@/components/ui/chart';
import { DateRange } from 'react-day-picker';
import { BarChart3, TrendingUp, AlertTriangle, Clock, ShieldCheck, Timer } from 'lucide-react';
import { SEVERITY_COLORS, Stoerung, StoerungsStats, StoerungStatus } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { motion } from 'framer-motion';

interface StoerungsStatsProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

interface StoerungsStatsChartProps extends StoerungsStatsProps { }

interface StoerungsKpiCardsProps extends StoerungsStatsProps { }

// Custom hook to share data logic between components
const useStoerungsStats = (dateRange?: DateRange, refreshKey: number = 0) => {
  const [stats, setStats] = useState<StoerungsStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAndCalculateStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const stoerungen = await stoerungenService.getStoerungen();

        let filteredStoerungen = Array.isArray(stoerungen) ? stoerungen : [];

        if (dateRange?.from && dateRange?.to) {
          filteredStoerungen = filteredStoerungen.filter(stoerung => {
            const createdDate = new Date(stoerung.created_at);
            return createdDate >= dateRange.from! && createdDate <= dateRange.to!;
          });
        }

        if (filteredStoerungen.length === 0) {
          setStats({ total: 0, active: 0, resolved: 0, critical: 0, high: 0, medium: 0, low: 0, avg_mttr_minutes: 0, avg_mtta_minutes: 0, first_time_fix_rate: 0, resolution_rate_24h: 0 });
          return;
        }

        const total = filteredStoerungen.length;
        const active = filteredStoerungen.filter(s => (s.status as any) !== 'GELÖST' && (s.status as any) !== 'CLOSED').length;
        const resolved = total - active;

        const critical = filteredStoerungen.filter(s => s.severity === 'CRITICAL').length;
        const high = filteredStoerungen.filter(s => s.severity === 'HIGH').length;
        const medium = filteredStoerungen.filter(s => s.severity === 'MEDIUM').length;
        const low = filteredStoerungen.filter(s => s.severity === 'LOW').length;

        const resolvedWithMTTR = filteredStoerungen.filter(s => (s.status as any) === 'GELÖST' && s.mttr_minutes != null && s.mttr_minutes > 0);
        const avg_mttr_minutes = resolvedWithMTTR.length > 0 ? Math.round(resolvedWithMTTR.reduce((sum, s) => sum + s.mttr_minutes!, 0) / resolvedWithMTTR.length) : 0;

        const acknowledgedWithMTTA = filteredStoerungen.filter(s => s.mtta_minutes != null && s.mtta_minutes > 0);
        const avg_mtta_minutes = acknowledgedWithMTTA.length > 0 ? Math.round(acknowledgedWithMTTA.reduce((sum, s) => sum + s.mtta_minutes!, 0) / acknowledgedWithMTTA.length) : 0;

        const resolvedWithoutEscalation = resolvedWithMTTR.filter(s => !s.escalation_level || s.escalation_level === 'L1');
        const first_time_fix_rate = resolvedWithMTTR.length > 0 ? Math.round((resolvedWithoutEscalation.length / resolvedWithMTTR.length) * 100) : 0;

        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const resolution_rate_24h = filteredStoerungen.filter(s => (s.status as any) === 'GELÖST' && s.resolved_at && new Date(s.resolved_at) >= yesterday).length;

        setStats({ total, active, resolved, critical, high, medium, low, avg_mttr_minutes, avg_mtta_minutes, first_time_fix_rate, resolution_rate_24h });

      } catch (err) {
        console.error('Error fetching or calculating stats:', err);
        setError('Fehler beim Laden der Statistiken');
      } finally {
        setLoading(false);
      }
    };

    fetchAndCalculateStats();
  }, [refreshKey, dateRange]);

  return { stats, loading, error };
};

// KPI Cards Component
const StoerungsKpiCardsWrapper: React.FC<{ dateRange?: DateRange; refreshKey?: number }> = ({
  dateRange,
  refreshKey = 0
}) => {
  const { stats, loading, error } = useStoerungsStats(dateRange, refreshKey);

  if (loading) {
    return <StoerungsKpiCards 
      stats={{
        total: 0,
        active: 0,
        resolved: 0,
        avg_mttr_minutes: 0,
        avg_mtta_minutes: 0,
        first_time_fix_rate: 0
      }} 
      className="opacity-50 pointer-events-none"
    />;
  }

  if (!stats) {
    return null;
  }

  return <StoerungsKpiCards stats={stats} />;
};

StoerungsKpiCards.displayName = 'StoerungsKpiCards';

// Severity Distribution Chart Component (Radial Chart)
const SeverityDistributionChart: React.FC<StoerungsStatsChartProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const { stats, loading, error } = useStoerungsStats(dateRange, refreshKey);

  if (loading) {
    return (
      <Card className="h-[300px] border border-slate-200 bg-slate-50 transition-all duration-300 hover:border-slate-300 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <BarChart3 className="h-4 w-4" />
            Schweregrad Verteilung
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-row items-center p-4 h-[220px] justify-between">
          <div className="animate-pulse">
            <div className="h-[180px] w-[180px] bg-gray-200 rounded-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card className="h-[300px] border border-slate-200 bg-slate-50 transition-all duration-300 hover:border-slate-300 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <BarChart3 className="h-4 w-4" />
            Schweregrad Verteilung
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-row items-center p-4 h-[220px] justify-between">
          <div className="text-center text-gray-500">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Keine Daten verfügbar</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const severityData = [
    { name: 'Kritisch', value: stats.critical, fill: SEVERITY_COLORS.CRITICAL },
    { name: 'Hoch', value: stats.high, fill: SEVERITY_COLORS.HIGH },
    { name: 'Mittel', value: stats.medium, fill: SEVERITY_COLORS.MEDIUM },
    { name: 'Niedrig', value: stats.low, fill: SEVERITY_COLORS.LOW },
  ].filter(item => item.value > 0);

  const chartConfig = {
    critical: { label: "Kritisch", color: SEVERITY_COLORS.CRITICAL },
    high: { label: "Hoch", color: SEVERITY_COLORS.HIGH },
    medium: { label: "Mittel", color: SEVERITY_COLORS.MEDIUM },
    low: { label: "Niedrig", color: SEVERITY_COLORS.LOW },
  };

  return (
    <Card className="h-[300px] border border-slate-200 bg-slate-50 transition-all duration-300 hover:border-slate-300 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <BarChart3 className="h-4 w-4" />
          Schweregrad Verteilung
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-row items-center p-4 h-[220px] justify-between">
        {/* Legende links */}
        <div className="flex flex-col gap-1 min-w-[100px]">
          {severityData.map((entry, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-sm border border-gray-300"
                style={{ backgroundColor: entry.fill }}
              />
              <span className="text-xs text-gray-700">
                {entry.name}
              </span>
            </div>
          ))}
        </div>

        {/* Chart rechts */}
        <div className="flex-1 flex justify-center items-center">
          <ChartContainer config={chartConfig} className="h-[180px] w-[180px]">
            <RadialBarChart
              width={180}
              height={180}
              cx="50%"
              cy="50%"
              innerRadius="20%"
              outerRadius="90%"
              data={severityData}
              margin={{ top: 5, right: 5, bottom: 5, left: 5 }}
            >
              <RadialBar
                dataKey="value"
                cornerRadius={4}
                stroke="#000"
                strokeWidth={2}
              >
                {severityData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </RadialBar>
              <PolarAngleAxis
                type="number"
                domain={[0, Math.max(...severityData.map(d => d.value), 1)]}
                tick={false}
              />
              <ChartTooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload;
                    return (
                      <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
                        <div className="flex items-center gap-2 mb-1">
                          <div
                            className="w-3 h-3 rounded-sm"
                            style={{ backgroundColor: data.fill }}
                          />
                          <p className="font-medium text-sm">{data.name}</p>
                        </div>
                        <p className="text-sm text-gray-600">{data.value} Störungen</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
            </RadialBarChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
});

SeverityDistributionChart.displayName = 'SeverityDistributionChart';

// Status Distribution Chart Component (Pie Chart)
const StatusDistributionChart: React.FC<StoerungsStatsChartProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const { stats, loading, error } = useStoerungsStats(dateRange, refreshKey);

  if (loading) {
    return (
      <Card className="h-[300px] border border-slate-200 bg-slate-50 transition-all duration-300 hover:border-slate-300 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <TrendingUp className="h-4 w-4" />
            Status Verteilung
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-row items-center p-4 h-[220px] justify-between">
          <div className="animate-pulse">
            <div className="h-[180px] w-[180px] bg-gray-200 rounded-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card className="h-[300px] border border-slate-200 bg-slate-50 transition-all duration-300 hover:border-slate-300 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <TrendingUp className="h-4 w-4" />
            Status Verteilung
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-row items-center p-4 h-[220px] justify-between">
          <div className="text-center text-gray-500">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Keine Daten verfügbar</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const statusData = [
    { name: 'Aktiv', value: stats.active, fill: '#ef4444' }, // red-500
    { name: 'Gelöst', value: stats.resolved, fill: '#22c55e' }, // green-500
  ].filter(item => item.value > 0);

  const chartConfig = {
    active: { label: "Aktiv", color: "#ef4444" },
    resolved: { label: "Gelöst", color: "#22c55e" },
  };

  return (
    <Card className="h-[300px] border border-slate-200 bg-slate-50 transition-all duration-300 hover:border-slate-300 shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <TrendingUp className="h-4 w-4" />
          Status Verteilung
        </CardTitle>
        <div className="text-xs text-gray-600">
          Aktive vs. gelöste Störungen
        </div>
      </CardHeader>
      <CardContent className="flex flex-row items-center p-4 h-[220px] justify-between">
        {/* Legende links */}
        <div className="flex flex-col gap-1 min-w-[100px]">
          {statusData.map((entry, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-sm border border-gray-300"
                style={{ backgroundColor: entry.fill }}
              />
              <span className="text-xs text-gray-700">
                {entry.name}
              </span>
            </div>
          ))}
        </div>

        {/* Chart rechts */}
        <div className="flex-1 flex justify-center items-center">
          <ChartContainer config={chartConfig} className="h-[180px] w-[180px]">
            <PieChart
              width={180}
              height={180}
              margin={{ top: 5, right: 5, bottom: 5, left: 5 }}
            >
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                innerRadius={35}
                outerRadius={75}
                paddingAngle={2}
                stroke="#000"
                strokeWidth={2}
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
              <ChartTooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload;
                    const total = statusData.reduce((sum, item) => sum + item.value, 0);
                    const percentage = total > 0 ? ((data.value / total) * 100).toFixed(1) : '0';
                    return (
                      <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
                        <div className="flex items-center gap-2 mb-1">
                          <div
                            className="w-3 h-3 rounded-sm"
                            style={{ backgroundColor: data.fill }}
                          />
                          <p className="font-medium text-sm">{data.name}</p>
                        </div>
                        <p className="text-sm text-gray-600">{data.value} Störungen ({percentage}%)</p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
            </PieChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
});

StatusDistributionChart.displayName = 'StatusDistributionChart';

export { SeverityDistributionChart, StatusDistributionChart, StoerungsKpiCardsWrapper };
