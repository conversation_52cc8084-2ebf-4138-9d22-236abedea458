import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import AreaChartStacked from "@/modules/dashboard/components/charts/ServiceGradChart";
import { DailyPerformanceChart } from "@/modules/dashboard/components/charts/LieferpositionenChart";
import { PickingChart } from "@/modules/dashboard/components/charts/PickingChart";
import { ReturnsChart } from "@/modules/dashboard/components/charts/QMeldungenChart";
import { TagesleistungChart } from "@/modules/dashboard/components/charts/TagesleistungChart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Truck } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import dispatchIcon from "@/assets/iconDelivery.png";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";
/**
 * Dispatch-Dashboard mit sauberen, wiederverwendbaren Komponenten
 * 
 * Alle zeitbasierten Charts werden automatisch basierend auf dem 
 * ausgewählten Datumsbereich gefiltert. Die Charts reagieren 
 * auf Änderungen des dateRange-States und aktualisieren ihre Daten entsprechend.
 */

export default function DispatchPage() {
  // Zustand für den ausgewählten Datumsbereich - Verwende bekannten Datenbereich basierend auf verfügbaren Daten
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 6, 4), // 4. Juli 2025 - basierend auf verfügbaren Backend-Daten
    to: new Date(2025, 6, 31), // 31. Juli 2025 - basierend auf verfügbaren Backend-Daten
  });

  return ( 
    <div className="p-6 md:p-6">
      
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <img
            src={dispatchIcon}
            alt="Versand"
            className="h-11 w-11 mr-0 object-contain"
          />
          <h1 className="text-4xl font-bold text-black">VERSAND</h1>
          <div className="-mt-4">
            <AskJaszButton
              context={createPageContext(
                "Versand Dashboard",
                [
                  "Versand-Bereich Performance Übersicht",
                  "Servicegrad und Lieferpünktlichkeit",
                  "Picking Performance und Tagesleistung",
                  "Q-Meldungen und Retouren Analyse",
                  "Lieferpositionen und Kapazitätsplanung"
                ],
                "Versand Bereich Dashboard"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum Versand Dashboard erhalten"
            />
          </div>
        </div>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Zeitraum: 04.07.2025 - 31.07.2025"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-2">
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <AreaChartStacked dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>

          <div className="xl:col-span-2 gap-2">
            <ChartErrorBoundary>
              <TagesleistungChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4 gap-2">
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <DailyPerformanceChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
            
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <ReturnsChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
            
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <PickingChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>    

  )
};
