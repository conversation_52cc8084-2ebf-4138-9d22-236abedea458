"use client"

import React, { useEffect, useState, memo, useRef } from "react"
import { TrendingUp, TrendingDown } from "lucide-react"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, Bar, Line } from "recharts"
import { DateRange } from "react-day-picker"
import apiService from "@/services/api.service"
import { Badge } from "@/components/ui/badge"
import { useSpring, useMotionValueEvent } from "motion/react"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "@/components/ui/chart"

// Interface für die ServiceLevel-Daten aus der Datenbank
interface ServiceLevelDbRow {
  datum: string;
  servicegrad: number;
}

// Props für die AreaChartStacked Komponente
interface AreaChartStackedProps {
  dateRange?: DateRange;
}

// Definition des Datentyps für Chart-Daten
interface ChartDataPoint {
  date: string;
  servicegrad: number;
}

const chartConfig = {
  servicegrad: {
    label: "Servicegrad",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig

const AreaChartStacked = React.forwardRef<HTMLDivElement, AreaChartStackedProps>(function AreaChartStacked({ dateRange }, ref) {
  const chartRef = useRef<HTMLDivElement>(null);
  const [axis, setAxis] = useState(0);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Setze die initale Position der Linie auf die rechte Seite nach dem Rendern
  useEffect(() => {
    if (chartRef.current && chartData.length > 0) {
      const chartWidth = chartRef.current.getBoundingClientRect().width;
      setAxis(chartWidth);
      springX.set(chartWidth);
    }
  }, [chartData]); // Nur abhängig von chartData, nicht von axis

  // motion values
  const springX = useSpring(0, {
    damping: 30,
    stiffness: 100,
  });
  const springY = useSpring(0, {
    damping: 30,
    stiffness: 100,
  });

  useMotionValueEvent(springX, "change", (latest) => {
    setAxis(latest);
  });

  // Lade Daten aus der Datenbank
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Konvertiere dateRange zu API-Format
        const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
        const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;
        
        console.log('ServiceGradChart: Lade Daten mit Parametern:', { startDate, endDate });
        
        const result = await apiService.getServiceLevelData(startDate, endDate);
        console.log('ServiceGradChart: Empfangene Rohdaten:', result);

        if (result && Array.isArray(result) && result.length > 0) {
          console.log(`ServiceGradChart: Verarbeite ${result.length} Datensätze`);
          console.log('ServiceGradChart: Erste 3 Rohdaten-Samples:', result.slice(0, 3));
          console.log('ServiceGradChart: Datumfeld-Typen der ersten 3 Einträge:');
          result.slice(0, 3).forEach((item: any, index) => {
            console.log(`  [${index}] datum:`, item.datum, 'typeof:', typeof item.datum, 'instanceof Date:', item.datum instanceof Date);
          });

          // Konvertiere Datenbankdaten zu Chart-Format
          let processedData: ChartDataPoint[] = result.map((row: any, index: number) => {
            // Typsicherheit und Validierung
            if (!row || typeof row !== 'object') {
              console.warn(`ServiceGradChart: Ungültiger Datensatz bei Index ${index}:`, row);
              return { date: '', servicegrad: 0 };
            }

            // Verwende servicegrad (korrekte Spalte aus dispatch_data Tabelle)
            const rawValue = row.servicegrad;
            let servicegradValue = Number(rawValue) || 0;
            
            // Wenn der Wert zwischen 0 und 1 liegt, handelt es sich um einen Dezimalwert (z.B. 0.95 = 95%)
            if (servicegradValue > 0 && servicegradValue <= 1) {
              servicegradValue = servicegradValue * 100;
            }
            
            // Backend liefert echte Datumswerte - verwende sie direkt
            let dateValue: string;
            if (typeof row.datum === 'string') {
              dateValue = row.datum; // Backend liefert bereits YYYY-MM-DD Format
            } else if (row.datum instanceof Date) {
              dateValue = row.datum.toISOString().split('T')[0];
            } else if (typeof row.datum === 'number') {
              dateValue = new Date(row.datum).toISOString().split('T')[0];
            } else {
              console.warn('ServiceGradChart: Null/undefined Datum gefunden - überspringe Datensatz:', row);
              return { date: '', servicegrad: 0 }; // Wird später herausgefiltert
            }
            
            const dataPoint: ChartDataPoint = {
              date: dateValue,
              servicegrad: servicegradValue
            };
            
            return dataPoint;
          }).filter(point => point.date !== ''); // Filtere ungültige Datenpunkte heraus
          
          console.log('ServiceGradChart: Verarbeitete Daten vor Filterung:', processedData.length);
          console.log('ServiceGradChart: Erste 3 verarbeitete Datenpunkte:', processedData.slice(0, 3));
          console.log('ServiceGradChart: Verarbeitete Datumfeld-Typen:');
          processedData.slice(0, 3).forEach((item, index) => {
            console.log(`  [${index}] date:`, item.date, 'typeof:', typeof item.date);
          });
          
          // Zusätzliche clientseitige Filterung (falls Backend-Filter nicht vollständig funktioniert)
          let filteredData = processedData;
          if (dateRange?.from || dateRange?.to) {
            filteredData = processedData.filter((item) => {
              try {
                const itemDate = new Date(item.date);
                
                // Prüfe auf gültiges Datum
                if (isNaN(itemDate.getTime())) {
                  console.warn('ServiceGradChart: Ungültiges Datum gefunden:', item.date);
                  return false;
                }
                
                // Prüfe ob das Datum im ausgewählten Bereich liegt
                if (dateRange.from && itemDate < dateRange.from) return false;
                if (dateRange.to && itemDate > dateRange.to) return false;
                
                return true;
              } catch (error) {
                console.error('ServiceGradChart: Fehler bei Datumsvergleich für:', item.date, error);
                return false;
              }
            });
          }
          
          // Sortiere die Daten nach Datum in aufsteigender Reihenfolge (älteste zuerst)
          // Dies stellt sicher, dass die X-Achse von links (älteste) nach rechts (neueste) angezeigt wird
          filteredData.sort((a, b) => {
            try {
              const dateA = new Date(a.date).getTime();
              const dateB = new Date(b.date).getTime();
              return dateA - dateB; // Aufsteigende Sortierung
            } catch (e) {
              return 0; // Bei Fehlern beim Datumsvergleich Reihenfolge beibehalten
            }
          });
          
          console.log(`ServiceGradChart: Nach Filterung ${filteredData.length} Datensätze übrig`);
          setChartData(filteredData);
        } else if (result && Array.isArray(result) && result.length === 0) {
          console.log('ServiceGradChart: API gab leeres Array zurück - keine Daten im angegebenen Zeitraum');
          setChartData([]);
          setError('Keine Daten für den ausgewählten Zeitraum verfügbar');
        } else {
          console.error('ServiceGradChart: Unerwartete API-Antwort:', result);
          throw new Error('Keine gültigen Daten erhalten');
        }
      } catch (err) {
        console.error('ServiceGradChart: Fehler beim Laden der Daten:', err);
        const errorMessage = err instanceof Error ? err.message : 'Fehler beim Laden der Daten aus der Datenbank.';
        setError(errorMessage);
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [dateRange]); // Abhängigkeit hinzugefügt für dateRange

  // Zeige Ladezustand an
  if (loading) {
    return (
      <Card className="text-black">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Zeige Fehler an - nur wenn kein Fallback vorhanden ist
  if (error && chartData.length === 0) {
    return (
      <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <p className="font-bold text-red-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Berechne Statistiken
  const avgServicegrad = chartData.length > 0 
    ? chartData.reduce((sum, item) => sum + item.servicegrad, 0) / chartData.length 
    : 0;

  return (
    <Card  className="text-black w-full h-105 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>SERVICEGRAD</CardTitle>
        <CardDescription>
          Servicegradverlauf
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          ref={ref || chartRef}
          className="h-54 w-full"
          config={chartConfig}
        >
          <AreaChart
            className="overflow-visible"
            accessibilityLayer
            data={chartData}
            onMouseMove={(state) => {
              const x = state.activeCoordinate?.x;
              const dataValue = state.activePayload?.[0]?.value;
              if (x && dataValue !== undefined) {
                springX.set(x);
                springY.set(dataValue);
              }
            }}
            onMouseLeave={() => {
              // Springe zum letzten Datenpunkt (rechte Seite)
              if (chartRef.current && chartData.length > 0) {
                const chartWidth = chartRef.current.getBoundingClientRect().width;
                // Setze explizit auf die rechte Seite des Charts
                springX.set(chartWidth);
                springY.jump(chartData[chartData.length - 1].servicegrad);
              } else {
                // Fallback, wenn keine Daten vorhanden
                springX.set(0);
                springY.jump(0);
              }
            }}
            margin={{
              right: 0,
              left: 0,
            }}
          >
            <CartesianGrid
              vertical={false}
              strokeDasharray="3 3"
              horizontalCoordinatesGenerator={(props) => {
                const { height } = props;
                return [0, height - 30];
              }}
            />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => {
                try {
                  const date = new Date(value);
                  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                } catch {
                  return value;
                }
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tick={{ fontSize: 12, fontWeight: 600 }}
              domain={[80, 100]}
              tickFormatter={(value) => `${value}%`}
            />
            <defs>
              <linearGradient
                id="gradient-cliped-area-servicegrad"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop
                  offset="5%"
                  stopColor="var(--color-servicegrad)"
                  stopOpacity={0.4}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-servicegrad)"
                  stopOpacity={0}
                />
              </linearGradient>
              <mask id="mask-cliped-area-chart">
                <rect
                  x={0}
                  y={0}
                  width={axis}
                  height={"100%"}
                  fill="white"
                />
              </mask>
            </defs>
            {/* this is a ghost line behind graph */}
            <Area
              dataKey="servicegrad"
              type="monotone"
              fill="url(#gradient-cliped-area-servicegrad)"
              stroke="var(--color-servicegrad)"
              strokeOpacity={0.1}
              fillOpacity={1}
              mask="url(#mask-cliped-area-chart)"
            />
            <Area
              dataKey="servicegrad"
              type="monotone"
              fill="none"
              stroke="var(--color-servicegrad)"
              strokeWidth={2}
            />
            <line
              x1={axis}
              y1={0}
              x2={axis}
              y2={"85%"}
              stroke="var(--color-servicegrad)"
              strokeLinecap="round"
              strokeOpacity={1}
            />
            <rect
              x={axis - 30}
              y={0}
              width={60}
              height={24}
              rx={4}
              fill="var(--color-servicegrad)"
            />
            <text
              x={axis}
              fontWeight={600}
              y={16}
              textAnchor="middle"
              fill="white"
              fontSize={12}
            >
              {springY.get().toFixed(0)}%
            </text>
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full pt-6 items-start justify-between text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Wert: {springY.get().toFixed(0)}%
            </div>
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: {avgServicegrad.toFixed(1)}% <TrendingUp className="h-4 w-4" />
            </div>
          </div>
          <div className="text-muted-foreground pt-12 flex items-center gap-2 leading-none">
            {error ? (
              <Badge variant="destructive">Datenbank nicht verfügbar</Badge>
            ) : (
              <Badge variant="outline2">Letzte Aktualisierung: {new Date().toLocaleDateString()}</Badge>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});

export default AreaChartStacked;
