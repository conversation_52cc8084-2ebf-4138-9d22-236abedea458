"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const system_controller_1 = require("../controllers/system.controller");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const router = (0, express_1.Router)();
const controller = new system_controller_1.SystemController();
// Rate limiting for system endpoints
const systemLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.NODE_ENV === "development" ? 500 : 100, // Limit each IP
    message: "Too many requests to system API from this IP",
    standardHeaders: true,
    legacyHeaders: false,
});
// Apply rate limiting to all routes
router.use(systemLimiter);
// Service Level routes
router.get('/service-level', controller.getServiceLevelData.bind(controller));
router.get('/service-level/current', controller.getCurrentServiceLevel.bind(controller));
router.get('/service-level/stats', controller.getServiceLevelStats.bind(controller));
// Performance routes
router.get('/performance', controller.getDailyPerformance.bind(controller));
router.get('/performance/trends', controller.getPerformanceTrend.bind(controller));
// Picking routes
router.get('/picking', controller.getPickingData.bind(controller));
router.get('/picking/efficiency', controller.getPickingEfficiency.bind(controller));
// Returns routes
router.get('/returns', controller.getReturnsData.bind(controller));
router.get('/returns/analysis', controller.getReturnsAnalysis.bind(controller));
// Delivery Positions routes
router.get('/delivery-positions', controller.getDeliveryPositions.bind(controller));
router.get('/delivery-positions/analysis', controller.getDeliveryAnalysis.bind(controller));
// Tagesleistung routes
router.get('/tagesleistung', controller.getTagesleistungData.bind(controller));
router.get('/tagesleistung/stats', controller.getDailyPerformanceStats.bind(controller));
// System Stats routes
router.get('/stats', controller.getSystemStats.bind(controller));
router.get('/health', controller.getSystemHealthDashboard.bind(controller));
// Dashboard route
router.get('/dashboard', controller.getSystemDashboard.bind(controller));
exports.default = router;
