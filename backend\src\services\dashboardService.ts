import { dashboardRepository, ServiceLevelDataPoint, DeliveryPositionDataPoint, TagesleistungDataPoint, PickingDataPoint } from '../repositories/dashboardRepository';

export class DashboardService {
  /**
   * Holt ServiceGrad-Daten für Dashboard-Charts
   */
  async getServiceLevelData(startDate?: string, endDate?: string): Promise<ServiceLevelDataPoint[]> {
    try {
      return await dashboardRepository.getServiceLevelData(startDate, endDate);
    } catch (error) {
      console.error('DashboardService: Fehler beim Laden der ServiceGrad-Daten:', error);
      throw new Error('ServiceGrad-Daten konnten nicht geladen werden');
    }
  }

  /**
   * Holt Lieferpositionen-Daten für Dashboard-Charts
   */
  async getDeliveryPositionsData(startDate?: string, endDate?: string): Promise<DeliveryPositionDataPoint[]> {
    try {
      return await dashboardRepository.getDeliveryPositionsData(startDate, endDate);
    } catch (error) {
      console.error('DashboardService: Fe<PERSON> beim <PERSON> der Lieferpositionen-Daten:', error);
      throw new Error('Lieferpositionen-Daten konnten nicht geladen werden');
    }
  }

  /**
   * Holt Tagesleistung-Daten für Dashboard-Charts
   */
  async getTagesleistungData(startDate?: string, endDate?: string): Promise<TagesleistungDataPoint[]> {
    try {
      return await dashboardRepository.getTagesleistungData(startDate, endDate);
    } catch (error) {
      console.error('DashboardService: Fehler beim Laden der Tagesleistung-Daten:', error);
      throw new Error('Tagesleistung-Daten konnten nicht geladen werden');
    }
  }

  /**
   * Holt Picking-Daten für Dashboard-Charts
   */
  async getPickingData(startDate?: string, endDate?: string): Promise<PickingDataPoint[]> {
    try {
      return await dashboardRepository.getPickingData(startDate, endDate);
    } catch (error) {
      console.error('DashboardService: Fehler beim Laden der Picking-Daten:', error);
      throw new Error('Picking-Daten konnten nicht geladen werden');
    }
  }

  /**
   * Holt alle Dashboard-Daten in einer optimierten Abfrage
   */
  async getAllDashboardData(startDate?: string, endDate?: string): Promise<{
    serviceLevel: ServiceLevelDataPoint[];
    deliveryPositions: DeliveryPositionDataPoint[];
    tagesleistung: TagesleistungDataPoint[];
    picking: PickingDataPoint[];
  }> {
    try {
      return await dashboardRepository.getAllDashboardData(startDate, endDate);
    } catch (error) {
      console.error('DashboardService: Fehler beim Laden aller Dashboard-Daten:', error);
      throw new Error('Dashboard-Daten konnten nicht geladen werden');
    }
  }

  /**
   * Validiert Datumsangaben
   */
  private validateDateRange(startDate?: string, endDate?: string): void {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start > end) {
        throw new Error('Startdatum darf nicht nach dem Enddatum liegen');
      }

      // Maximal 2 Jahre Zeitraum erlauben für Performance
      const maxDays = 730;
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff > maxDays) {
        throw new Error(`Zeitraum darf maximal ${maxDays} Tage betragen`);
      }
    }
  }

  /**
   * Berechnet aggregierte Statistiken für einen Zeitraum
   */
  async getDashboardStatistics(startDate?: string, endDate?: string): Promise<{
    serviceLevel: {
      average: number;
      min: number;
      max: number;
      count: number;
    };
    deliveryPositions: {
      totalAusgeliefert: number;
      totalRueckstaendig: number;
      count: number;
    };
    tagesleistung: {
      totalProduzierteTonnagen: number;
      averageKgProColli: number;
      count: number;
    };
    picking: {
      totalAtrl: number;
      totalAril: number;
      averageFuellgrad: number;
      count: number;
    };
  }> {
    try {
      this.validateDateRange(startDate, endDate);
      const allData = await this.getAllDashboardData(startDate, endDate);

      // Service Level Statistiken
      const serviceLevelStats = this.calculateServiceLevelStats(allData.serviceLevel);

      // Lieferpositionen Statistiken
      const deliveryStats = this.calculateDeliveryStats(allData.deliveryPositions);

      // Tagesleistung Statistiken
      const tagesleistungStats = this.calculateTagesleistungStats(allData.tagesleistung);

      // Picking Statistiken
      const pickingStats = this.calculatePickingStats(allData.picking);

      return {
        serviceLevel: serviceLevelStats,
        deliveryPositions: deliveryStats,
        tagesleistung: tagesleistungStats,
        picking: pickingStats
      };
    } catch (error) {
      console.error('DashboardService: Fehler beim Berechnen der Statistiken:', error);
      throw new Error('Dashboard-Statistiken konnten nicht berechnet werden');
    }
  }

  private calculateServiceLevelStats(data: ServiceLevelDataPoint[]) {
    if (data.length === 0) {
      return { average: 0, min: 0, max: 0, count: 0 };
    }

    const values = data.map(d => d.servicegrad);
    return {
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: data.length
    };
  }

  private calculateDeliveryStats(data: DeliveryPositionDataPoint[]) {
    if (data.length === 0) {
      return { totalAusgeliefert: 0, totalRueckstaendig: 0, count: 0 };
    }

    return {
      totalAusgeliefert: data.reduce((sum, d) => sum + d.ausgeliefert_lup, 0),
      totalRueckstaendig: data.reduce((sum, d) => sum + d.rueckstaendig, 0),
      count: data.length
    };
  }

  private calculateTagesleistungStats(data: TagesleistungDataPoint[]) {
    if (data.length === 0) {
      return { totalProduzierteTonnagen: 0, averageKgProColli: 0, count: 0 };
    }

    const totalTonnagen = data.reduce((sum, d) => sum + d.produzierte_tonnagen, 0);
    const totalKgProColli = data.reduce((sum, d) => sum + d.kg_pro_colli, 0);

    return {
      totalProduzierteTonnagen: totalTonnagen,
      averageKgProColli: totalKgProColli / data.length,
      count: data.length
    };
  }

  private calculatePickingStats(data: PickingDataPoint[]) {
    if (data.length === 0) {
      return { totalAtrl: 0, totalAril: 0, averageFuellgrad: 0, count: 0 };
    }

    return {
      totalAtrl: data.reduce((sum, d) => sum + d.atrl, 0),
      totalAril: data.reduce((sum, d) => sum + d.aril, 0),
      averageFuellgrad: data.reduce((sum, d) => sum + d.fuellgrad_aril, 0) / data.length,
      count: data.length
    };
  }
}

export const dashboardService = new DashboardService();