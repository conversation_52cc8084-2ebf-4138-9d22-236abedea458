import { Router } from 'express';
import { SupplierController } from '../controllers/supplier.controller';
import { rateLimit } from 'express-rate-limit';

const router = Router();
const controller = new SupplierController();

// Rate limiting für Supplier-Routen
const supplierLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: 100, // Limit pro Window pro IP
  message: {
    success: false,
    error: '<PERSON>u viele An<PERSON> von dieser IP-Adresse',
    retryAfter: '15 Minuten'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Basis-Routen für Lieferanten
router.get('/', supplierLimiter, controller.getAllSuppliers.bind(controller));
router.get('/stats', supplierLimiter, controller.getSupplierStats.bind(controller));
router.get('/search', supplierLimiter, controller.searchSuppliers.bind(controller));

// Routen mit Parameter
router.get('/:supplierId', supplierLimiter, controller.getSupplierById.bind(controller));
router.get('/performance/:supplierId', supplierLimiter, controller.getSupplierPerformance.bind(controller));
router.get('/category/:category', supplierLimiter, controller.getSuppliersByCategory.bind(controller));
router.get('/:supplierId/history', supplierLimiter, controller.getSupplierDeliveryHistory.bind(controller));

// Performance-Routen
router.get('/top', supplierLimiter, controller.getTopPerformingSuppliers.bind(controller));
router.get('/risk', supplierLimiter, controller.getRiskSuppliers.bind(controller));
router.put('/:supplierId/performance', supplierLimiter, controller.updateSupplierPerformance.bind(controller));

export default router;