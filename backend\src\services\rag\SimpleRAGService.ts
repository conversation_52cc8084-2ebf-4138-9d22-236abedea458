/**
 * SimpleRAGService - Simplified RAG Service for SQLite-based implementation
 * 
 * Provides basic RAG functionality using Drizzle ORM for data persistence
 */

import PostgreSQLRAGService from './PostgreSQLRAGService';
import EmbeddingService from './EmbeddingService';
import PostgreSQLDocumentService from './PostgreSQLDocumentService';
import {
  CreateDocumentRequest,
  CreateKnowledgeBaseRequest,
  SimilaritySearchRequest,
  RAGResponse,
  SimilaritySearchResult
} from '../../types/rag.types';

export class SimpleRAGService {
  private vectorService: PostgreSQLRAGService;
  private embeddingService: EmbeddingService;
  private documentService: PostgreSQLDocumentService;

  constructor(openAIApiKey?: string) {
    this.vectorService = new PostgreSQLRAGService();
    this.embeddingService = new EmbeddingService({ apiKey: openAIApiKey });
    this.documentService = new PostgreSQLDocumentService();
  }

  /**
   * Perform semantic search
   */
  async search(request: SimilaritySearchRequest): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      // Generate query embedding
      const providerInfo = this.embeddingService.getProviderInfo();
      const queryEmbedding = await this.embeddingService.generateEmbedding({
        text: request.query,
        model: providerInfo.model
      });

      // Perform similarity search
      const searchResults = await this.vectorService.searchSimilar(
        queryEmbedding.embedding,
        {
          limit: request.limit || 10,
          threshold: request.threshold || 0.7,
          language: request.language || 'de'
        }
      );

      // Prepare context from search results
      const context = this.prepareContext(searchResults);

      const executionTimeMs = Date.now() - startTime;

      return {
        query: request.query,
        results: searchResults,
        context,
        intent: this.detectIntent(request.query),
        executionTimeMs,
        totalResults: searchResults.length
      };

    } catch (error) {
      console.error('Error in RAG search:', error);
      throw new Error('RAG search failed: ' + (error as Error).message);
    }
  }

  /**
   * Add document with automatic processing
   */
  async addDocument(
    request: CreateDocumentRequest,
    generateEmbeddings: boolean = true
  ): Promise<{ documentId: string; chunksCreated: number; embeddingsGenerated: number }> {
    try {
      // Store document
      const documentId = await this.documentService.storeDocument(request);
      
      // Chunk the document
      const chunks = this.documentService.chunkDocument(request.content);
      const chunkIds: string[] = [];

      // Store chunks
      for (let i = 0; i < chunks.length; i++) {
        const chunkId = await this.documentService.storeChunk(documentId, chunks[i], i);
        chunkIds.push(chunkId);
      }

      let embeddingsGenerated = 0;

      if (generateEmbeddings) {
        // Generate embeddings for all chunks
        const providerInfo = this.embeddingService.getProviderInfo();
        
        for (const chunkId of chunkIds) {
          try {
            const chunk = chunks[chunkIds.indexOf(chunkId)];
            const embedding = await this.embeddingService.generateEmbedding({
              text: chunk,
              model: providerInfo.model
            });

            await this.vectorService.storeEmbedding(
              chunkId,
              embedding.embedding,
              embedding.model
            );

            embeddingsGenerated++;
          } catch (error) {
            console.error(`Error generating embedding for chunk ${chunkId}:`, error);
          }
        }
      }

      return {
        documentId,
        chunksCreated: chunks.length,
        embeddingsGenerated
      };

    } catch (error) {
      console.error('Error adding document:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const stats = await this.vectorService.getStatistics();
      return {
        totalQueries: 0, // Would need separate tracking
        averageResponseTime: 0, // Would need separate tracking
        averageSimilarity: 0, // Would need separate tracking
        cacheHitRate: 0,
        embeddingGenerationTime: 0,
        searchTime: 0,
        totalDocuments: stats.totalDocuments,
        totalChunks: stats.totalChunks,
        totalEmbeddings: stats.totalEmbeddings,
        totalKnowledgeBases: stats.totalKnowledgeBases, // Neu hinzugefügt
        averageChunkSize: stats.averageChunkSize, // Neu hinzugefügt
        storageSize: stats.storageSize
      };
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      return {
        totalQueries: 0,
        averageResponseTime: 0,
        averageSimilarity: 0,
        cacheHitRate: 0,
        embeddingGenerationTime: 0,
        searchTime: 0,
        totalDocuments: 0,
        totalChunks: 0,
        totalEmbeddings: 0,
        totalKnowledgeBases: 0, // Neu hinzugefügt
        averageChunkSize: 0, // Neu hinzugefügt
        storageSize: 0
      };
    }
  }

  /**
   * Get recent queries (simplified)
   */
  async getRecentQueries(limit: number = 20) {
    try {
      console.log('[SimpleRAGService] Getting recent queries...');
      // For now, return empty array since we don't have query logging implemented yet
      // This will be implemented when we add query tracking functionality
      return [];
    } catch (error) {
      console.error('[SimpleRAGService] Error getting recent queries:', error);
      return [];
    }
  }

  /**
   * Create knowledge base (simplified)
   */
  async createKnowledgeBase(request: CreateKnowledgeBaseRequest) {
    // TODO: Implement real knowledge base creation in database
    throw new Error('Knowledge base creation not yet implemented. Please use direct document operations.');
  }

  /**
   * Get knowledge bases (simplified)
   */
  async getKnowledgeBases() {
    // TODO: Implement real knowledge base retrieval from database
    throw new Error('Knowledge base retrieval not yet implemented. Please use direct document operations.');
  }

  /**
   * Get all documents
   */
  async getDocuments() {
    try {
      return await this.documentService.getAllDocuments();
    } catch (error) {
      console.error('Error getting documents:', error);
      throw error;
    }
  }

  /**
   * Delete document and all associated data
   */
  async deleteDocument(documentId: string): Promise<{
    success: boolean;
    chunksDeleted: number;
    embeddingsDeleted: number;
    error?: string;
  }> {
    try {
      // Get chunks for this document first
      const chunks = await this.documentService.getDocumentChunks(documentId);
      
      // Delete embeddings for all chunks
      let embeddingsDeleted = 0;
      for (const chunk of chunks) {
        try {
          await this.vectorService.deleteEmbedding(chunk.id);
          embeddingsDeleted++;
        } catch (error) {
          console.error(`Error deleting embedding for chunk ${chunk.id}:`, error);
        }
      }

      // Delete chunks
      const chunksDeleted = await this.documentService.deleteDocumentChunks(documentId);

      // Delete document
      const documentDeleted = await this.documentService.deleteDocument(documentId);

      if (!documentDeleted) {
        return {
          success: false,
          chunksDeleted: 0,
          embeddingsDeleted: 0,
          error: 'Document not found'
        };
      }

      return {
        success: true,
        chunksDeleted,
        embeddingsDeleted
      };

    } catch (error) {
      console.error('Error deleting document:', error);
      return {
        success: false,
        chunksDeleted: 0,
        embeddingsDeleted: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Search documents by text
   */
  async searchDocuments(query: string, knowledgeBaseIds?: number[]) {
    // For now, return empty array
    return [];
  }

  /**
   * Prepare context string from search results
   */
  private prepareContext(results: SimilaritySearchResult[]): string {
    if (results.length === 0) {
      return '';
    }

    let context = '';
    const maxTokens = 4000;
    let tokenCount = 0;

    for (const result of results) {
      const chunkText = `[Dokument] ${result.chunk.content}`;
      const chunkTokens = Math.ceil(chunkText.length / 4); // Rough estimate

      if (tokenCount + chunkTokens > maxTokens) {
        break;
      }

      if (context.length > 0) {
        context += '\n\n';
      }
      context += chunkText;
      tokenCount += chunkTokens;
    }

    return context;
  }

  /**
   * Simple intent detection
   */
  private detectIntent(query: string): string | undefined {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('kpi') || lowerQuery.includes('kennzahl')) {
      return 'kpi_inquiry';
    }
    if (lowerQuery.includes('optimier') || lowerQuery.includes('verbesser')) {
      return 'process_optimization';
    }
    if (lowerQuery.includes('problem') || lowerQuery.includes('fehler')) {
      return 'troubleshooting';
    }
    if (lowerQuery.includes('was ist') || lowerQuery.includes('wie')) {
      return 'information_retrieval';
    }

    return 'general_inquiry';
  }

  /**
   * Cleanup resources
   */
  async disconnect(): Promise<void> {
    await this.vectorService.disconnect();
    await this.documentService.disconnect();
  }
}

export default SimpleRAGService;