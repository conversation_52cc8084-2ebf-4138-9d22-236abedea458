// Drizzle ORM Database Connection mit PostgreSQL
import 'dotenv/config';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from './schema';
import {
  loadDatabaseConfig,
  loadRagDatabaseConfig,
  createPoolConfig,
  validateDatabaseConfig
} from '../config/database.config';

/**
 * PostgreSQL-Verbindung für zentrale Server-DB
 *
 * Dokumentation: https://orm.drizzle.team/docs/get-started/postgresql
 */

// Lade die Server-Datenbankkonfiguration (für die Dispatch-Daten)
const dbConfig = loadDatabaseConfig() || loadRagDatabaseConfig();

// Validiere die Konfiguration
if (!validateDatabaseConfig(dbConfig)) {
  console.error('❌ Ungültige Datenbankkonfiguration. Anwendung wird beendet.');
  process.exit(1);
}

console.log('🔗 Initialisiere PostgreSQL-Datenbankverbindung...');
console.log('🗄️  Host:', dbConfig.host, 'DB:', dbConfig.database, 'User:', dbConfig.username);

// Erstelle Pool-Konfiguration
const poolConfig = createPoolConfig(dbConfig);

// PostgreSQL Pool erstellen
const pool = new Pool(poolConfig);

// Drizzle ORM mit pg-Pool initialisieren
const db = drizzle(pool, { schema });

console.log('✅ PostgreSQL-Datenbankverbindung erfolgreich initialisiert');

// Datenbankverbindung exportieren
export { db };

// Schema-Exports
export * from './schema';

// Typ des DB-Objekts exportieren
export type Database = typeof db;

// Optional: Pool exportieren für Low-Level-Zugriffe
export { pool };