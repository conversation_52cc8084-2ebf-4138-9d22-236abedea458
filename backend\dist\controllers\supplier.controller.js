"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierController = void 0;
const supplier_repository_1 = require("../repositories/supplier.repository");
class SupplierController {
    constructor() {
        this.repository = new supplier_repository_1.SupplierRepositoryImpl();
    }
    /**
     * Get all suppliers
     * GET /api/supplier
     */
    async getAllSuppliers(req, res) {
        try {
            const suppliers = await this.repository.getAll();
            res.json({
                success: true,
                data: suppliers,
                count: suppliers.length
            });
        }
        catch (error) {
            console.error('Error fetching all suppliers:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen aller Lieferanten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get supplier statistics
     * GET /api/supplier/stats
     */
    async getSupplierStats(req, res) {
        try {
            const stats = await this.repository.getSupplierStats();
            res.json({
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching supplier stats:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Lieferanten-Statistiken',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get supplier performance data
     * GET /api/supplier/performance/:supplierId?days=30
     */
    async getSupplierPerformance(req, res) {
        try {
            const { supplierId } = req.params;
            const { days } = req.query;
            const timeRange = days ? { days: parseInt(days) } : { days: 30 };
            const performance = await this.repository.getSupplierPerformance(supplierId, timeRange);
            res.json({
                success: true,
                data: performance,
                supplierId,
                timeRange
            });
        }
        catch (error) {
            console.error(`Error fetching supplier performance for ${req.params.supplierId}:`, error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Lieferanten-Performance',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get supplier by ID
     * GET /api/supplier/:supplierId
     */
    async getSupplierById(req, res) {
        try {
            const { supplierId } = req.params;
            const supplier = await this.repository.getSupplierById(supplierId);
            if (!supplier) {
                return res.status(404).json({
                    success: false,
                    error: 'Lieferant nicht gefunden',
                    message: `Lieferant mit ID ${supplierId} wurde nicht gefunden`
                });
            }
            res.json({
                success: true,
                data: supplier
            });
        }
        catch (error) {
            console.error(`Error fetching supplier ${req.params.supplierId}:`, error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen des Lieferanten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get suppliers by category
     * GET /api/supplier/category/:category
     */
    async getSuppliersByCategory(req, res) {
        try {
            const { category } = req.params;
            const suppliers = await this.repository.getSuppliersByCategory(category);
            res.json({
                success: true,
                data: suppliers,
                count: suppliers.length,
                category
            });
        }
        catch (error) {
            console.error(`Error fetching suppliers by category ${req.params.category}:`, error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Lieferanten nach Kategorie',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get top performing suppliers
     * GET /api/supplier/top?limit=10
     */
    async getTopPerformingSuppliers(req, res) {
        try {
            const { limit } = req.query;
            const limitNum = limit ? parseInt(limit) : 10;
            const suppliers = await this.repository.getTopPerformingSuppliers(limitNum);
            res.json({
                success: true,
                data: suppliers,
                count: suppliers.length,
                limit: limitNum
            });
        }
        catch (error) {
            console.error('Error fetching top performing suppliers:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Top-Performers',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get risk suppliers
     * GET /api/supplier/risk
     */
    async getRiskSuppliers(req, res) {
        try {
            const suppliers = await this.repository.getRiskSuppliers();
            res.json({
                success: true,
                data: suppliers,
                count: suppliers.length,
                riskLevel: 'high'
            });
        }
        catch (error) {
            console.error('Error fetching risk suppliers:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Risiko-Lieferanten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Update supplier performance metrics
     * PUT /api/supplier/:supplierId/performance
     */
    async updateSupplierPerformance(req, res) {
        try {
            const { supplierId } = req.params;
            const metrics = req.body;
            await this.repository.updateSupplierPerformance(supplierId, metrics);
            res.json({
                success: true,
                message: 'Lieferanten-Performance erfolgreich aktualisiert',
                supplierId,
                updatedMetrics: metrics
            });
        }
        catch (error) {
            console.error(`Error updating supplier performance for ${req.params.supplierId}:`, error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Aktualisieren der Lieferanten-Performance',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Search suppliers
     * GET /api/supplier/search?q=query
     */
    async searchSuppliers(req, res) {
        try {
            const { q } = req.query;
            if (!q || typeof q !== 'string') {
                return res.status(400).json({
                    success: false,
                    error: 'Suchbegriff erforderlich',
                    message: 'Bitte geben Sie einen Suchbegriff mit dem Parameter "q" an'
                });
            }
            const suppliers = await this.repository.searchSuppliers(q);
            res.json({
                success: true,
                data: suppliers,
                count: suppliers.length,
                query: q
            });
        }
        catch (error) {
            console.error(`Error searching suppliers with query "${req.query.q}":`, error);
            res.status(500).json({
                success: false,
                error: 'Fehler bei der Lieferanten-Suche',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get supplier delivery history
     * GET /api/supplier/:supplierId/history?days=30
     */
    async getSupplierDeliveryHistory(req, res) {
        try {
            const { supplierId } = req.params;
            const { days } = req.query;
            const timeRange = days ? { days: parseInt(days) } : { days: 30 };
            const history = await this.repository.getSupplierDeliveryHistory(supplierId, timeRange);
            res.json({
                success: true,
                data: history,
                count: history.length,
                supplierId,
                timeRange
            });
        }
        catch (error) {
            console.error(`Error fetching delivery history for supplier ${req.params.supplierId}:`, error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Lieferhistorie',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
}
exports.SupplierController = SupplierController;
