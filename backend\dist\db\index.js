"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pool = exports.db = void 0;
// Drizzle ORM Database Connection mit PostgreSQL
require("dotenv/config");
const node_postgres_1 = require("drizzle-orm/node-postgres");
const pg_1 = require("pg");
const schema = __importStar(require("./schema"));
const database_config_1 = require("../config/database.config");
/**
 * PostgreSQL-Verbindung für zentrale Server-DB
 *
 * Dokumentation: https://orm.drizzle.team/docs/get-started/postgresql
 */
// Lade die Datenbankkonfiguration
const dbConfig = (0, database_config_1.loadDatabaseConfig)();
// Validiere die Konfiguration
if (!(0, database_config_1.validateDatabaseConfig)(dbConfig)) {
    console.error('❌ Ungültige Datenbankkonfiguration. Anwendung wird beendet.');
    process.exit(1);
}
console.log('🔗 Initialisiere PostgreSQL-Datenbankverbindung...');
console.log('🗄️  Host:', dbConfig.host, 'DB:', dbConfig.database, 'User:', dbConfig.username);
// Erstelle Pool-Konfiguration
const poolConfig = (0, database_config_1.createPoolConfig)(dbConfig);
// PostgreSQL Pool erstellen
const pool = new pg_1.Pool(poolConfig);
exports.pool = pool;
// Drizzle ORM mit pg-Pool initialisieren
const db = (0, node_postgres_1.drizzle)(pool, { schema });
exports.db = db;
console.log('✅ PostgreSQL-Datenbankverbindung erfolgreich initialisiert');
// Schema-Exports
__exportStar(require("./schema"), exports);
