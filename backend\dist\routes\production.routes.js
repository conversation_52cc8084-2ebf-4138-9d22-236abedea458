"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const production_controller_1 = require("../controllers/production.controller");
const express_rate_limit_1 = require("express-rate-limit");
const router = (0, express_1.Router)();
const controller = new production_controller_1.ProductionController();
// Rate limiting für Production-Routen
const productionLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    max: 100, // Limit pro Window pro IP
    message: {
        success: false,
        error: '<PERSON>u viele Anfragen von dieser IP-Adresse',
        retryAfter: '15 Minuten'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
// Schnitte Routes
router.get('/schnitte', productionLimiter, controller.getSchnitteData.bind(controller));
router.get('/schnitte/grouped', productionLimiter, controller.getSchnitteGroupedByDate.bind(controller));
router.get('/schnitte/sums', productionLimiter, controller.getSchnitteDailySums.bind(controller));
router.get('/schnitte/performance', productionLimiter, controller.getMachinePerformance.bind(controller));
// Maschinen-Effizienz Routes
router.get('/efficiency', productionLimiter, controller.getMachineEfficiency.bind(controller));
router.get('/efficiency/top', productionLimiter, controller.getTopPerformers.bind(controller));
router.get('/efficiency/low', productionLimiter, controller.getLowPerformers.bind(controller));
router.get('/efficiency/average', productionLimiter, controller.getAverageEfficiency.bind(controller));
// Ablaengerei Routes
router.get('/ablaengerei', productionLimiter, controller.getAblaengereiData.bind(controller));
router.get('/ablaengerei/stats', productionLimiter, controller.getAblaengereiDailyStats.bind(controller));
router.get('/ablaengerei/warehouse-performance', productionLimiter, controller.getWarehousePerformance.bind(controller));
// Cutting Chart Routes
router.get('/cutting-chart', productionLimiter, controller.getCuttingChartData.bind(controller));
router.get('/cutting-chart/trends', productionLimiter, controller.getCuttingTrendAnalysis.bind(controller));
// Lager Cuts Routes
router.get('/lager-cuts', productionLimiter, controller.getLagerCutsData.bind(controller));
router.get('/lager-cuts/distribution', productionLimiter, controller.getWarehouseDistribution.bind(controller));
// Gesamtstatistiken
router.get('/stats', productionLimiter, controller.getOverallProductionStats.bind(controller));
exports.default = router;
