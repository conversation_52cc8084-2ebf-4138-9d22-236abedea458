import { Client } from 'pg';
import 'dotenv/config';

// Datenbankverbindung herstellen
const connectionString = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`;

const client = new Client({
  connectionString,
  connectionTimeoutMillis: 5000,
});

async function createMissingTables() {
  console.log('🔍 Prüfe fehlende Tabellen...');

  try {
    // Verbinde zur Datenbank
    await client.connect();
    console.log('✅ Datenbankverbindung hergestellt');

    // Prüfe, ob die Tabellen bereits existieren
    const result = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
    `);

    const existingTableNames = result.rows.map((t: any) => t.table_name);

    console.log('📋 Vorhandene Tabellen:', existingTableNames);

    // Erstelle fehlende Tabellen
    const tablesToCreate = [
      { name: 'Ablaengerei' },
      { name: 'WE' },
      { name: 'dispatch_data' },
    ];

    for (const table of tablesToCreate) {
      if (!existingTableNames.includes(table.name.toLowerCase())) {
        console.log(`🏗️  Erstelle Tabelle: ${table.name}`);

        // Erstelle die Tabelle basierend auf dem Schema
        if (table.name === 'Ablaengerei') {
          await client.query(`
            CREATE TABLE "Ablaengerei" (
              "id" SERIAL PRIMARY KEY,
              "Datum" TEXT,
              "cutLagerK220" INTEGER DEFAULT 0,
              "cutLagerR220" INTEGER DEFAULT 0,
              "lagerCut220" INTEGER DEFAULT 0,
              "cutLagerK240" INTEGER DEFAULT 0,
              "cutLagerR240" INTEGER DEFAULT 0,
              "lagerCut240" INTEGER DEFAULT 0,
              "cutTT" INTEGER DEFAULT 0,
              "cutTR" INTEGER DEFAULT 0,
              "cutRR" INTEGER DEFAULT 0,
              "cutGesamt" INTEGER DEFAULT 0,
              "pickCut" INTEGER DEFAULT 0,
              "cutLager200" INTEGER DEFAULT 0,
              "cutLagerK200" INTEGER DEFAULT 0,
              "lagerCut200" INTEGER DEFAULT 0
            )
          `);
          await client.query(`CREATE INDEX "Ablaengerei_datum_idx" ON "Ablaengerei" ("Datum")`);
        } else if (table.name === 'WE') {
          await client.query(`
            CREATE TABLE "WE" (
              "id" SERIAL PRIMARY KEY,
              "Datum" TEXT,
              "weAtrl" INTEGER DEFAULT 0,
              "weManl" INTEGER DEFAULT 0
            )
          `);
          await client.query(`CREATE INDEX "WE_datum_idx" ON "WE" ("Datum")`);
        } else if (table.name === 'dispatch_data') {
          await client.query(`
            CREATE TABLE "dispatch_data" (
              "id" SERIAL PRIMARY KEY,
              "datum" TEXT,
              "tag" INTEGER,
              "monat" INTEGER,
              "kw" INTEGER,
              "jahr" INTEGER,
              "servicegrad" REAL,
              "ausgeliefert_lup" INTEGER,
              "rueckstaendig" INTEGER,
              "produzierte_tonnagen" REAL,
              "direktverladung_kiaa" INTEGER,
              "umschlag" INTEGER,
              "kg_pro_colli" REAL,
              "elefanten" INTEGER,
              "atrl" INTEGER,
              "aril" INTEGER,
              "fuellgrad_aril" REAL,
              "qm_angenommen" INTEGER,
              "qm_abgelehnt" INTEGER,
              "qm_offen" INTEGER,
              "mitarbeiter_std" REAL
            )
          `);
          await client.query(`CREATE INDEX "dispatch_data_datum_idx" ON "dispatch_data" ("datum")`);
        }

        console.log(`✅ Tabelle ${table.name} erfolgreich erstellt`);
      } else {
        console.log(`✅ Tabelle ${table.name} existiert bereits`);
      }
    }

    console.log('🎉 Migration erfolgreich abgeschlossen!');

  } catch (error) {
    console.error('❌ Fehler bei der Migration:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Migration ausführen
createMissingTables()
  .then(() => {
    console.log('✅ Datenbank-Migration erfolgreich beendet');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Migration fehlgeschlagen:', error);
    process.exit(1);
  });