/**
 * API-Konfigurations-Service
 * 
 * Verwaltet die sichere Laden der API-Konfiguration vom Main-Process
 * und stellt sie dem ApiService zur Verfügung.
 */

import { AppConfig } from '@/helpers/ipc/config/config-context';

/**
 * API-Konfiguration mit Standardwerten
 * Im Electron-Kontext verwenden wir immer die vollständige URL,
 * nur im Browser (ohne Electron) verwenden wir den Vite-Proxy
 */
let apiConfig: AppConfig = {
  apiKey: 'development-secret-key-for-workflow-testing-12345',
  // Standardmäßig verwenden wir localhost:3001 für Electron-Apps
  // Dies wird später durch die Konfiguration vom Main-Process überschrieben
  apiBaseUrl: 'http://localhost:3001/api',
  environment: 'development',
  version: '1.0.0'
};

// Debug: Logge die API-Konfiguration beim Laden
console.log('[API-CONFIG] Initiale API-Konfiguration:', {
  apiBaseUrl: apiConfig.apiBaseUrl,
  environment: apiConfig.environment,
  hasApiKey: !!apiConfig.apiKey
});

/**
 * Flag um zu verfolgen ob die Konfiguration geladen wurde
 */
let configLoaded = false;

/**
 * Promise für das Laden der Konfiguration (verhindert mehrfaches Laden)
 */
let configPromise: Promise<AppConfig> | null = null;

/**
 * Lädt die API-Konfiguration vom Main-Process
 * @returns Promise mit der geladenen Konfiguration
 */
export async function loadApiConfig(): Promise<AppConfig> {
  // Verhindere mehrfaches gleichzeitiges Laden
  if (configPromise) {
    return configPromise;
  }

  // Wenn bereits geladen, gib die aktuelle Konfiguration zurück
  if (configLoaded) {
    return apiConfig;
  }

  configPromise = (async () => {
    try {
      // Prüfe ob configAPI verfügbar ist (Electron-Umgebung)
      if (typeof window !== 'undefined' && (window as any).configAPI) {
        console.log('[CONFIG] Lade Konfiguration vom Main-Process...');
        
        const loadedConfig = await (window as any).configAPI.getConfig();
        apiConfig = loadedConfig;
        configLoaded = true;
        
        console.log('[CONFIG] ✅ Konfiguration erfolgreich geladen:', {
          apiBaseUrl: apiConfig.apiBaseUrl,
          environment: apiConfig.environment,
          version: apiConfig.version,
          hasApiKey: !!apiConfig.apiKey
        });

        // Lausche auf Konfigurationsänderungen
        (window as any).configAPI.onConfigUpdated((newConfig: AppConfig) => {
          console.log('[CONFIG] 🔄 Konfiguration aktualisiert');
          apiConfig = newConfig;
        });
        
        return apiConfig;
      } else {
        // Fallback für Nicht-Electron-Umgebungen (z.B. Tests)
        console.warn('[CONFIG] ⚠️ Electron configAPI nicht verfügbar, verwende Fallback-Konfiguration');
        configLoaded = true;
        return apiConfig;
      }
    } catch (error) {
      console.error('[CONFIG] ❌ Fehler beim Laden der Konfiguration:', error);
      // Bei Fehlern verwende Standardkonfiguration
      configLoaded = true;
      return apiConfig;
    } finally {
      configPromise = null;
    }
  })();

  return configPromise;
}

/**
 * Gibt die aktuelle API-Konfiguration zurück
 * Lädt sie falls notwendig vom Main-Process
 * @returns Promise mit der API-Konfiguration
 */
export async function getApiConfig(): Promise<AppConfig> {
  if (!configLoaded) {
    await loadApiConfig();
  }
  return apiConfig;
}

/**
 * Gibt die aktuelle API-Konfiguration synchron zurück
 * Sollte nur verwendet werden, wenn sicher ist, dass die Konfiguration bereits geladen wurde
 * @returns Aktuelle API-Konfiguration
 */
export function getApiConfigSync(): AppConfig {
  if (!configLoaded) {
    console.warn('[CONFIG] ⚠️ Konfiguration noch nicht geladen, verwende Standardwerte');
  }
  return apiConfig;
}

/**
 * Überprüft ob die API-Konfiguration geladen wurde
 * @returns True wenn die Konfiguration geladen wurde
 */
export function isConfigLoaded(): boolean {
  return configLoaded;
}

/**
 * Testet die API-Verbindung
 * @returns Promise<boolean> - true wenn API erreichbar ist
 */
export async function testApiConnection(): Promise<boolean> {
  try {
    const config = getApiConfigSync();
    const response = await fetch(`${config.apiBaseUrl.replace('/api', '')}/api/health`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('[API-TEST] ✅ API-Verbindung erfolgreich:', data);
      return true;
    } else {
      console.error('[API-TEST] ❌ API-Verbindung fehlgeschlagen:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('[API-TEST] ❌ API-Verbindung fehlgeschlagen:', error);
    return false;
  }
}

/**
 * Erstellt authentifizierte Headers mit JWT Token oder API-Konfiguration
 * @param additionalHeaders Zusätzliche Headers
 * @returns Headers-Objekt mit Authentifizierung
 */
export function createAuthHeaders(additionalHeaders: Record<string, string> = {}): Record<string, string> {
  const config = getApiConfigSync();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...additionalHeaders,
  };

  // Check if we're in a browser environment (renderer process)
  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    // Check for JWT token first (for user authentication)
    const jwtToken = localStorage.getItem('auth_token');

    if (jwtToken) {
      // Use JWT token for authenticated user requests
      headers['Authorization'] = `Bearer ${jwtToken}`;
      return headers;
    }
  }

  // For main process or when no JWT token is available, use API key
  if (config.apiKey) {
    headers['Authorization'] = `Bearer ${config.apiKey}`;
    headers['X-API-Key'] = config.apiKey;
  } else {
    console.warn('[CONFIG] ⚠️ Weder JWT-Token noch API-Schlüssel verfügbar');
  }

  return headers;
}