/**
 * Typdefinitionen für die Datenbank-API
 * 
 * Diese Definitionen ermöglichen den typisierten Zugriff auf die Datenbank
 * im Renderer-Prozess.
 */

/**
 * Servicelevel-Datenpunkt
 */
interface ServiceLevelDataPoint {
  datum: string;
  servicegrad: number;
  csr?: number; // Alias für servicegrad für Kompatibilität
}

/**
 * Tägliche Leistungsdaten
 */
interface DailyPerformanceDataPoint {
  datum: string;
  value: number;
}

/**
 * Kommissionierungsdaten
 */
interface PickingDataPoint {
  date: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
  name?: string; // Alias für Kompatibilität
  value?: number; // Alias für atrl für Kompatibilität
}

/**
 * Retourendaten
 */
interface ReturnsDataPoint {
  date: string;
  returns: number;
  name?: string; // Alias für Kompatibilität
  value?: number; // Alias für returns für Kompatibilität
}

/**
 * Lieferpositionsdaten
 */
interface DeliveryPositionsDataPoint {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

/**
 * Tagesleistungsdaten
 */
interface TagesleistungDataPoint {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

/**
 * Ablaengerei-Datenpunkt
 */
interface AblaengereiDataPoint {
  id?: number;
  datum: string;
  cutTT: number | null;
  cutTR: number | null;
  cutRR: number | null;
  pickCut: number | null;
  lagerCut220: number | null;
  lagerCut240: number | null;
  lagerCut200: number | null;
  cutLagerK200: number | null;
  cutLagerK240: number | null;
  cutLagerK220: number | null;
  cutLager200: number | null;
  cutLagerR240: number | null;
  cutLagerR220: number | null;
  
  // Alias-Felder für Kompatibilität
  lagerCut220?: number | null;
  lagerCut240?: number | null;
  lagerCut200?: number | null;
  cutLagerK200?: number | null;
  cutLagerK240?: number | null;
  cutLagerK220?: number | null;
  cutLager200?: number | null;
  cutLagerR240?: number | null;
  cutLagerR220?: number | null;
}

/**
 * WE-Datenpunkt (Wareneingang)
 */
interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;  // Entspricht atrl in dispatch_data
  weManl: number | null;  // Entspricht aril in dispatch_data
}
/**
 * Lagerauslastung200-Datenpunkt
 */
export interface Lagerauslastung200DataPoint {
  aufnahmeDatum: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

/**
 * Lagerauslastung240-Datenpunkt
 */
export interface Lagerauslastung240DataPoint {
  aufnahmeDatum: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

export interface ArilDataPoint {
  Datum: string;
  waTaPositionen: number;
  cuttingLagerKunde: number;
  cuttingLagerRest: number;
  Umlagerungen: number;
  lagerCutting: number;
}

export interface AtrlDataPoint {
  id?: number;
  Datum: string;
  weAtrl: number | null;
  EinlagerungAblKunde: number | null;
  EinlagerungAblRest: number | null;
  umlagerungen: number | null;
  waTaPositionen: number | null;
  AuslagerungAbl: number | null;
}

/**
 * Datenbank-API
 */
interface DatabaseAPI {
  /**
   * Ruft die Servicelevel-Daten für das Diagramm ab
   * @returns Promise mit den Servicelevel-Daten
   */
  getServiceLevelData: () => Promise<ServiceLevelDataPoint[]>;
  
  /**
   * Ruft die täglichen Leistungsdaten für das Diagramm ab
   * @returns Promise mit den täglichen Leistungsdaten
   */
  getDailyPerformanceData: () => Promise<DailyPerformanceDataPoint[]>;
  
  /**
   * Ruft die Kommissionierungsdaten für das Diagramm ab
   * @returns Promise mit den Kommissionierungsdaten
   */
  getPickingData: () => Promise<PickingDataPoint[]>;
  
  /**
   * Ruft die Retourendaten für das Diagramm ab
   * @returns Promise mit den Retourendaten
   */
  getReturnsData: () => Promise<ReturnsDataPoint[]>;
  
  /**
   * Ruft die Lieferpositionsdaten für das Diagramm ab
   * @returns Promise mit den Lieferpositionsdaten
   */
  getDeliveryPositionsData: () => Promise<DeliveryPositionsDataPoint[]>;
  
  /**
   * Ruft die Tagesleistungsdaten für das Diagramm ab
   * @returns Promise mit den Tagesleistungsdaten
   */
  getTagesleistungData: () => Promise<TagesleistungDataPoint[]>;
  
  // CSV-Import wurde entfernt, da nicht verwendet
  
  /**
   * Ruft die Daten aus der Ablaengerei-Tabelle ab
   * @returns Promise mit den Ablaengerei-Daten
   */
  getAblaengereiData: () => Promise<AblaengereiDataPoint[]>;

    /**
   * Ruft die Daten aus der ATrL-Tabelle ab
   * @returns Promise mit den ATrL-Daten
   */
  getAtrlData: () => Promise<AtrlDataPoint[]>;

    /**
   * Ruft die Daten aus der ARiL-Tabelle ab
   * @returns Promise mit den ARiL-Daten
   */
  getArilData: () => Promise<ArilDataPoint[]>;
  
  /**
   * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
   * @returns Promise mit den WE-Daten
   */
  getWEData: () => Promise<WEDataPoint[]>;

  /**
   * Ruft die Lagerauslastung200-Daten für das Diagramm ab
   * @returns Promise mit den Lagerauslastung200-Daten
   */
  getLagerauslastung200Data: () => Promise<Lagerauslastung200DataPoint[]>;

  /**
   * Ruft die Lagerauslastung240-Daten für das Diagramm ab
   * @returns Promise mit den Lagerauslastung240-Daten
   */
  getLagerauslastung240Data: () => Promise<Lagerauslastung240DataPoint[]>;

  /**
   * Gibt eine Liste aller Tabellen in der Datenbank zurück
   * @returns Array mit Tabellennamen
   */
  getTables: () => { name: string }[];

  /**
   * Liest Daten aus einer Tabelle aus
   * @param tableName Name der Tabelle
   * @param limit Maximale Anzahl der Zeilen
   * @returns Tabellendaten mit Spalten und Zeilen
   */
  getTableData: (tableName: string, limit?: number) => { columns: string[]; rows: any[] };
}

/**
 * Erweiterung der Window-Schnittstelle um die Datenbank-API
 */
interface Window {
  database: DatabaseAPI;
}
