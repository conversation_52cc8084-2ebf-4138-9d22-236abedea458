export enum StoerungStatus {
  NEU = "NEU",
  IN_BEARBEITUNG = "IN_BEARBEITUNG",
  ESKALIERT_L2 = "ESKALIERT_L2",
  ESKALIERT_L3 = "ESKALIERT_L3",
  GELÖST = "GELÖST",
  ABGESCHLOSSEN = "ABGESCHLOSSEN",
  REVIEW = "REVIEW",
}

export enum Eskalationslevel {
  L1 = "L1",
  L2 = "L2",
  L3 = "L3",
  TEAMLEAD = "TEAMLEAD",
  MANAGEMENT = "MANAGEMENT",
}

export interface ActionItem {
  id: number;
  stoerung_id: number;
  beschreibung: string;
  zustaendig: string;
  faellig_am: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  erledigt: boolean;
}

export interface Stoerung {
  id: number;
  title: string;
  description?: string;
  severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  status: StoerungStatus;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  created_at: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  updated_at: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  resolved_at?: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  acknowledged_at?: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  acknowledged_by?: string;
  escalation_level?: Eskalationslevel;
  resolution_steps?: string;
  root_cause?: string;
  lessons_learned?: string;
  action_items?: ActionItem[];
  runbook_ids?: number[];
  mttr_minutes?: number;
  mtta_minutes?: number;
  tags?: string[] | string;
  comments?: StoerungComment[];
}

export interface StoerungComment {
  id: number;
  stoerung_id: number;
  user_id?: string;
  comment: string;
  created_at: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
}

export interface StoerungAttachment {
  id: number;
  stoerungId: number;
  filename: string;
  storedName?: string | null; // Match actual DB field
  filePath?: string | null; // Match actual DB field
  fileSize: number;
  mimeType: string;
  fileType?: string | null; // Match actual DB field
  uploadedBy: string;
  createdAt: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  updatedAt: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
}

export interface StoerungCreateData {
  title: string;
  description?: string;
  severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  status?: StoerungStatus;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  tags?: string[] | string;
  send_protocol?: boolean;
}

export interface StoerungUpdateData {
  title?: string;
  description?: string;
  severity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  status?: StoerungStatus;
  category?: string;
  affected_system?: string;
  location?: string;
  assigned_to?: string;
  tags?: string[] | string;
  escalation_level?: Eskalationslevel;
  resolution_steps?: string;
  root_cause?: string;
  lessons_learned?: string;
  action_items?: ActionItem[];
  runbook_ids?: number[];
}

export interface StoerungsStats {
  total: number;
  active: number;
  resolved: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  avg_mttr_minutes: number;
  avg_mtta_minutes: number;
  first_time_fix_rate: number;
  resolution_rate_24h: number;
}

export interface SystemStatusMessage {
  id: number;
  title: string;
  description: string;
  priority: number;
  category: string;
}

export interface SystemStatus {
  id: number;
  system_name: string;
  status: "OK" | "WARNING" | "ERROR" | "OFF";
  last_check: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  metadata?: any;
  created_at: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  updated_at: string; // ISO 8601 format: "2025-09-23T10:30:45.000Z"
  statusMessages?: SystemStatusMessage[];
}

export interface SystemStatusUpdate {
  system_name: string;
  status: "OK" | "WARNING" | "ERROR" | "OFF";
  metadata?: any;
}

export const SEVERITY_COLORS = {
  LOW: "#10b981",
  MEDIUM: "#f59e0b",
  HIGH: "#ef4444",
  CRITICAL: "#dc2626",
} as const;

export const STATUS_COLORS = {
  [StoerungStatus.NEU]: "#3b82f6",
  [StoerungStatus.IN_BEARBEITUNG]: "#f59e0b",
  [StoerungStatus.ESKALIERT_L2]: "#9333ea",
  [StoerungStatus.ESKALIERT_L3]: "#c026d3",
  [StoerungStatus.GELÖST]: "#16a34a",
  [StoerungStatus.ABGESCHLOSSEN]: "#10b981",
  [StoerungStatus.REVIEW]: "#64748b",
} as const;

export const SYSTEM_STATUS_COLORS = {
  OK: "#10b981",
  WARNING: "#f59e0b",
  ERROR: "#ef4444",
  OFF: "#6b7280",
} as const;

// Zusätzliche Typen für Repository-Interfaces
export interface StoerungWithComments extends Stoerung {
  comments: StoerungComment[];
}

export interface StoerungCommentCreateData {
  stoerungId: number;
  userId?: string;
  comment: string;
  caretakerId?: string;
}

export interface SystemStatusData extends SystemStatus {}

export interface SystemStatusUpdateData extends SystemStatusUpdate {}

export interface StoerungsAttachmentCreateData {
  stoerungId: number;
  filename: string;
  storedName?: string;
  filePath?: string;
  fileSize: number;
  mimeType: string;
  fileType?: string;
  uploadedBy: string;
}
