/**
 * Supplier Repository Interface
 *
 * Definiert die Schnittstelle für Supplier-Datenoperationen
 */

import { SupplierRepositoryImpl } from '../supplier.repository';

export interface SupplierRepository {
  getAll(): Promise<any[]>;
  getSupplierStats(): Promise<any>;
  getSupplierPerformance(supplierId: string, timeRange: { days: number }): Promise<any>;
  getAllSuppliers(): Promise<any[]>;
  getSupplierById(supplierId: string): Promise<any | null>;
  getSuppliersByCategory(category: string): Promise<any[]>;
  getTopPerformingSuppliers(limit?: number): Promise<any[]>;
  getRiskSuppliers(): Promise<any[]>;
  updateSupplierPerformance(supplierId: string, metrics: any): Promise<void>;
  searchSuppliers(query: string): Promise<any[]>;
  getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<any[]>;
  getStats(): Promise<any>;
  invalidateCache(key?: string): Promise<void>;
}

export { SupplierRepositoryImpl };