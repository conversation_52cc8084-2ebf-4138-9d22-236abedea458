import apiService from './api.service';

export interface ServiceLevelDataPoint {
  datum: string;
  servicegrad: number;
}

export interface DeliveryPositionDataPoint {
  datum: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

export interface TagesleistungDataPoint {
  datum: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

export interface PickingDataPoint {
  datum: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
}

export interface DashboardStatistics {
  serviceLevel: {
    average: number;
    min: number;
    max: number;
    count: number;
  };
  deliveryPositions: {
    totalAusgeliefert: number;
    totalRueckstaendig: number;
    count: number;
  };
  tagesleistung: {
    totalProduzierteTonnagen: number;
    averageKgProColli: number;
    count: number;
  };
  picking: {
    totalAtrl: number;
    totalAril: number;
    averageFuellgrad: number;
    count: number;
  };
}

class DashboardApiService {
  private baseUrl = '/dashboard';

  // Helper method for making API requests
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const url = `${this.baseUrl}${endpoint}`;

      if (options?.method === 'POST') {
        return await apiService.post<T>(url, options.body ? JSON.parse(options.body as string) : undefined);
      } else if (options?.method === 'PUT') {
        return await apiService.put<T>(url, options.body ? JSON.parse(options.body as string) : undefined);
      } else if (options?.method === 'DELETE') {
        return await apiService.delete<T>(url);
      } else {
        return await apiService.get<T>(url);
      }
    } catch (error) {
      console.error(`Dashboard API Error (${endpoint}):`, error);
      throw error;
    }
  }

  /**
   * Holt ServiceGrad-Daten für Dashboard-Charts
   */
  async getServiceLevelData(startDate?: string, endDate?: string): Promise<ServiceLevelDataPoint[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{ success: boolean; data: ServiceLevelDataPoint[]; count: number }>(`/service-level${query}`);

      return response.data || [];
    } catch (error) {
      console.error('Error fetching service level data:', error);
      return [];
    }
  }

  /**
   * Holt Lieferpositionen-Daten für Dashboard-Charts
   */
  async getDeliveryPositionsData(startDate?: string, endDate?: string): Promise<DeliveryPositionDataPoint[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{ success: boolean; data: DeliveryPositionDataPoint[]; count: number }>(`/delivery-positions${query}`);

      return response.data || [];
    } catch (error) {
      console.error('Error fetching delivery positions data:', error);
      return [];
    }
  }

  /**
   * Holt Tagesleistung-Daten für Dashboard-Charts
   */
  async getTagesleistungData(startDate?: string, endDate?: string): Promise<TagesleistungDataPoint[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{ success: boolean; data: TagesleistungDataPoint[]; count: number }>(`/tagesleistung${query}`);

      return response.data || [];
    } catch (error) {
      console.error('Error fetching tagesleistung data:', error);
      return [];
    }
  }

  /**
   * Holt Picking-Daten für Dashboard-Charts
   */
  async getPickingData(startDate?: string, endDate?: string): Promise<PickingDataPoint[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{ success: boolean; data: PickingDataPoint[]; count: number }>(`/picking${query}`);

      return response.data || [];
    } catch (error) {
      console.error('Error fetching picking data:', error);
      return [];
    }
  }

  /**
   * Holt alle Dashboard-Daten in einer optimierten Abfrage
   */
  async getAllDashboardData(startDate?: string, endDate?: string): Promise<{
    serviceLevel: ServiceLevelDataPoint[];
    deliveryPositions: DeliveryPositionDataPoint[];
    tagesleistung: TagesleistungDataPoint[];
    picking: PickingDataPoint[];
  }> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{
        success: boolean;
        data: {
          serviceLevel: ServiceLevelDataPoint[];
          deliveryPositions: DeliveryPositionDataPoint[];
          tagesleistung: TagesleistungDataPoint[];
          picking: PickingDataPoint[];
        };
        counts: {
          serviceLevel: number;
          deliveryPositions: number;
          tagesleistung: number;
          picking: number;
        };
      }>(`/all${query}`);

      return response.data || {
        serviceLevel: [],
        deliveryPositions: [],
        tagesleistung: [],
        picking: []
      };
    } catch (error) {
      console.error('Error fetching all dashboard data:', error);
      return {
        serviceLevel: [],
        deliveryPositions: [],
        tagesleistung: [],
        picking: []
      };
    }
  }

  /**
   * Holt aggregierte Statistiken für Dashboard
   */
  async getDashboardStatistics(startDate?: string, endDate?: string): Promise<DashboardStatistics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{ success: boolean; data: DashboardStatistics }>(`/statistics${query}`);

      return response.data || {
        serviceLevel: { average: 0, min: 0, max: 0, count: 0 },
        deliveryPositions: { totalAusgeliefert: 0, totalRueckstaendig: 0, count: 0 },
        tagesleistung: { totalProduzierteTonnagen: 0, averageKgProColli: 0, count: 0 },
        picking: { totalAtrl: 0, totalAril: 0, averageFuellgrad: 0, count: 0 }
      };
    } catch (error) {
      console.error('Error fetching dashboard statistics:', error);
      return {
        serviceLevel: { average: 0, min: 0, max: 0, count: 0 },
        deliveryPositions: { totalAusgeliefert: 0, totalRueckstaendig: 0, count: 0 },
        tagesleistung: { totalProduzierteTonnagen: 0, averageKgProColli: 0, count: 0 },
        picking: { totalAtrl: 0, totalAril: 0, averageFuellgrad: 0, count: 0 }
      };
    }
  }

  /**
   * Prüft die Verfügbarkeit des Dashboard-Services
   */
  async getHealth(): Promise<{ status: string; message: string; dataAvailable: boolean }> {
    try {
      const response = await this.request<{ success: boolean; status: string; message: string; dataAvailable: boolean }>(`/health`);
      return response;
    } catch (error) {
      console.error('Error checking dashboard health:', error);
      return {
        status: 'unhealthy',
        message: 'Dashboard-Service ist nicht verfügbar',
        dataAvailable: false
      };
    }
  }

  /**
   * Batch-Operation für mehrere Datenarten
   */
  async getBatchData(dataTypes: string[], startDate?: string, endDate?: string): Promise<{
    serviceLevel?: ServiceLevelDataPoint[];
    deliveryPositions?: DeliveryPositionDataPoint[];
    tagesleistung?: TagesleistungDataPoint[];
    picking?: PickingDataPoint[];
  }> {
    try {
      const params = new URLSearchParams();
      params.append('types', dataTypes.join(','));
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const query = params.toString() ? `?${params.toString()}` : '';
      const response = await this.request<{
        success: boolean;
        data: {
          serviceLevel?: ServiceLevelDataPoint[];
          deliveryPositions?: DeliveryPositionDataPoint[];
          tagesleistung?: TagesleistungDataPoint[];
          picking?: PickingDataPoint[];
        };
      }>(`/batch${query}`);

      return response.data || {};
    } catch (error) {
      console.error('Error fetching batch data:', error);
      return {};
    }
  }
}

export const dashboardApiService = new DashboardApiService();