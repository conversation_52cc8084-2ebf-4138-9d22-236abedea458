"use strict";
/**
 * Production Repository Implementation
 *
 * Implementiert die IProductionRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Produktionsdaten.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionRepositoryImpl = void 0;
const db_1 = require("../db");
const drizzle_orm_1 = require("drizzle-orm");
const cache_service_1 = require("../services/cache.service");
const schema_1 = require("../db/schema");
/**
 * Cache-TTL für Production-Daten (verschiedene Datentypen)
 */
const PRODUCTION_CACHE_TTL = {
    SCHNITTE: 2 * 60 * 1000, // 2 Minuten
    EFFICIENCY: 5 * 60 * 1000, // 5 Minuten
    ABLAENGEREI: 3 * 60 * 1000, // 3 Minuten
    TRENDS: 10 * 60 * 1000, // 10 Minuten
    STATS: 15 * 60 * 1000, // 15 Minuten
};
/**
 * Schnitte Repository Implementation
 */
class SchnitteRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
        this.stats = {
            totalQueries: 0,
            cacheHits: 0,
            cacheMisses: 0,
            hitRate: 0,
            avgQueryTime: 0,
            lastAccessed: new Date()
        };
    }
    async getAll(filter) {
        const startTime = Date.now();
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forQuery('production', 'schnitte', filter);
        this.stats.totalQueries++;
        try {
            const data = await this.cache.cachedQuery(cacheKey, async () => {
                if (filter && filter.startDate && filter.endDate) {
                    return await db_1.db.select().from(schema_1.schnitte)
                        .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.schnitte.datum, filter.startDate), (0, drizzle_orm_1.lte)(schema_1.schnitte.datum, filter.endDate)))
                        .orderBy((0, drizzle_orm_1.desc)(schema_1.schnitte.datum))
                        .limit(1000);
                }
                return await db_1.db.select().from(schema_1.schnitte)
                    .orderBy((0, drizzle_orm_1.desc)(schema_1.schnitte.datum))
                    .limit(1000);
            }, PRODUCTION_CACHE_TTL.SCHNITTE);
            this.stats.cacheHits++;
            this.updateStats(startTime);
            return data;
        }
        catch (error) {
            console.error('Error fetching schnitte data:', error);
            this.stats.cacheMisses++;
            this.updateStats(startTime);
            throw error;
        }
    }
    async getGroupedByDate() {
        const data = await this.getAll();
        return data.reduce((grouped, item) => {
            const date = item.datum || 'unknown';
            if (!grouped[date]) {
                grouped[date] = [];
            }
            grouped[date].push(item);
            return grouped;
        }, {});
    }
    async getDailySums() {
        const data = await this.getAll();
        return data.map(item => ({
            date: item.datum || '',
            totalH1: item['Sum-H1'] || 0,
            totalH3: item['Sum-H3'] || 0,
            grandTotal: (item['Sum-H1'] || 0) + (item['Sum-H3'] || 0)
        }));
    }
    async getMachinePerformance() {
        const data = await this.getAll();
        // Extrahiere Maschinen-Spalten (M1-M27)
        const machineColumns = Object.keys(data[0] || {}).filter(key => key.match(/^M\d+/) && !key.includes('Sum'));
        return machineColumns.map(machine => {
            const machineCuts = data.map(item => item[machine] || 0);
            const totalCuts = machineCuts.reduce((sum, cuts) => sum + cuts, 0);
            const avgPerDay = totalCuts / data.length;
            return {
                machine,
                totalCuts,
                avgPerDay,
                efficiency: avgPerDay > 0 ? (totalCuts / (data.length * 100)) * 100 : 0
            };
        });
    }
    updateStats(startTime) {
        const queryTime = Date.now() - startTime;
        this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
        this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
        this.stats.lastAccessed = new Date();
    }
}
/**
 * Maschinen Efficiency Repository Implementation
 */
class MaschinenEfficiencyRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        // Mock implementation - in real app, this would query the database
        const mockData = [];
        for (let i = 0; i < 20; i++) {
            mockData.push({
                Datum: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                Machine: `M${i + 1}-T-H1`,
                sollSchnitte: 50 + Math.random() * 20,
                sollSchnitteProTag: (50 + Math.random() * 20) * 21,
                tagesSchnitte: Math.floor(Math.random() * 1000) + 500,
                istSchnitteProStunde: Math.random() * 50 + 20,
                effizienzProzent: Math.random() * 40 + 60
            });
        }
        return mockData;
    }
    async getTopPerformers(limit = 5) {
        const data = await this.getAll();
        return data
            .sort((a, b) => b.effizienzProzent - a.effizienzProzent)
            .slice(0, limit);
    }
    async getLowPerformers(threshold = 50) {
        const data = await this.getAll();
        return data.filter(item => item.effizienzProzent < threshold);
    }
    async getAverageEfficiency() {
        const data = await this.getAll();
        const efficiencies = data.map(item => item.effizienzProzent);
        const average = efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
        return {
            average,
            total: data.length,
            aboveAverage: efficiencies.filter(eff => eff >= average).length,
            belowAverage: efficiencies.filter(eff => eff < average).length
        };
    }
}
/**
 * Ablaengerei Repository Implementation
 */
class AblaengereiRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        if (filter && filter.startDate && filter.endDate) {
            return await db_1.db.select().from(schema_1.ablaengerei)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.ablaengerei.datum, filter.startDate), (0, drizzle_orm_1.lte)(schema_1.ablaengerei.datum, filter.endDate)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.ablaengerei.datum))
                .limit(1000);
        }
        return await db_1.db.select().from(schema_1.ablaengerei)
            .orderBy((0, drizzle_orm_1.asc)(schema_1.ablaengerei.datum))
            .limit(1000);
    }
    async getDailyStats() {
        const data = await this.getAll();
        return data.map(item => ({
            date: item.datum || '',
            total220: (item.cutLagerK220 || 0) + (item.cutLagerR220 || 0) + (item.lagerCut220 || 0),
            total240: (item.cutLagerK240 || 0) + (item.cutLagerR240 || 0) + (item.lagerCut240 || 0),
            total200: (item.cutLager200 || 0) + (item.cutLagerK200 || 0) + (item.lagerCut200 || 0),
            totalCuts: item.cutGesamt || 0,
            pickCut: item.pickCut || 0
        }));
    }
    async getWarehousePerformance() {
        const data = await this.getAll();
        const totals = data.reduce((acc, item) => {
            acc.total220 += (item.cutLagerK220 || 0) + (item.cutLagerR220 || 0) + (item.lagerCut220 || 0);
            acc.total240 += (item.cutLagerK240 || 0) + (item.cutLagerR240 || 0) + (item.lagerCut240 || 0);
            acc.total200 += (item.cutLager200 || 0) + (item.cutLagerK200 || 0) + (item.lagerCut200 || 0);
            acc.totalCuts += item.cutGesamt || 0;
            return acc;
        }, { total220: 0, total240: 0, total200: 0, totalCuts: 0 });
        return {
            warehouse220: {
                cuts: totals.total220,
                percentage: totals.totalCuts > 0 ? (totals.total220 / totals.totalCuts) * 100 : 0
            },
            warehouse240: {
                cuts: totals.total240,
                percentage: totals.totalCuts > 0 ? (totals.total240 / totals.totalCuts) * 100 : 0
            },
            warehouse200: {
                cuts: totals.total200,
                percentage: totals.totalCuts > 0 ? (totals.total200 / totals.totalCuts) * 100 : 0
            },
            totalCuts: totals.totalCuts
        };
    }
}
/**
 * Cutting Chart Repository Implementation
 */
class CuttingChartRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        // Mock implementation
        return [];
    }
    async getTrendAnalysis(days = 30) {
        // Mock implementation
        return {
            trend: 'stable',
            changePercentage: 0,
            averageDaily: 0,
            projectedNext: 0
        };
    }
}
/**
 * Lager Cuts Repository Implementation
 */
class LagerCutsRepositoryImpl {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)();
    }
    async getAll(filter) {
        // Mock implementation
        return [];
    }
    async getDistributionAnalysis() {
        // Mock implementation
        return {
            byWarehouse: {},
            totalCuts: 0,
            mostActiveWarehouse: '',
            efficiency: 0
        };
    }
}
/**
 * Production Repository Implementation
 */
class ProductionRepositoryImpl {
    constructor() {
        this.schnitte = new SchnitteRepositoryImpl();
        this.maschinenEfficiency = new MaschinenEfficiencyRepositoryImpl();
        this.ablaengerei = new AblaengereiRepositoryImpl();
        this.cuttingChart = new CuttingChartRepositoryImpl();
        this.lagerCuts = new LagerCutsRepositoryImpl();
    }
    invalidateAllCache() {
        // Implementation for cache invalidation
        console.log('Invalidating all production cache');
    }
    async getOverallProductionStats() {
        const [dailySums, efficiency, warehousePerf, trends] = await Promise.all([
            this.schnitte.getDailySums(),
            this.maschinenEfficiency.getAverageEfficiency(),
            this.ablaengerei.getWarehousePerformance(),
            this.cuttingChart.getTrendAnalysis()
        ]);
        return {
            dailyAverages: dailySums,
            machineEfficiency: efficiency,
            warehousePerformance: warehousePerf,
            trends
        };
    }
}
exports.ProductionRepositoryImpl = ProductionRepositoryImpl;
