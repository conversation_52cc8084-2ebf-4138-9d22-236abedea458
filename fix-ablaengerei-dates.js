/**
 * Script zum Beheben der fehlenden Daten in der Ablaengerei-Tabelle
 * Das Problem: Alle Datensätze haben datum: '' (leeres Datum)
 */

const axios = require('axios');

// Backend API Base URL
const API_BASE_URL = 'http://localhost:3001/api';

async function fixAblaengereiDates() {
  console.log('🔧 Behebe fehlende Daten in Ablaengerei-Tabelle...\n');

  try {
    // 1. Hole alle Ablaengerei-Daten
    console.log('1️⃣ Hole alle Ablaengerei-Daten...');
    const response = await axios.get(`${API_BASE_URL}/database/ablaengerei`);
    const ablaengereiData = response.data?.data || [];

    console.log(`📊 Gefunden: ${ablaengereiData.length} Datensätze`);

    if (ablaengereiData.length === 0) {
      console.log('❌ Keine Ablaengerei-Daten gefunden');
      return;
    }

    // 2. Analysiere die Daten
    console.log('\n2️⃣ Analysiere Daten...');
    const emptyDates = ablaengereiData.filter(item => !item.datum || item.datum === '').length;
    const validDates = ablaengereiData.filter(item => item.datum && item.datum !== '').length;

    console.log(`📅 Leere Daten: ${emptyDates}`);
    console.log(`📅 Gültige Daten: ${validDates}`);

    // 3. Hole Schnitte-Daten für Datumsreferenz
    console.log('\n3️⃣ Hole Schnitte-Daten für Datumsreferenz...');
    const schnitteResponse = await axios.get(`${API_BASE_URL}/database/schnitte-data`);
    const schnitteData = schnitteResponse.data?.data || [];

    console.log(`📊 Schnitte-Daten: ${schnitteData.length} Datensätze`);

    if (schnitteData.length === 0) {
      console.log('❌ Keine Schnitte-Daten für Referenz gefunden');
      return;
    }

    // 4. Erstelle Mapping von Schnitte-Daten zu Ablaengerei-Daten
    console.log('\n4️⃣ Erstelle Datums-Mapping...');

    // Gruppiere Schnitte-Daten nach Datum
    const schnitteByDate = new Map();
    schnitteData.forEach(item => {
      if (item.datum) {
        schnitteByDate.set(item.datum, item);
      }
    });

    console.log(`📅 Verfügbare Daten in Schnitte: ${schnitteByDate.size}`);

    // 5. Aktualisiere Ablaengerei-Daten mit Daten
    console.log('\n5️⃣ Aktualisiere Ablaengerei-Daten...');

    // Nimm die neuesten Daten aus Schnitte als Referenz
    const sortedDates = Array.from(schnitteByDate.keys()).sort().reverse();
    const recentDates = sortedDates.slice(0, Math.min(20, sortedDates.length));

    console.log(`📅 Verwende Daten: ${recentDates.slice(0, 5).join(', ')}${recentDates.length > 5 ? '...' : ''}`);

    // Simuliere die Aktualisierung (in echtem Szenario würde das über eine Admin-API gehen)
    console.log('\n✅ SIMULATION: Ablaengerei-Daten würden aktualisiert mit:');
    recentDates.forEach((date, index) => {
      if (index < 5) { // Zeige nur erste 5
        console.log(`  ${date}: ${ablaengereiData[index]?.id || 'N/A'} -> datum: '${date}'`);
      }
    });

    // 6. Teste die aktualisierten Daten
    console.log('\n6️⃣ Teste aktualisierte Cutting-Chart-Daten...');

    // Teste mit einem der verfügbaren Daten
    if (recentDates.length > 0) {
      const testDate = recentDates[0];
      console.log(`🧪 Teste mit Datum: ${testDate}`);

      const testResponse = await axios.get(`${API_BASE_URL}/database/cutting-chart-data`, {
        params: {
          startDate: testDate,
          endDate: testDate
        }
      });

      console.log(`✅ Test-Ergebnis: ${testResponse.data?.data?.length || 0} Datensätze`);
    }

    // 7. Zusammenfassung
    console.log('\n📋 ZUSAMMENFASSUNG:');
    console.log(`- Ablaengerei-Tabelle: ${ablaengereiData.length} Datensätze (alle ohne Datum)`);
    console.log(`- Schnitte-Tabelle: ${schnitteData.length} Datensätze (mit gültigen Daten)`);
    console.log(`- Verfügbare Daten: ${recentDates.length} Tage`);
    console.log(`- Problem: Ablaengerei-Daten haben keine datum-Werte`);
    console.log(`- Lösung: datum-Felder in Ablaengerei-Tabelle mit Schnitte-Daten füllen`);

    console.log('\n💡 EMPFEHLUNG:');
    console.log('1. Aktualisiere die datum-Felder in der ablaengerei-Tabelle');
    console.log('2. Verwende die Daten aus der schnitte-Tabelle als Referenz');
    console.log('3. Stelle sicher, dass beide Tabellen synchronisiert sind');

  } catch (error) {
    console.error('❌ Fehler beim Beheben der Daten:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

// Führe Script aus
fixAblaengereiDates();