# SFM Electron

SFM Electron ist eine Desktop-Anwendung zur Visualisierung von Logistik- und Produktionsdaten. Sie bietet ein Dashboard mit verschiedenen Diagrammen und Statistiken, um Einblicke in wichtige Kennzahlen zu geben.

## Funktionen

- **Dashboard-Ansicht:** Eine zentrale Ansicht mit den wichtigsten Diagrammen und Kennzahlen.
- **Interaktive Diagramme:** Visualisierung von Daten wie:
  - Wareneingangspositionen
  - Lieferpositionen
  - Schnitte
  - Kommissionierung
  - QM-Meldungen
  - Servicegrad
  - Tagesleistung
- **Datentabellen:** Detaillierte Tabellenansichten der Daten.
- **Mehrsprachigkeit:** Unterstützung für die deutsche Sprache.
- **Dunkel-/Hell-Modus:** Anpassbares Anwendungs-Theme.

## Technologie-Stack

- **Core:** Electron, Vite, SWC
- **UI:** React, Tailwind CSS, shadcn/ui, Recharts
- **State Management:** React Query (TanStack)
- **Routing:** TanStack Router
- **Datenbank:** PostgreSQL mit Drizzle ORM
- **Sprachen:** TypeScript
- **Testing:** Vitest, Playwright

## Verzeichnisstruktur

```plaintext
.
└── ./src/
    ├── ./src/app/
    ├── ./src/assets/
    ├── ./src/components/
    ├── ./src/helpers/
    ├── ./src/hooks/
    ├── ./src/i18n/
    ├── ./src/layouts/
    ├── ./src/lib/
    ├── ./src/localization/
    ├── ./src/pages/
    ├── ./src/routes/
    ├── ./src/services/
    ├── ./src/styles/
    └── ./src/types/
```

## PNPM-Skripte

- `run dev`: Startet die App im Entwicklungsmodus.
- `package`: Verpackt Ihre Anwendung in ein plattformspezifisches ausführbares Bündel.
- `make`: Erstellt plattformspezifische Distributionen (z. B. .exe, .dmg).
- `lint`: Führt ESLint aus, um den Code zu überprüfen.
- `test`: Führt die Unit-Tests mit Vitest aus.
- `test:e2e`: Führt die End-to-End-Tests mit Playwright aus.

## Wie man es benutzt

1. **Abhängigkeiten installieren**
   ```bash
   pnpm install
   ```

2. **App starten**
   ```bash
   pnpm run start
   ```
2. **Postgres Server Starten**
   ```bash
   cd G:\Programma\PostgreSQL\data
   & "G:\Programma\PostgreSQL\bin\pg_ctl.exe" start -D .
   ```
taskkill /f /im node.exe
## Lizenz

Dieses Projekt ist unter der MIT-Lizenz lizenziert - siehe die [LICENSE](LICENSE)-Datei für Details.
