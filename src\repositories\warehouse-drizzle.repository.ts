import { eq, gte, lte, desc, and } from 'drizzle-orm';
import { db, auslastung200, auslastung240, ariL, atrL } from '../../backend/src/db';

// Types (simplified versions of the original interfaces)
export interface DateRange {
  startDate: string;
  endDate: string;
}

export interface Lagerauslastung200DataPoint {
  aufnahmeDatum: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt: number;
}

export interface Lagerauslastung240DataPoint {
  aufnahmeDatum: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt: number;
}

export interface ArilDataPoint {
  id: number;
  Datum: string;
  waTaPositionen: number;
  cuttingLagerKunde: number;
  cuttingLagerRest: number;
  Umlagerungen: number;
  lagerCutting: number;
}

export interface AtrlDataPoint {
  id: number;
  datum: string;
  umlagerungen: number;
  waTaPositionen: number;
  belegtePlaetze: number;
}

// Simple in-memory cache for warehouse data
const warehouseCache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = warehouseCache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  warehouseCache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttlMs: number = 300000) => {
  warehouseCache.set(key, { data, expires: Date.now() + ttlMs });
};

export class WarehouseDrizzleRepository {
  private static instance: WarehouseDrizzleRepository;

  constructor() {}

  static getInstance(): WarehouseDrizzleRepository {
    if (!WarehouseDrizzleRepository.instance) {
      WarehouseDrizzleRepository.instance = new WarehouseDrizzleRepository();
    }
    return WarehouseDrizzleRepository.instance;
  }

  /**
   * Lagerauslastung 200 Daten abrufen
   */
  async getLagerauslastung200Data(dateRange?: DateRange): Promise<Lagerauslastung200DataPoint[]> {
    const cacheKey = `lagerauslastung200:${JSON.stringify(dateRange || {})}`;
    const cached = getCached<Lagerauslastung200DataPoint[]>(cacheKey);
    if (cached) return cached;

    try {
      let query = db
        .select()
        .from(auslastung200)
        .orderBy(desc(auslastung200.aufnahmeDatum))
        .limit(1000);

      if (dateRange) {
        query = query.where(and(
          gte(auslastung200.aufnahmeDatum, dateRange.startDate),
          lte(auslastung200.aufnahmeDatum, dateRange.endDate)
        )) as any;
      }

      const data = await query;
      
      const formattedData: Lagerauslastung200DataPoint[] = data.map((item: any) => ({
        aufnahmeDatum: item.aufnahmeDatum || '',
        auslastungA: parseFloat(item.auslastungA || '0'),
        auslastungB: parseFloat(item.auslastungB || '0'),
        auslastungC: parseFloat(item.auslastungC || '0'),
        gesamt: parseFloat(item.auslastungA || '0') + parseFloat(item.auslastungB || '0') + parseFloat(item.auslastungC || '0')
      }));

      setCache(cacheKey, formattedData, 120000); // 2 minutes
      return formattedData;

    } catch (error) {
      console.error('Error fetching Lagerauslastung200Data:', error);
      throw error;
    }
  }

  /**
   * Lagerauslastung 240 Daten abrufen
   */
  async getLagerauslastung240Data(dateRange?: DateRange): Promise<Lagerauslastung240DataPoint[]> {
    const cacheKey = `lagerauslastung240:${JSON.stringify(dateRange || {})}`;
    const cached = getCached<Lagerauslastung240DataPoint[]>(cacheKey);
    if (cached) return cached;

    try {
      let query = db
        .select()
        .from(auslastung240)
        .orderBy(desc(auslastung240.aufnahmeDatum))
        .limit(1000);

      if (dateRange) {
        query = query.where(and(
          gte(auslastung240.aufnahmeDatum, dateRange.startDate),
          lte(auslastung240.aufnahmeDatum, dateRange.endDate)
        )) as any;
      }

      const data = await query;
      
      const formattedData: Lagerauslastung240DataPoint[] = data.map((item: any) => ({
        aufnahmeDatum: item.aufnahmeDatum || '',
        auslastungA: parseFloat(item.auslastungA || '0'),
        auslastungB: parseFloat(item.auslastungB || '0'),
        auslastungC: parseFloat(item.auslastungC || '0'),
        gesamt: parseFloat(item.auslastungA || '0') + parseFloat(item.auslastungB || '0') + parseFloat(item.auslastungC || '0')
      }));

      setCache(cacheKey, formattedData, 120000); // 2 minutes
      return formattedData;

    } catch (error) {
      console.error('Error fetching Lagerauslastung240Data:', error);
      throw error;
    }
  }

  // Hilfsmethoden
  async clearCache(): Promise<void> {
    warehouseCache.clear();
  }

  getCacheStats() {
    return {
      size: warehouseCache.size,
      entries: Array.from(warehouseCache.keys())
    };
  }
}

// Export singleton instance
export const warehouseDrizzleRepository = WarehouseDrizzleRepository.getInstance();
