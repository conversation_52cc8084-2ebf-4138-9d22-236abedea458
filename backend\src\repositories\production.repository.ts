/**
 * Production Repository Implementation
 *
 * Implementiert die IProductionRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Produktionsdaten.
 */

import { db } from '../db';
import { eq, gte, lte, desc, asc, and, sql } from 'drizzle-orm';
import { getBackendCache, BackendCacheKeyGenerator } from '../services/cache.service';
import { ablaengerei, schnitte, maschinen } from '../db/schema';

// Type definitions for production operations
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

export interface ProductionFilter extends DateRangeFilter {
  machineId?: string;
  productionType?: 'cutting' | 'ablängerei' | 'efficiency';
  minEfficiency?: number;
  maxEfficiency?: number;
}

export interface SchnitteData {
  Datum?: string | null;
  datum?: string | null;
  [key: string]: any; // Für dynamische Maschinenspalten
}

export interface SchnitteSummary {
  date: string;
  totalH1: number;
  totalH3: number;
  grandTotal: number;
}

export interface MachinePerformance {
  machine: string;
  totalCuts: number;
  avgPerDay: number;
  efficiency: number;
}

export interface MachineEfficiencyData {
  Datum: string;
  Machine: string;
  sollSchnitte: number;
  sollSchnitteProTag: number;
  tagesSchnitte: number;
  istSchnitteProStunde: number;
  effizienzProzent: number;
}

export interface EfficiencyStats {
  average: number;
  total: number;
  aboveAverage: number;
  belowAverage: number;
}

export interface AblaengereiStats {
  date: string;
  total220: number;
  total240: number;
  total200: number;
  totalCuts: number;
  pickCut: number;
}

export interface WarehousePerformance {
  warehouse220: { cuts: number; percentage: number };
  warehouse240: { cuts: number; percentage: number };
  warehouse200: { cuts: number; percentage: number };
  totalCuts: number;
}

export interface CuttingTrendAnalysis {
  trend: 'increasing' | 'decreasing' | 'stable';
  changePercentage: number;
  averageDaily: number;
  projectedNext: number;
}

export interface WarehouseDistribution {
  byWarehouse: Record<string, number>;
  totalCuts: number;
  mostActiveWarehouse: string;
  efficiency: number;
}

export interface RepositoryStats {
  totalQueries: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  avgQueryTime: number;
  lastAccessed: Date;
}

export interface ProductionStats {
  dailyAverages: SchnitteSummary[];
  machineEfficiency: EfficiencyStats;
  warehousePerformance: WarehousePerformance;
  trends: CuttingTrendAnalysis;
}

export interface ISchnitteRepository {
  getAll(filter?: DateRangeFilter): Promise<any[]>;
  getGroupedByDate(): Promise<Record<string, any[]>>;
  getDailySums(): Promise<SchnitteSummary[]>;
  getMachinePerformance(): Promise<MachinePerformance[]>;
}

export interface IMaschinenEfficiencyRepository {
  getAll(filter?: ProductionFilter): Promise<MachineEfficiencyData[]>;
  getTopPerformers(limit?: number): Promise<MachineEfficiencyData[]>;
  getLowPerformers(threshold?: number): Promise<MachineEfficiencyData[]>;
  getAverageEfficiency(): Promise<EfficiencyStats>;
}

export interface IAblaengereiRepository {
  getAll(filter?: DateRangeFilter): Promise<any[]>;
  getDailyStats(): Promise<AblaengereiStats[]>;
  getWarehousePerformance(): Promise<WarehousePerformance>;
}

export interface ICuttingChartRepository {
  getAll(filter?: DateRangeFilter): Promise<any[]>;
  getTrendAnalysis(days?: number): Promise<CuttingTrendAnalysis>;
}

export interface ILagerCutsRepository {
  getAll(filter?: DateRangeFilter): Promise<any[]>;
  getDistributionAnalysis(): Promise<WarehouseDistribution>;
}

export interface IProductionRepository {
  schnitte: ISchnitteRepository;
  maschinenEfficiency: IMaschinenEfficiencyRepository;
  ablaengerei: IAblaengereiRepository;
  cuttingChart: ICuttingChartRepository;
  lagerCuts: ILagerCutsRepository;

  invalidateAllCache(): void;
  getOverallProductionStats(): Promise<ProductionStats>;
}

/**
 * Cache-TTL für Production-Daten (verschiedene Datentypen)
 */
const PRODUCTION_CACHE_TTL = {
  SCHNITTE: 2 * 60 * 1000, // 2 Minuten
  EFFICIENCY: 5 * 60 * 1000, // 5 Minuten
  ABLAENGEREI: 3 * 60 * 1000, // 3 Minuten
  TRENDS: 10 * 60 * 1000, // 10 Minuten
  STATS: 15 * 60 * 1000, // 15 Minuten
};

/**
 * Schnitte Repository Implementation
 */
class SchnitteRepositoryImpl implements ISchnitteRepository {
  private cache = getBackendCache();
  private stats: RepositoryStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    avgQueryTime: 0,
    lastAccessed: new Date()
  };

  async getAll(filter?: DateRangeFilter): Promise<SchnitteData[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.forQuery('production', 'schnitte', filter);

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          if (filter && filter.startDate && filter.endDate) {
            return await db.select().from(schnitte)
              .where(
                and(
                  gte(schnitte.datum, filter.startDate),
                  lte(schnitte.datum, filter.endDate)
                )
              )
              .orderBy(desc(schnitte.datum))
              .limit(1000);
          }

          return await db.select().from(schnitte)
            .orderBy(desc(schnitte.datum))
            .limit(1000);
        },
        PRODUCTION_CACHE_TTL.SCHNITTE
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching schnitte data:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  async getGroupedByDate(): Promise<Record<string, SchnitteData[]>> {
    const data = await this.getAll();
    return data.reduce((grouped, item) => {
      const date = item.datum || item.Datum || 'unknown';
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(item);
      return grouped;
    }, {} as Record<string, SchnitteData[]>);
  }

  async getDailySums(): Promise<SchnitteSummary[]> {
    const data = await this.getAll();

    return data.map(item => ({
      date: item.datum || item.Datum || '',
      totalH1: item['Sum-H1'] || 0,
      totalH3: item['Sum-H3'] || 0,
      grandTotal: (item['Sum-H1'] || 0) + (item['Sum-H3'] || 0)
    }));
  }

  async getMachinePerformance(): Promise<MachinePerformance[]> {
    const data = await this.getAll();

    // Extrahiere Maschinen-Spalten (M1-M27)
    const machineColumns = Object.keys(data[0] || {}).filter(key =>
      key.match(/^M\d+/) && !key.includes('Sum')
    );

    return machineColumns.map(machine => {
      const machineCuts = data.map(item => item[machine] || 0);
      const totalCuts = machineCuts.reduce((sum, cuts) => sum + cuts, 0);
      const avgPerDay = totalCuts / data.length;

      return {
        machine,
        totalCuts,
        avgPerDay,
        efficiency: avgPerDay > 0 ? (totalCuts / (data.length * 100)) * 100 : 0
      };
    });
  }

  private updateStats(startTime: number): void {
    const queryTime = Date.now() - startTime;
    this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
    this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
    this.stats.lastAccessed = new Date();
  }
}

/**
 * Maschinen Efficiency Repository Implementation
 */
class MaschinenEfficiencyRepositoryImpl implements IMaschinenEfficiencyRepository {
  private cache = getBackendCache();

  async getAll(filter?: ProductionFilter): Promise<MachineEfficiencyData[]> {
    // Mock implementation - in real app, this would query the database
    const mockData: MachineEfficiencyData[] = [];

    for (let i = 0; i < 20; i++) {
      mockData.push({
        Datum: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        Machine: `M${i + 1}-T-H1`,
        sollSchnitte: 50 + Math.random() * 20,
        sollSchnitteProTag: (50 + Math.random() * 20) * 21,
        tagesSchnitte: Math.floor(Math.random() * 1000) + 500,
        istSchnitteProStunde: Math.random() * 50 + 20,
        effizienzProzent: Math.random() * 40 + 60
      });
    }

    return mockData;
  }

  async getTopPerformers(limit: number = 5): Promise<MachineEfficiencyData[]> {
    const data = await this.getAll();
    return data
      .sort((a, b) => b.effizienzProzent - a.effizienzProzent)
      .slice(0, limit);
  }

  async getLowPerformers(threshold: number = 50): Promise<MachineEfficiencyData[]> {
    const data = await this.getAll();
    return data.filter(item => item.effizienzProzent < threshold);
  }

  async getAverageEfficiency(): Promise<EfficiencyStats> {
    const data = await this.getAll();
    const efficiencies = data.map(item => item.effizienzProzent);
    const average = efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;

    return {
      average,
      total: data.length,
      aboveAverage: efficiencies.filter(eff => eff >= average).length,
      belowAverage: efficiencies.filter(eff => eff < average).length
    };
  }
}

/**
 * Ablaengerei Repository Implementation
 */
class AblaengereiRepositoryImpl implements IAblaengereiRepository {
  private cache = getBackendCache();

  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    if (filter && filter.startDate && filter.endDate) {
      return await db.select().from(ablaengerei)
        .where(
          and(
            gte(ablaengerei.Datum, filter.startDate),
            lte(ablaengerei.Datum, filter.endDate)
          )
        )
        .orderBy(asc(ablaengerei.Datum))
        .limit(1000);
    }

    return await db.select().from(ablaengerei)
      .orderBy(asc(ablaengerei.Datum))
      .limit(1000);
  }

  async getDailyStats(): Promise<AblaengereiStats[]> {
    const data = await this.getAll();

    return data.map(item => ({
      date: item.datum || '',
      total220: (item.cutLagerK220 || 0) + (item.cutLagerR220 || 0) + (item.lagerCut220 || 0),
      total240: (item.cutLagerK240 || 0) + (item.cutLagerR240 || 0) + (item.lagerCut240 || 0),
      total200: (item.cutLager200 || 0) + (item.cutLagerK200 || 0) + (item.lagerCut200 || 0),
      totalCuts: (item as any).cutGesamt || 0,
      pickCut: item.pickCut || 0
    }));
  }

  async getWarehousePerformance(): Promise<WarehousePerformance> {
    const data = await this.getAll();

    const totals = data.reduce((acc, item) => {
      acc.total220 += (item.cutLagerK220 || 0) + (item.cutLagerR220 || 0) + (item.lagerCut220 || 0);
      acc.total240 += (item.cutLagerK240 || 0) + (item.cutLagerR240 || 0) + (item.lagerCut240 || 0);
      acc.total200 += (item.cutLager200 || 0) + (item.cutLagerK200 || 0) + (item.lagerCut200 || 0);
      acc.totalCuts += (item as any).cutGesamt || 0;
      return acc;
    }, { total220: 0, total240: 0, total200: 0, totalCuts: 0 });

    return {
      warehouse220: {
        cuts: totals.total220,
        percentage: totals.totalCuts > 0 ? (totals.total220 / totals.totalCuts) * 100 : 0
      },
      warehouse240: {
        cuts: totals.total240,
        percentage: totals.totalCuts > 0 ? (totals.total240 / totals.totalCuts) * 100 : 0
      },
      warehouse200: {
        cuts: totals.total200,
        percentage: totals.totalCuts > 0 ? (totals.total200 / totals.totalCuts) * 100 : 0
      },
      totalCuts: totals.totalCuts
    };
  }
}

/**
 * Cutting Chart Repository Implementation
 */
class CuttingChartRepositoryImpl implements ICuttingChartRepository {
  private cache = getBackendCache();

  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    // Mock implementation
    return [];
  }

  async getTrendAnalysis(days: number = 30): Promise<CuttingTrendAnalysis> {
    // Mock implementation
    return {
      trend: 'stable',
      changePercentage: 0,
      averageDaily: 0,
      projectedNext: 0
    };
  }
}

/**
 * Lager Cuts Repository Implementation
 */
class LagerCutsRepositoryImpl implements ILagerCutsRepository {
  private cache = getBackendCache();

  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    // Mock implementation
    return [];
  }

  async getDistributionAnalysis(): Promise<WarehouseDistribution> {
    // Mock implementation
    return {
      byWarehouse: {},
      totalCuts: 0,
      mostActiveWarehouse: '',
      efficiency: 0
    };
  }
}

/**
 * Production Repository Implementation
 */
export class ProductionRepositoryImpl implements IProductionRepository {
  public readonly schnitte: ISchnitteRepository;
  public readonly maschinenEfficiency: IMaschinenEfficiencyRepository;
  public readonly ablaengerei: IAblaengereiRepository;
  public readonly cuttingChart: ICuttingChartRepository;
  public readonly lagerCuts: ILagerCutsRepository;

  constructor() {
    this.schnitte = new SchnitteRepositoryImpl();
    this.maschinenEfficiency = new MaschinenEfficiencyRepositoryImpl();
    this.ablaengerei = new AblaengereiRepositoryImpl();
    this.cuttingChart = new CuttingChartRepositoryImpl();
    this.lagerCuts = new LagerCutsRepositoryImpl();
  }

  invalidateAllCache(): void {
    // Implementation for cache invalidation
    console.log('Invalidating all production cache');
  }

  async getOverallProductionStats(): Promise<ProductionStats> {
    const [dailySums, efficiency, warehousePerf, trends] = await Promise.all([
      this.schnitte.getDailySums(),
      this.maschinenEfficiency.getAverageEfficiency(),
      this.ablaengerei.getWarehousePerformance(),
      this.cuttingChart.getTrendAnalysis()
    ]);

    return {
      dailyAverages: dailySums,
      machineEfficiency: efficiency,
      warehousePerformance: warehousePerf,
      trends
    };
  }
}