/**
 * Datenbank-Inhalt überprüfen
 * Verwendet Drizzle ORM um direkt in die Datenbank zu schauen
 */

const { db } = require('./backend/src/db');
const { dispatchData } = require('./backend/src/db/schema');
const { desc, sql } = require('drizzle-orm');

async function checkDatabaseContent() {
  console.log('🔍 Überprüfe Datenbank-Inhalt...\n');

  try {
    // 1. Anzahl der Datensätze in dispatch_data Tabelle
    console.log('1️⃣ Anzahl der Datensätze in dispatch_data Tabelle...');
    const totalCount = await db.select({ count: sql`count(*)` }).from(dispatchData);
    console.log(`📊 Gesamtanzahl Datensätze: ${totalCount[0].count}`);

    // 2. Erste 5 Datensätze anzeigen
    console.log('\n2️⃣ Erste 5 Datensätze:');
    const firstFive = await db.select().from(dispatchData).limit(5);
    console.log('📋 Erste 5 Datensätze:');
    firstFive.forEach((row, index) => {
      console.log(`  ${index + 1}.`, {
        id: row.id,
        datum: row.datum,
        servicegrad: row.servicegrad,
        produzierte_tonnagen: row.produzierte_tonnagen,
        atrl: row.atrl,
        aril: row.aril
      });
    });

    // 3. Datensätze mit servicegrad != null
    console.log('\n3️⃣ Datensätze mit servicegrad != null:');
    const serviceGradData = await db.select()
      .from(dispatchData)
      .where(sql`${dispatchData.servicegrad} IS NOT NULL`)
      .limit(10);

    console.log(`📈 Anzahl Datensätze mit servicegrad: ${serviceGradData.length}`);
    if (serviceGradData.length > 0) {
      console.log('📋 Beispiele:');
      serviceGradData.slice(0, 5).forEach((row, index) => {
        console.log(`  ${index + 1}.`, {
          datum: row.datum,
          servicegrad: row.servicegrad,
          produzierte_tonnagen: row.produzierte_tonnagen
        });
      });
    }

    // 4. Datumsbereich analysieren
    console.log('\n4️⃣ Datumsbereich-Analyse:');
    const dateStats = await db.select({
      minDate: sql`MIN(${dispatchData.datum})`,
      maxDate: sql`MAX(${dispatchData.datum})`,
      distinctDates: sql`COUNT(DISTINCT ${dispatchData.datum})`
    }).from(dispatchData);

    console.log('📅 Datumsbereich:', {
      minDate: dateStats[0].minDate,
      maxDate: dateStats[0].maxDate,
      distinctDates: dateStats[0].distinctDates
    });

    // 5. Test mit spezifischem Zeitraum (2025-07-03 bis 2025-07-30)
    console.log('\n5️⃣ Test mit Zeitraum 2025-07-03 bis 2025-07-30:');
    const testRangeData = await db.select()
      .from(dispatchData)
      .where(sql`${dispatchData.datum} >= '2025-07-03' AND ${dispatchData.datum} <= '2025-07-30'`)
      .limit(10);

    console.log(`📊 Datensätze im Zeitraum: ${testRangeData.length}`);
    if (testRangeData.length > 0) {
      console.log('📋 Beispiele:');
      testRangeData.forEach((row, index) => {
        console.log(`  ${index + 1}.`, {
          datum: row.datum,
          servicegrad: row.servicegrad,
          produzierte_tonnagen: row.produzierte_tonnagen
        });
      });
    }

    // 6. Aktuelle Daten (letzte 30 Tage)
    console.log('\n6️⃣ Aktuelle Daten (letzte 30 Tage):');
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentDateString = thirtyDaysAgo.toISOString().split('T')[0];

    const recentData = await db.select()
      .from(dispatchData)
      .where(sql`${dispatchData.datum} >= ${recentDateString}`)
      .limit(10);

    console.log(`📊 Datensätze in letzten 30 Tagen: ${recentData.length}`);
    if (recentData.length > 0) {
      console.log('📋 Beispiele:');
      recentData.forEach((row, index) => {
        console.log(`  ${index + 1}.`, {
          datum: row.datum,
          servicegrad: row.servicegrad,
          produzierte_tonnagen: row.produzierte_tonnagen
        });
      });
    }

    // 7. Zusammenfassung
    console.log('\n📋 ZUSAMMENFASSUNG:');
    console.log(`- Gesamtanzahl Datensätze: ${totalCount[0].count}`);
    console.log(`- Datensätze mit servicegrad: ${serviceGradData.length}`);
    console.log(`- Datensätze im Test-Zeitraum: ${testRangeData.length}`);
    console.log(`- Datensätze in letzten 30 Tagen: ${recentData.length}`);

    if (totalCount[0].count === 0) {
      console.log('\n❌ PROBLEM: Die Datenbank ist leer!');
      console.log('💡 LÖSUNG: Daten müssen in die dispatch_data Tabelle importiert werden.');
    } else if (serviceGradData.length === 0) {
      console.log('\n❌ PROBLEM: Keine Datensätze haben servicegrad-Werte!');
      console.log('💡 LÖSUNG: servicegrad-Spalte muss mit Daten gefüllt werden.');
    } else if (testRangeData.length === 0) {
      console.log('\n❌ PROBLEM: Keine Daten im gewünschten Zeitraum!');
      console.log('💡 LÖSUNG: Daten für den Zeitraum 2025-07-03 bis 2025-07-30 müssen importiert werden.');
    } else {
      console.log('\n✅ Daten sind vorhanden, aber möglicherweise nicht im erwarteten Zeitraum.');
    }

  } catch (error) {
    console.error('❌ Fehler beim Überprüfen der Datenbank:', error);
  }
}

// Script ausführen
checkDatabaseContent();