/**
 * System Repository Implementation
 *
 * Concrete implementation for system data management using backend API routes
 */

import { BaseRepository } from './base.repository';
import { apiService } from '../services/api.service';
import {
  ServiceLevelDataPoint,
  DailyPerformanceDataPoint,
  PickingDataPoint,
  ReturnsDataPoint,
  DeliveryPositionsDataPoint,
  TagesleistungDataPoint
} from '../types/database';

/**
 * Filter-Interface für Datum-basierte Repositories
 */
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

/**
 * Filter für System-Daten
 */
export interface SystemFilter extends DateRangeFilter {
  systemType?: 'performance' | 'service-level' | 'picking' | 'returns' | 'delivery';
  minServiceLevel?: number;
  maxServiceLevel?: number;
}

export interface ServiceLevelStats {
  average: number;
  current: number;
  trend: 'improving' | 'declining' | 'stable';
  aboveTarget: number;
  belowTarget: number;
  targetPercentage: number;
}

export interface PerformanceTrend {
  trend: 'improving' | 'declining' | 'stable';
  averageDaily: number;
  bestDay: DailyPerformanceDataPoint | null;
  worstDay: DailyPerformanceDataPoint | null;
  consistency: number;
}

export interface PickingEfficiency {
  averagePicksPerDay: number;
  totalPicks: number;
  efficiency: number;
  trends: { date: string; picks: number }[];
}

export interface ReturnsAnalysis {
  totalReturns: number;
  averageReturnsPerDay: number;
  returnRate: number;
  trends: { date: string; returns: number }[];
  categories: Record<string, number>;
}

export interface DeliveryAnalysis {
  totalPositions: number;
  averagePositionsPerDay: number;
  deliveryEfficiency: number;
  onTimeDeliveries: number;
  delays: number;
}

export interface DailyPerformanceStats {
  averageDaily: number;
  bestPerformance: TagesleistungDataPoint | null;
  worstPerformance: TagesleistungDataPoint | null;
  targetAchievement: number;
  consistency: number;
}

export interface SystemHealthDashboard {
  overall: 'healthy' | 'warning' | 'critical';
  components: Record<string, { status: string; value: number }>;
  recommendations: string[];
}

export interface SystemDashboard {
  serviceLevel: ServiceLevelStats;
  performance: PerformanceTrend;
  picking: PickingEfficiency;
  delivery: DeliveryAnalysis;
  systemHealth: SystemHealthDashboard;
}

/**
 * Service Level Repository Interface
 */
export interface IServiceLevelRepository {
  getAll(filter?: SystemFilter): Promise<ServiceLevelDataPoint[]>;
  getCurrentServiceLevel(): Promise<ServiceLevelDataPoint | null>;
  getServiceLevelStats(filter?: SystemFilter): Promise<ServiceLevelStats>;
  invalidateCache(): void;
}

/**
* Daily Performance Repository Interface
*/
export interface IDailyPerformanceRepository {
  getAll(filter?: DateRangeFilter): Promise<DailyPerformanceDataPoint[]>;
  getPerformanceTrend(days?: number): Promise<PerformanceTrend>;
  invalidateCache(): void;
}

/**
* Picking Repository Interface
*/
export interface IPickingRepository {
  getAll(): Promise<PickingDataPoint[]>;
  getPickingEfficiency(): Promise<PickingEfficiency>;
  invalidateCache(): void;
}

/**
* Returns Repository Interface
*/
export interface IReturnsRepository {
  getAll(): Promise<ReturnsDataPoint[]>;
  getReturnsAnalysis(): Promise<ReturnsAnalysis>;
  invalidateCache(): void;
}

/**
* Delivery Positions Repository Interface
*/
export interface IDeliveryPositionsRepository {
  getAll(): Promise<DeliveryPositionsDataPoint[]>;
  getDeliveryAnalysis(): Promise<DeliveryAnalysis>;
  invalidateCache(): void;
}

/**
* Tagesleistung Repository Interface
*/
export interface ITagesleistungRepository {
  getAll(): Promise<TagesleistungDataPoint[]>;
  getDailyPerformanceStats(): Promise<DailyPerformanceStats>;
  invalidateCache(): void;
}

/**
* System Stats Repository Interface
*/
export interface ISystemStatsRepository {
  getAll(filter?: DateRangeFilter): Promise<any>;
  getSystemHealthDashboard(): Promise<SystemHealthDashboard>;
  invalidateCache(): void;
}

/**
* System Repository Interface
*/
export interface ISystemRepository {
  serviceLevel: IServiceLevelRepository;
  dailyPerformance: IDailyPerformanceRepository;
  picking: IPickingRepository;
  returns: IReturnsRepository;
  deliveryPositions: IDeliveryPositionsRepository;
  tagesleistung: ITagesleistungRepository;
  systemStats: ISystemStatsRepository;

  invalidateAllCache(): void;
  getSystemDashboard(filter?: DateRangeFilter): Promise<SystemDashboard>;
}

/**
 * Service Level Repository Implementation
 */
class ServiceLevelRepositoryImpl extends BaseRepository<ServiceLevelDataPoint, SystemFilter> implements IServiceLevelRepository {
  protected repositoryName = 'SystemServiceLevelRepository';

  async getAll(filter?: SystemFilter): Promise<ServiceLevelDataPoint[]> {
    try {
      // Use the existing API service method
      const data = await apiService.getServiceLevelData(filter?.startDate, filter?.endDate);

      // Apply additional filters if provided
      let filteredData = data;

      if (filter?.minServiceLevel !== undefined) {
        filteredData = filteredData.filter(item => item.servicegrad >= filter.minServiceLevel!);
      }

      if (filter?.maxServiceLevel !== undefined) {
        filteredData = filteredData.filter(item => item.servicegrad <= filter.maxServiceLevel!);
      }

      // Map servicegrad to csr for compatibility
      return filteredData.map(item => ({
        ...item,
        csr: item.servicegrad
      }));
    } catch (error) {
      console.error('Error fetching service level data:', error);
      throw error;
    }
  }

  async getCurrentServiceLevel(): Promise<ServiceLevelDataPoint | null> {
    try {
      const data = await this.getAll();
      return data.length > 0 ? data[0] : null;
    } catch (error) {
      console.error('Error fetching current service level:', error);
      throw error;
    }
  }

  async getServiceLevelStats(filter?: SystemFilter): Promise<ServiceLevelStats> {
    try {
      const data = await this.getAll(filter);
      const serviceLevels = data.map(item => item.servicegrad);

      if (serviceLevels.length === 0) {
        return {
          average: 0,
          current: 0,
          trend: 'stable',
          aboveTarget: 0,
          belowTarget: 0,
          targetPercentage: 0
        };
      }

      const average = serviceLevels.reduce((sum, level) => sum + level, 0) / serviceLevels.length;
      const current = serviceLevels[0];
      const target = 95; // 95% Service Level Target

      return {
        average,
        current,
        trend: current > average ? 'improving' : current < average ? 'declining' : 'stable',
        aboveTarget: serviceLevels.filter(level => level >= target).length,
        belowTarget: serviceLevels.filter(level => level < target).length,
        targetPercentage: (serviceLevels.filter(level => level >= target).length / serviceLevels.length) * 100
      };
    } catch (error) {
      console.error('Error calculating service level stats:', error);
      throw error;
    }
  }
}

/**
 * Daily Performance Repository Implementation
 */
class DailyPerformanceRepositoryImpl extends BaseRepository<DailyPerformanceDataPoint, DateRangeFilter> implements IDailyPerformanceRepository {
  protected repositoryName = 'SystemDailyPerformanceRepository';

  async getAll(filter?: DateRangeFilter): Promise<DailyPerformanceDataPoint[]> {
    try {
      // Use the existing API service method
      return await apiService.getDailyPerformanceData(filter?.startDate, filter?.endDate);
    } catch (error) {
      console.error('Error fetching daily performance data:', error);
      throw error;
    }
  }

  async getPerformanceTrend(days: number = 30): Promise<PerformanceTrend> {
    try {
      const data = await this.getAll({ startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0] });
      const performances = data.map(item => item.value || 0);

      if (performances.length === 0) {
        return {
          trend: 'stable',
          averageDaily: 0,
          bestDay: null,
          worstDay: null,
          consistency: 0
        };
      }

      const averageDaily = performances.reduce((sum, perf) => sum + perf, 0) / performances.length;
      const bestDay = data.find(item => item.value === Math.max(...performances)) || null;
      const worstDay = data.find(item => item.value === Math.min(...performances)) || null;

      // Calculate consistency (lower variance = higher consistency)
      const variance = performances.reduce((sum, perf) => sum + Math.pow(perf - averageDaily, 2), 0) / performances.length;
      const consistency = Math.max(0, 100 - (variance / averageDaily) * 100);

      // Determine trend
      const recent = performances.slice(0, Math.floor(performances.length / 2));
      const older = performances.slice(Math.floor(performances.length / 2));
      const recentAvg = recent.reduce((sum, perf) => sum + perf, 0) / recent.length;
      const olderAvg = older.reduce((sum, perf) => sum + perf, 0) / older.length;

      let trend: 'improving' | 'declining' | 'stable' = 'stable';
      if (recentAvg > olderAvg * 1.05) trend = 'improving';
      else if (recentAvg < olderAvg * 0.95) trend = 'declining';

      return {
        trend,
        averageDaily,
        bestDay,
        worstDay,
        consistency
      };
    } catch (error) {
      console.error('Error calculating performance trend:', error);
      throw error;
    }
  }
}

/**
 * Picking Repository Implementation
 */
class PickingRepositoryImpl extends BaseRepository<PickingDataPoint, DateRangeFilter> implements IPickingRepository {
  protected repositoryName = 'SystemPickingRepository';

  async getAll(): Promise<PickingDataPoint[]> {
    try {
      // Use the existing API service method
      return await apiService.getPickingData();
    } catch (error) {
      console.error('Error fetching picking data:', error);
      throw error;
    }
  }

  async getPickingEfficiency(): Promise<PickingEfficiency> {
    try {
      const data = await this.getAll();
      const picks = data.map(item => item.atrl || 0);

      if (picks.length === 0) {
        return {
          averagePicksPerDay: 0,
          totalPicks: 0,
          efficiency: 0,
          trends: []
        };
      }

      const totalPicks = picks.reduce((sum, pick) => sum + pick, 0);
      const averagePicksPerDay = totalPicks / picks.length;

      // Calculate efficiency based on target (assuming 1000 picks per day target)
      const target = 1000;
      const efficiency = (averagePicksPerDay / target) * 100;

      const trends = data.slice(0, 30).map(item => ({
        date: item.date,
        picks: item.atrl || 0
      }));

      return {
        averagePicksPerDay,
        totalPicks,
        efficiency,
        trends
      };
    } catch (error) {
      console.error('Error calculating picking efficiency:', error);
      throw error;
    }
  }
}

/**
 * Returns Repository Implementation
 */
class ReturnsRepositoryImpl extends BaseRepository<ReturnsDataPoint, DateRangeFilter> implements IReturnsRepository {
  protected repositoryName = 'SystemReturnsRepository';

  async getAll(): Promise<ReturnsDataPoint[]> {
    try {
      // Use the existing API service method
      return await apiService.getReturnsData();
    } catch (error) {
      console.error('Error fetching returns data:', error);
      throw error;
    }
  }

  async getReturnsAnalysis(): Promise<ReturnsAnalysis> {
    try {
      const data = await this.getAll();
      const returnsData = data.map(item => item.value || 0);

      if (returnsData.length === 0) {
        return {
          totalReturns: 0,
          averageReturnsPerDay: 0,
          returnRate: 0,
          trends: [],
          categories: {}
        };
      }

      const totalReturns = returnsData.reduce((sum, returns) => sum + returns, 0);
      const averageReturnsPerDay = totalReturns / returnsData.length;

      // Calculate return rate (assuming 5% of total orders)
      const returnRate = (totalReturns / (totalReturns * 20)) * 100; // Assuming 20x more orders than returns

      const trends = data.slice(0, 30).map(item => ({
        date: item.date,
        returns: item.value || 0
      }));

      // Group by category (mock implementation)
      const categories: Record<string, number> = {
        'quality': totalReturns * 0.6,
        'damage': totalReturns * 0.3,
        'other': totalReturns * 0.1
      };

      return {
        totalReturns,
        averageReturnsPerDay,
        returnRate,
        trends,
        categories
      };
    } catch (error) {
      console.error('Error calculating returns analysis:', error);
      throw error;
    }
  }
}

/**
 * Delivery Positions Repository Implementation
 */
class DeliveryPositionsRepositoryImpl extends BaseRepository<DeliveryPositionsDataPoint, DateRangeFilter> implements IDeliveryPositionsRepository {
  protected repositoryName = 'SystemDeliveryPositionsRepository';

  async getAll(): Promise<DeliveryPositionsDataPoint[]> {
    try {
      // Use the existing API service method
      return await apiService.getDeliveryPositionsData();
    } catch (error) {
      console.error('Error fetching delivery positions data:', error);
      throw error;
    }
  }

  async getDeliveryAnalysis(): Promise<DeliveryAnalysis> {
    try {
      const data = await this.getAll();
      const positions = data.map(item => item.ausgeliefert_lup || 0);

      if (positions.length === 0) {
        return {
          totalPositions: 0,
          averagePositionsPerDay: 0,
          deliveryEfficiency: 0,
          onTimeDeliveries: 0,
          delays: 0
        };
      }

      const totalPositions = positions.reduce((sum, pos) => sum + pos, 0);
      const averagePositionsPerDay = totalPositions / positions.length;

      // Calculate efficiency based on target (assuming 100 positions per day target)
      const target = 100;
      const deliveryEfficiency = (averagePositionsPerDay / target) * 100;

      const onTimeDeliveries = data.filter(item => item.ausgeliefert_lup > 0).length;
      const delays = data.filter(item => item.rueckstaendig > 0).length;

      return {
        totalPositions,
        averagePositionsPerDay,
        deliveryEfficiency,
        onTimeDeliveries,
        delays
      };
    } catch (error) {
      console.error('Error calculating delivery analysis:', error);
      throw error;
    }
  }
}

/**
 * Tagesleistung Repository Implementation
 */
class TagesleistungRepositoryImpl extends BaseRepository<TagesleistungDataPoint, DateRangeFilter> implements ITagesleistungRepository {
  protected repositoryName = 'SystemTagesleistungRepository';

  async getAll(): Promise<TagesleistungDataPoint[]> {
    try {
      // Use the existing API service method
      return await apiService.getTagesleistungData();
    } catch (error) {
      console.error('Error fetching tagesleistung data:', error);
      throw error;
    }
  }

  async getDailyPerformanceStats(): Promise<DailyPerformanceStats> {
    try {
      const data = await this.getAll();
      const performances = data.map(item => item.produzierte_tonnagen || 0);

      if (performances.length === 0) {
        return {
          averageDaily: 0,
          bestPerformance: null,
          worstPerformance: null,
          targetAchievement: 0,
          consistency: 0
        };
      }

      const averageDaily = performances.reduce((sum, perf) => sum + perf, 0) / performances.length;
      const bestPerformance = data.find(item => item.produzierte_tonnagen === Math.max(...performances)) || null;
      const worstPerformance = data.find(item => item.produzierte_tonnagen === Math.min(...performances)) || null;

      // Calculate target achievement (assuming 100% target)
      const target = 100;
      const targetAchievement = (averageDaily / target) * 100;

      // Calculate consistency
      const variance = performances.reduce((sum, perf) => sum + Math.pow(perf - averageDaily, 2), 0) / performances.length;
      const consistency = Math.max(0, 100 - (variance / averageDaily) * 100);

      return {
        averageDaily,
        bestPerformance,
        worstPerformance,
        targetAchievement,
        consistency
      };
    } catch (error) {
      console.error('Error calculating daily performance stats:', error);
      throw error;
    }
  }
}

/**
 * System Stats Repository Implementation
 */
class SystemStatsRepositoryImpl extends BaseRepository<any, DateRangeFilter> implements ISystemStatsRepository {
  protected repositoryName = 'SystemSystemStatsRepository';

  async getAll(filter?: DateRangeFilter): Promise<any> {
    try {
      // Use the existing API service method
      return await apiService.getSystemStats(filter?.startDate, filter?.endDate);
    } catch (error) {
      console.error('Error fetching system stats:', error);
      throw error;
    }
  }

  async getSystemHealthDashboard(): Promise<SystemHealthDashboard> {
    try {
      // For now, return a mock implementation
      // In a real implementation, this would call a specific API endpoint
      const components: Record<string, { status: string; value: number }> = {
        'database': { status: 'healthy', value: 98 },
        'api': { status: 'healthy', value: 95 },
        'cache': { status: 'warning', value: 87 },
        'storage': { status: 'healthy', value: 92 }
      };

      const overall = Object.values(components).every(c => c.status === 'healthy') ? 'healthy' :
                     Object.values(components).some(c => c.status === 'critical') ? 'critical' : 'warning';

      return {
        overall,
        components,
        recommendations: overall === 'healthy' ? [] : ['Check cache performance', 'Monitor system resources']
      };
    } catch (error) {
      console.error('Error fetching system health dashboard:', error);
      throw error;
    }
  }
}

/**
 * System Repository Implementation
 */
export class SystemRepository implements ISystemRepository {
  public readonly serviceLevel: IServiceLevelRepository;
  public readonly dailyPerformance: IDailyPerformanceRepository;
  public readonly picking: IPickingRepository;
  public readonly returns: IReturnsRepository;
  public readonly deliveryPositions: IDeliveryPositionsRepository;
  public readonly tagesleistung: ITagesleistungRepository;
  public readonly systemStats: ISystemStatsRepository;

  constructor() {
    this.serviceLevel = new ServiceLevelRepositoryImpl();
    this.dailyPerformance = new DailyPerformanceRepositoryImpl();
    this.picking = new PickingRepositoryImpl();
    this.returns = new ReturnsRepositoryImpl();
    this.deliveryPositions = new DeliveryPositionsRepositoryImpl();
    this.tagesleistung = new TagesleistungRepositoryImpl();
    this.systemStats = new SystemStatsRepositoryImpl();
  }

  invalidateAllCache(): void {
    this.serviceLevel.invalidateCache();
    this.dailyPerformance.invalidateCache();
    this.picking.invalidateCache();
    this.returns.invalidateCache();
    this.deliveryPositions.invalidateCache();
    this.tagesleistung.invalidateCache();
    this.systemStats.invalidateCache();
  }

  async getSystemDashboard(filter?: DateRangeFilter): Promise<SystemDashboard> {
    try {
      const [
        serviceLevelStats,
        performanceTrend,
        pickingEfficiency,
        returnsAnalysis,
        deliveryAnalysis,
        systemHealth
      ] = await Promise.all([
        this.serviceLevel.getServiceLevelStats(filter ? { ...filter, systemType: 'performance' } : { systemType: 'performance' }),
        this.dailyPerformance.getPerformanceTrend(filter ? undefined : 30),
        this.picking.getPickingEfficiency(),
        this.returns.getReturnsAnalysis(),
        this.deliveryPositions.getDeliveryAnalysis(),
        this.systemStats.getSystemHealthDashboard()
      ]);

      return {
        serviceLevel: serviceLevelStats,
        performance: performanceTrend,
        picking: pickingEfficiency,
        delivery: deliveryAnalysis,
        systemHealth
      };
    } catch (error) {
      console.error('Error fetching system dashboard:', error);
      throw error;
    }
  }
}