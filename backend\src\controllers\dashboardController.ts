import { Request, Response } from 'express';
import { dashboardService } from '../services/dashboardService';

export class DashboardController {
  /**
   * GET /api/dashboard/service-level
   * Holt ServiceGrad-Daten für Dashboard-Charts
   */
  async getServiceLevelData(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const data = await dashboardService.getServiceLevelData(
        startDate as string,
        endDate as string
      );

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('DashboardController: <PERSON><PERSON> beim <PERSON>den der ServiceGrad-Daten:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/dashboard/delivery-positions
   * Holt Lieferpositionen-Daten für Dashboard-Charts
   */
  async getDeliveryPositionsData(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const data = await dashboardService.getDeliveryPositionsData(
        startDate as string,
        endDate as string
      );

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('DashboardController: Fehler beim Laden der Lieferpositionen-Daten:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/dashboard/tagesleistung
   * Holt Tagesleistung-Daten für Dashboard-Charts
   */
  async getTagesleistungData(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const data = await dashboardService.getTagesleistungData(
        startDate as string,
        endDate as string
      );

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('DashboardController: Fehler beim Laden der Tagesleistung-Daten:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/dashboard/picking
   * Holt Picking-Daten für Dashboard-Charts
   */
  async getPickingData(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const data = await dashboardService.getPickingData(
        startDate as string,
        endDate as string
      );

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('DashboardController: Fehler beim Laden der Picking-Daten:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/dashboard/all
   * Holt alle Dashboard-Daten in einer optimierten Abfrage
   */
  async getAllDashboardData(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const data = await dashboardService.getAllDashboardData(
        startDate as string,
        endDate as string
      );

      res.json({
        success: true,
        data,
        counts: {
          serviceLevel: data.serviceLevel.length,
          deliveryPositions: data.deliveryPositions.length,
          tagesleistung: data.tagesleistung.length,
          picking: data.picking.length
        }
      });
    } catch (error) {
      console.error('DashboardController: Fehler beim Laden aller Dashboard-Daten:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/dashboard/statistics
   * Holt aggregierte Statistiken für Dashboard
   */
  async getDashboardStatistics(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const statistics = await dashboardService.getDashboardStatistics(
        startDate as string,
        endDate as string
      );

      res.json({
        success: true,
        data: statistics
      });
    } catch (error) {
      console.error('DashboardController: Fehler beim Berechnen der Statistiken:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/dashboard/health
   * Prüft die Verfügbarkeit des Dashboard-Services
   */
  async getHealth(req: Request, res: Response): Promise<void> {
    try {
      // Einfache Gesundheitsprüfung - versuche Daten zu laden
      const data = await dashboardService.getServiceLevelData();

      res.json({
        success: true,
        status: 'healthy',
        message: 'Dashboard-Service ist verfügbar',
        dataAvailable: data.length > 0
      });
    } catch (error) {
      console.error('DashboardController: Gesundheitsprüfung fehlgeschlagen:', error);
      res.status(503).json({
        success: false,
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}

export const dashboardController = new DashboardController();