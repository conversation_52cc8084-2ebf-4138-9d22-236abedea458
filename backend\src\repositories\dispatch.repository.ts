/**
 * Dispatch Repository Implementation
 * 
 * Implementiert die DispatchRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Versanddaten.
 */

import { db } from '../db';
import { dispatchData } from '../db/schema';
import { eq, and, gte, lte, desc, count as drizzleCount, sum, avg, isNotNull } from 'drizzle-orm';
import {
  DispatchRepository,
  DispatchDataEntity,
  DateRange,
  PerformanceMetrics,
  FindAllOptions,
  RepositoryStats,
  RepositoryCacheConfig
} from './interfaces';
import {
  ServiceLevelDataPoint,
  DailyPerformanceDataPoint,
  PickingDataPoint,
  DeliveryPositionsDataPoint,
  TagesleistungDataPoint
} from '../types/database.types';
import { getBackendCache, BackendCacheKeyGenerator } from '../services/cache.service';

/**
 * Cache-TTL für Dispatch-Daten (verschiedene Datentypen)
 */
const DISPATCH_CACHE_TTL = {
  SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute - häufig aktualisiert
  PERFORMANCE: 2 * 60 * 1000, // 2 Minuten - moderate Aktualisierung
  PICKING: 3 * 60 * 1000, // 3 Minuten - weniger häufig
  DELIVERY: 2 * 60 * 1000, // 2 Minuten
  RETURNS: 5 * 60 * 1000, // 5 Minuten - seltene Änderungen
  METRICS: 10 * 60 * 1000 // 10 Minuten - Aggregierte Daten
};

export class DispatchRepositoryImpl implements DispatchRepository {
  private db = db;
  private cache = getBackendCache();
  private stats: RepositoryStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    avgQueryTime: 0,
    lastAccessed: new Date()
  };

  constructor(database?: any) {
    // Drizzle DB wird direkt importiert - Parameter für Kompatibilität
  }

  /**
   * Service Level Daten mit Cache-Optimierung
   */
  async getServiceLevelData(dateRange?: DateRange): Promise<ServiceLevelDataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getServiceLevelData', dateRange);
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
        
        const result = await this.db.select({
          datum: dispatchData.datum,
          servicegrad: dispatchData.servicegrad,
        })
        .from(dispatchData)
        .where(and(
          ...(drizzleConditions.length > 0 ? drizzleConditions : [])
        ));

        return result.map((item: any) => ({
          datum: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
          servicegrad: item.servicegrad || 0,
        }));
      },
      DISPATCH_CACHE_TTL.SERVICE_LEVEL
    );
  }

  /**
   * Tägliche Performance-Daten
   */
  async getDailyPerformanceData(dateRange?: DateRange): Promise<DailyPerformanceDataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getDailyPerformanceData', dateRange);
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
        
        const result = await this.db.select({
          datum: dispatchData.datum,
          produzierte_tonnagen: dispatchData.produzierte_tonnagen,
        })
        .from(dispatchData)
        .where(and(
          ...(drizzleConditions.length > 0 ? drizzleConditions : [])
        ));

        return result.map((item: any) => ({
          datum: item.datum!,
          value: item.produzierte_tonnagen || 0,
        }));
      },
      DISPATCH_CACHE_TTL.PERFORMANCE
    );
  }

  /**
   * Kommissionierungsdaten
   */
  async getPickingData(dateRange?: DateRange): Promise<PickingDataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getPickingData', dateRange);
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
        
        const result = await this.db.select({
          datum: dispatchData.datum,
          atrl: dispatchData.atrl,
          aril: dispatchData.aril,
          fuellgrad_aril: dispatchData.fuellgrad_aril,
        })
        .from(dispatchData)
        .where(and(
          ...(drizzleConditions.length > 0 ? drizzleConditions : [])
        ));

        return result.map((item: any) => ({
          date: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
          atrl: item.atrl || 0,
          aril: item.aril || 0,
          fuellgrad_aril: item.fuellgrad_aril || 0,
        }));
      },
      DISPATCH_CACHE_TTL.PICKING
    );
  }

  /**
   * Lieferpositionsdaten
   */
  async getDeliveryPositionsData(dateRange?: DateRange): Promise<DeliveryPositionsDataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getDeliveryPositionsData', dateRange);
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
        
        const result = await this.db.select({
          datum: dispatchData.datum,
          ausgeliefert_lup: dispatchData.ausgeliefert_lup,
          rueckstaendig: dispatchData.rueckstaendig,
        })
        .from(dispatchData)
        .where(and(
          ...(drizzleConditions.length > 0 ? drizzleConditions : [])
        ));

        return result.map((item: any) => ({
          date: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
          ausgeliefert_lup: item.ausgeliefert_lup || 0,
          rueckstaendig: item.rueckstaendig || 0,
        }));
      },
      DISPATCH_CACHE_TTL.DELIVERY
    );
  }

  /**
   * Tagesleistungsdaten
   */
  async getTagesleistungData(dateRange?: DateRange): Promise<TagesleistungDataPoint[]> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getTagesleistungData', dateRange);
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
        
        const result = await this.db.select({
          datum: dispatchData.datum,
          produzierte_tonnagen: dispatchData.produzierte_tonnagen,
        direktverladung_kiaa: dispatchData.direktverladung_kiaa,
        umschlag: dispatchData.umschlag,
        kg_pro_colli: dispatchData.kg_pro_colli,
          elefanten: dispatchData.elefanten,
        })
        .from(dispatchData)
        .where(and(
          ...(drizzleConditions.length > 0 ? drizzleConditions : [])
        ));

        return result.map((item: any) => ({
          date: item.datum || new Date().toISOString().split('T')[0], // Fallback: heute's Datum
          produzierte_tonnagen: item.produzierte_tonnagen || 0,
          direktverladung_kiaa: item.direktverladung_kiaa || 0,
          umschlag: item.umschlag || 0,
          kg_pro_colli: item.kg_pro_colli || 0,
          elefanten: item.elefanten || 0,
        }));
      },
      DISPATCH_CACHE_TTL.PERFORMANCE
    );
  }

  /**
   * Retourendaten aggregiert
   */
  async getReturnsData(): Promise<Array<{ name: string; value: number }>> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getReturnsData');
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const result = await this.db.select({
          qm_angenommen: dispatchData.qm_angenommen,
        qm_abgelehnt: dispatchData.qm_abgelehnt,
        qm_offen: dispatchData.qm_offen,
        })
        .from(dispatchData);

        const angenommen = result.reduce((sum: number, item: any) => sum + (item.qm_angenommen || 0), 0);
        const abgelehnt = result.reduce((sum: number, item: any) => sum + (item.qm_abgelehnt || 0), 0);
        const offen = result.reduce((sum: number, item: any) => sum + (item.qm_offen || 0), 0);

        return [
          { name: 'Angenommen', value: angenommen },
          { name: 'Abgelehnt', value: abgelehnt },
          { name: 'Offen', value: offen },
        ];
      },
      DISPATCH_CACHE_TTL.RETURNS
    );
  }

  /**
   * Performance-Metriken für einen bestimmten Zeitraum
   */
  async getPerformanceMetrics(dateRange: DateRange): Promise<PerformanceMetrics> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getPerformanceMetrics', dateRange);
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const drizzleConditions = this.buildDrizzleWhereClause(dateRange);
        const whereClause = drizzleConditions.length > 0 ? and(...drizzleConditions) : undefined;
        
        const [sumResult, avgResult, countResult] = await Promise.all([
          this.db.select({
            produzierte_tonnagen: sum(dispatchData.produzierte_tonnagen),
            ausgeliefert_lup: sum(dispatchData.ausgeliefert_lup),
            atrl: sum(dispatchData.atrl),
          })
          .from(dispatchData)
          .where(whereClause),
          
          this.db.select({
            servicegrad: avg(dispatchData.servicegrad),
            atrl: avg(dispatchData.atrl),
          })
          .from(dispatchData)
          .where(whereClause),
          
          this.db.select({ count: drizzleCount(dispatchData.datum) })
          .from(dispatchData)
          .where(whereClause)
        ]);
        
        const result = {
          _sum: {
            produzierte_tonnagen: sumResult[0]?.produzierte_tonnagen || 0,
            ausgeliefert_lup: sumResult[0]?.ausgeliefert_lup || 0,
            atrl: sumResult[0]?.atrl || 0,
          },
          _avg: {
            servicegrad: avgResult[0]?.servicegrad || 0,
            atrl: avgResult[0]?.atrl || 0,
          },
          _count: {
            datum: countResult[0]?.count || 0,
          },
        };

        // Berechne Periode
        const startDate = dateRange.startDate || '';
        const endDate = dateRange.endDate || '';
        const daysDiff = startDate && endDate 
          ? Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 3600 * 24))
          : result._count.datum || 0;

        return {
          totalTonnage: Number(result._sum.produzierte_tonnagen || 0),
          averageServiceLevel: Number(result._avg.servicegrad || 0) * 100,
          totalDeliveries: Number(result._sum.ausgeliefert_lup || 0),
          averagePickingRate: Number(result._avg.atrl || 0),
          period: {
            startDate: startDate,
            endDate: endDate,
            days: daysDiff,
          },
        };
      },
      DISPATCH_CACHE_TTL.METRICS
    );
  }

  /**
   * Top-Performance-Tage ermitteln
   */
  async getTopPerformanceDays(limit: number = 10): Promise<Array<{
    datum: string; // Datum als string für Konsistenz
    produzierte_tonnagen: number;
    servicegrad: number;
  }>> {
    const cacheKey = BackendCacheKeyGenerator.forQuery('dispatch', 'getTopPerformanceDays', { limit });
    
    return await this.cachedQuery(
      cacheKey,
      async () => {
        const result = await this.db.select({
          datum: dispatchData.datum,
          produzierte_tonnagen: dispatchData.produzierte_tonnagen,
          servicegrad: dispatchData.servicegrad,
        })
        .from(dispatchData)
        .orderBy(desc(dispatchData.produzierte_tonnagen), desc(dispatchData.servicegrad))
        .limit(limit);

        return result.map((item: any) => ({
          // Konvertiere Date zu string für Konsistenz mit Interface
          datum: item.datum instanceof Date ? item.datum.toISOString().split('T')[0] : (item.datum || new Date().toISOString().split('T')[0]),
          produzierte_tonnagen: item.produzierte_tonnagen || 0,
          servicegrad: (item.servicegrad || 0) * 100,
        }));
      },
      DISPATCH_CACHE_TTL.METRICS
    );
  }

  // Base Repository Implementation (simplified for key methods)

  async findById(id: string | number): Promise<DispatchDataEntity | null> {
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    const result = await this.db.select().from(dispatchData).where(eq(dispatchData.id, numericId)).limit(1);
    return result[0] ? this.mapToDispatchDataEntity(result[0]) : null;
  }

  async findAll(options?: FindAllOptions): Promise<DispatchDataEntity[]> {
    const result = await this.db.select().from(dispatchData)
      .limit(options?.limit || 1000)
      .offset(options?.offset || 0);
    return result.map(row => this.mapToDispatchDataEntity(row));
  }

  async findWhere(criteria: Partial<DispatchDataEntity>): Promise<DispatchDataEntity[]> {
    const result = await this.db.select().from(dispatchData);
    return result.map(row => this.mapToDispatchDataEntity(row));
  }

  async findOneWhere(criteria: Partial<DispatchDataEntity>): Promise<DispatchDataEntity | null> {
    const result = await this.db.select().from(dispatchData).limit(1);
    return result[0] ? this.mapToDispatchDataEntity(result[0]) : null;
  }

  async create(data: Partial<DispatchDataEntity>): Promise<DispatchDataEntity> {
    const result = await this.db.insert(dispatchData).values(data).returning();
    await this.invalidateCache();
    return result[0];
  }

  async update(id: string | number, data: Partial<DispatchDataEntity>): Promise<DispatchDataEntity | null> {
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    const result = await this.db.update(dispatchData)
      .set(data)
      .where(eq(dispatchData.id, numericId))
      .returning();
    await this.invalidateCache();
    return result[0] || null;
  }

  async delete(id: string | number): Promise<boolean> {
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    try {
      await this.db.delete(dispatchData).where(eq(dispatchData.id, numericId));
      await this.invalidateCache();
      return true;
    } catch {
      return false;
    }
  }

  async count(criteria?: Partial<DispatchDataEntity>): Promise<number> {
    const result = await this.db.select({ count: drizzleCount() }).from(dispatchData);
    return result[0]?.count || 0;
  }

  async invalidateCache(key?: string): Promise<void> {
    if (key) {
      // Invalidate specific key
      // Implementation depends on cache service
    } else {
      // Invalidate all dispatch-related cache entries
      this.cache.invalidateByDataTypes(['dispatch']);
    }
  }

  async getStats(): Promise<RepositoryStats> {
    return this.stats;
  }

  /**
   * Private helper methods
   */

  private async cachedQuery<T>(
    cacheKey: string,
    queryFn: () => Promise<T>,
    ttl: number
  ): Promise<T> {
    this.stats.totalQueries++;
    this.stats.lastAccessed = new Date();
    
    return await this.cache.cachedQuery(cacheKey, queryFn, ttl);
  }

  private buildDrizzleWhereClause(dateRange?: DateRange): any[] {
    if (!dateRange) return [];

    const conditions: any[] = [];

    // Datumswerte sind als Text in der DB gespeichert (YYYY-MM-DD) - String-Vergleich
    if (dateRange.startDate) {
      // Konvertiere zu String für Datenbank-Vergleich
      const startDateString = this.dateToString(dateRange.startDate);
      conditions.push(gte(dispatchData.datum, startDateString));
    }
    if (dateRange.endDate) {
      // Konvertiere zu String für Datenbank-Vergleich
      const endDateString = this.dateToString(dateRange.endDate);
      conditions.push(lte(dispatchData.datum, endDateString));
    }

    return conditions;
  }

  private dateToString(date: string | Date | undefined): string {
    if (!date) return '';
    if (typeof date === 'string') return date;
    if (date instanceof Date) return date.toISOString().split('T')[0];
    return String(date);
  }


  /**
   * Maps Drizzle result to DispatchDataEntity
   */
  private mapToDispatchDataEntity(row: any): DispatchDataEntity {
    return {
      id: row.id,
      datum: row.datum,
      servicegrad: row.servicegrad,
      produzierte_tonnagen: row.produzierte_tonnagen,
      atrl: row.atrl,
      aril: row.aril,
      fuellgrad_aril: row.fuellgrad_aril,
      qm_angenommen: row.qm_angenommen,
      qm_abgelehnt: row.qm_abgelehnt,
      qm_offen: row.qm_offen,
      ausgeliefert_lup: row.ausgeliefert_lup,
      rueckstaendig: row.rueckstaendig,
      direktverladung_kiaa: row.direktverladung_kiaa,
      umschlag: row.umschlag,
      kg_pro_colli: row.kg_pro_colli,
      elefanten: row.elefanten,
    };
  }
}