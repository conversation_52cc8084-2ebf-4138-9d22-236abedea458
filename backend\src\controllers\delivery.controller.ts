import { Request, Response } from "express";
import { DeliveryRepositoryImpl } from "../repositories/delivery.repository";

export class DeliveryController {
  private repository: DeliveryRepositoryImpl;

  constructor() {
    this.repository = new DeliveryRepositoryImpl();
  }

  /**
   * Get all delivery history
   * GET /api/delivery/history?days=30
   */
  async getDeliveryHistory(req: Request, res: Response) {
    try {
      const { days } = req.query;
      const filter = days ? { days: parseInt(days as string) } : undefined;

      const history = await this.repository.getAll(filter);

      res.json({
        success: true,
        data: history,
        count: history.length,
        filters: filter
      });
    } catch (error) {
      console.error('Error fetching delivery history:', error);
      res.status(500).json({
        success: false,
        error: '<PERSON><PERSON> beim Abrufen der Lieferhistorie',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get delivery statistics
   * GET /api/delivery/stats
   */
  async getDeliveryStats(req: Request, res: Response) {
    try {
      const stats = await this.repository.getDeliveryStats();

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching delivery stats:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Lieferstatistiken',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get supplier delivery history
   * GET /api/delivery/supplier/:supplierId?days=30
   */
  async getSupplierDeliveryHistory(req: Request, res: Response) {
    try {
      const { supplierId } = req.params;
      const { days } = req.query;
      const timeRange = { days: days ? parseInt(days as string) : 30 };

      const history = await this.repository.getSupplierDeliveryHistory(supplierId, timeRange);

      res.json({
        success: true,
        data: history,
        count: history.length,
        supplierId,
        timeRange
      });
    } catch (error) {
      console.error(`Error fetching delivery history for supplier ${req.params.supplierId}:`, error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Lieferanten-Lieferhistorie',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get active delivery routes
   * GET /api/delivery/routes/active
   */
  async getActiveRoutes(req: Request, res: Response) {
    try {
      const routes = await this.repository.getActiveRoutes();

      res.json({
        success: true,
        data: routes,
        count: routes.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching active routes:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der aktiven Routen',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get delivery performance metrics
   * GET /api/delivery/performance?days=30
   */
  async getDeliveryPerformanceMetrics(req: Request, res: Response) {
    try {
      const { days } = req.query;
      const timeRange = { days: days ? parseInt(days as string) : 30 };

      const metrics = await this.repository.getDeliveryPerformanceMetrics(timeRange);

      res.json({
        success: true,
        data: metrics,
        timeRange,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching delivery performance metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Performance-Metriken',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get delivery trends
   * GET /api/delivery/trends?days=30
   */
  async getDeliveryTrends(req: Request, res: Response) {
    try {
      const { days } = req.query;
      const timeRange = { days: days ? parseInt(days as string) : 30 };

      const trends = await this.repository.getDeliveryTrends(timeRange);

      res.json({
        success: true,
        data: trends,
        timeRange,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching delivery trends:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Liefertrends',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get delay reasons analysis
   * GET /api/delivery/delay-reasons?days=30
   */
  async getDelayReasons(req: Request, res: Response) {
    try {
      const { days } = req.query;
      const timeRange = { days: days ? parseInt(days as string) : 30 };

      const reasons = await this.repository.getDelayReasons(timeRange);

      res.json({
        success: true,
        data: reasons,
        timeRange,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching delay reasons:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Verzögerungsgründe',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Create new delivery record
   * POST /api/delivery
   */
  async createDelivery(req: Request, res: Response) {
    try {
      const deliveryData = req.body;

      // Basic validation
      if (!deliveryData.supplierId || !deliveryData.productType) {
        return res.status(400).json({
          success: false,
          error: 'Validierungsfehler',
          message: 'Lieferanten-ID und Produkt-Typ sind erforderlich',
          required: ['supplierId', 'productType']
        });
      }

      const deliveryId = await this.repository.createDelivery(deliveryData);

      res.status(201).json({
        success: true,
        data: { deliveryId },
        message: 'Lieferung erfolgreich erstellt'
      });
    } catch (error) {
      console.error('Error creating delivery:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Erstellen der Lieferung',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Update delivery status
   * PUT /api/delivery/:deliveryId/status
   */
  async updateDeliveryStatus(req: Request, res: Response) {
    try {
      const { deliveryId } = req.params;
      const { status, actualDeliveryTime } = req.body;

      if (!status) {
        return res.status(400).json({
          success: false,
          error: 'Validierungsfehler',
          message: 'Status ist erforderlich'
        });
      }

      await this.repository.updateDeliveryStatus(deliveryId, status, actualDeliveryTime);

      res.json({
        success: true,
        message: 'Lieferstatus erfolgreich aktualisiert',
        data: { deliveryId, status, actualDeliveryTime }
      });
    } catch (error) {
      console.error(`Error updating delivery status for ${req.params.deliveryId}:`, error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Aktualisieren des Lieferstatus',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get deliveries by route
   * GET /api/delivery/route/:routeId
   */
  async getDeliveriesByRoute(req: Request, res: Response) {
    try {
      const { routeId } = req.params;

      const deliveries = await this.repository.getDeliveriesByRoute(routeId);

      res.json({
        success: true,
        data: deliveries,
        count: deliveries.length,
        routeId
      });
    } catch (error) {
      console.error(`Error fetching deliveries for route ${req.params.routeId}:`, error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Routen-Lieferungen',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get seasonal delivery patterns
   * GET /api/delivery/seasonal-patterns
   */
  async getSeasonalPatterns(req: Request, res: Response) {
    try {
      const patterns = await this.repository.getSeasonalPatterns();

      res.json({
        success: true,
        data: patterns,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching seasonal patterns:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der saisonalen Muster',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}