/**
 * Delivery Repository Implementation
 *
 * Implementiert die IDeliveryRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Lieferungsdaten.
 */

import { db } from '../db';
import { eq, gte, lte, desc, asc, and, sql } from 'drizzle-orm';
import { DeliveryRepository } from './interfaces';
import { RepositoryStats } from './interfaces';
import { getBackendCache, BackendCacheKeyGenerator } from '../services/cache.service';

// Type definitions for delivery operations
export interface DeliveryStats {
  totalDeliveries: number;
  onTimeDeliveries: number;
  averageDeliveryTime: number; // days
  totalDistance: number; // km
  totalCost: number; // euros
  activeRoutes: number;
}

export interface DeliveryTimeRangeFilter {
  days: number;
}

export interface DeliveryHistoryRecord {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number; // days
  actualTime: number; // days
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number; // euros
  distance?: number; // km
  route?: string;
  delayReason?: string;
}

export interface DeliveryPerformanceMetrics {
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  delayFrequency: number;
  averageDelay: number;
}

export interface DeliveryTrendData {
  week: Date;
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  totalDistance: number;
  totalCost: number;
}

export interface DelayReasonAnalysis {
  reason: string;
  frequency: number;
  percentage: number;
  averageDelay: number;
  impact: 'high' | 'medium' | 'low';
}

export interface SeasonalPattern {
  season: string;
  months: number[];
  deliveryTimeFactor: number;
  riskFactor: number;
  description: string;
}

// Cache TTL constants
const DELIVERY_CACHE_TTL = {
  HISTORY: 300, // 5 minutes
  STATS: 600,  // 10 minutes
  ROUTES: 180,  // 3 minutes
  PERFORMANCE: 300, // 5 minutes
  TRENDS: 600   // 10 minutes
};

/**
 * Delivery Repository Implementation
 * Implementiert die DeliveryRepository-Schnittstelle mit Caching
 */
export class DeliveryRepositoryImpl implements DeliveryRepository {
  private db = db;
  private cache = getBackendCache();
  private stats: RepositoryStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    avgQueryTime: 0,
    lastAccessed: new Date()
  };

  constructor() {
    // Drizzle DB wird direkt importiert
  }

  /**
   * Get all delivery history
   */
  async getAll(filter?: DeliveryTimeRangeFilter): Promise<DeliveryHistoryRecord[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.forQuery('delivery', 'getAll', filter);

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - in real app, this would query the database
          // For now, return sample data
          const history: DeliveryHistoryRecord[] = [];
          const days = filter?.days || 30;

          for (let i = 0; i < 50; i++) {
            const deliveryDate = new Date(Date.now() - Math.random() * days * 24 * 60 * 60 * 1000);
            const promisedTime = Math.floor(Math.random() * 10) + 3;
            const actualTime = promisedTime + (Math.random() - 0.7) * 3;
            const wasLate = actualTime > promisedTime;

            history.push({
              deliveryId: `DEL_${Date.now()}_${i}`,
              supplierId: `SUP_${Math.floor(Math.random() * 5) + 1}`,
              orderDate: new Date(deliveryDate.getTime() - actualTime * 24 * 60 * 60 * 1000),
              deliveryDate,
              promisedTime,
              actualTime: Math.max(actualTime, 1),
              wasLate,
              productType: ['Kabel', 'Stecker', 'Komponenten', 'Werkzeug'][Math.floor(Math.random() * 4)],
              urgency: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
              quantity: Math.floor(Math.random() * 1000) + 50,
              value: Math.floor(Math.random() * 10000) + 500,
              distance: Math.floor(Math.random() * 500) + 50,
              route: `Route_${Math.floor(Math.random() * 10) + 1}`,
              delayReason: wasLate ? ['Wetterbedingungen', 'Verkehrsstau', 'Lieferantenprobleme'][Math.floor(Math.random() * 3)] : undefined
            });
          }

          return history.sort((a, b) => b.deliveryDate.getTime() - a.deliveryDate.getTime());
        },
        DELIVERY_CACHE_TTL.HISTORY
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching delivery history:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get overall delivery statistics
   */
  async getDeliveryStats(): Promise<DeliveryStats> {
    const startTime = Date.now();
    const cacheKey = 'delivery:stats';

    this.stats.totalQueries++;

    try {
      const stats = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate realistic stats
          const totalDeliveries = Math.floor(Math.random() * 1000) + 500;
          const onTimeDeliveries = Math.floor(totalDeliveries * (0.7 + Math.random() * 0.25));

          return {
            totalDeliveries,
            onTimeDeliveries,
            averageDeliveryTime: 5.5 + Math.random() * 3,
            totalDistance: Math.floor(Math.random() * 50000) + 25000,
            totalCost: Math.floor(Math.random() * 100000) + 50000,
            activeRoutes: Math.floor(Math.random() * 20) + 10
          };
        },
        DELIVERY_CACHE_TTL.STATS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return stats;

    } catch (error) {
      console.error('Error fetching delivery stats:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get delivery history for a specific supplier
   */
  async getSupplierDeliveryHistory(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<DeliveryHistoryRecord[]> {
    const startTime = Date.now();
    const cacheKey = `delivery:supplier:${supplierId}:${timeRange.days}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate supplier-specific history
          const history: DeliveryHistoryRecord[] = [];
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - timeRange.days);

          const recordCount = Math.floor(Math.random() * 20) + 5;

          for (let i = 0; i < recordCount; i++) {
            const deliveryDate = new Date(startDate.getTime() + Math.random() * timeRange.days * 24 * 60 * 60 * 1000);
            const promisedTime = Math.floor(Math.random() * 10) + 3;
            const actualTime = promisedTime + (Math.random() - 0.75) * 4;
            const wasLate = actualTime > promisedTime;

            history.push({
              deliveryId: `DEL_${supplierId}_${Date.now()}_${i}`,
              supplierId,
              orderDate: new Date(deliveryDate.getTime() - Math.max(actualTime, 1) * 24 * 60 * 60 * 1000),
              deliveryDate,
              promisedTime,
              actualTime: Math.max(actualTime, 1),
              wasLate,
              productType: ['Kabel', 'Stecker', 'Komponenten', 'Werkzeug', 'Zubehör'][Math.floor(Math.random() * 5)],
              urgency: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
              quantity: Math.floor(Math.random() * 1000) + 50,
              value: Math.floor(Math.random() * 10000) + 500,
              distance: Math.floor(Math.random() * 500) + 50,
              route: `Route_${Math.floor(Math.random() * 10) + 1}`,
              delayReason: wasLate ? ['Wetterbedingungen', 'Verkehrsstau', 'Lieferantenprobleme', 'Qualitätsprüfung'][Math.floor(Math.random() * 4)] : undefined
            });
          }

          return history.sort((a, b) => b.deliveryDate.getTime() - a.deliveryDate.getTime());
        },
        DELIVERY_CACHE_TTL.HISTORY
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error(`Error fetching delivery history for supplier ${supplierId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get all delivery history within time range
   */
  async getDeliveryHistory(timeRange: { days: number }): Promise<DeliveryHistoryRecord[]> {
    return this.getAll({ days: timeRange.days });
  }

  /**
   * Get active delivery routes
   */
  async getActiveRoutes(): Promise<any[]> {
    const startTime = Date.now();
    const cacheKey = 'delivery:routes:active';

    this.stats.totalQueries++;

    try {
      const routes = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate sample active routes
          const deliveryRoutes: any[] = [];
          const routeCount = Math.floor(Math.random() * 10) + 5;

          for (let i = 0; i < routeCount; i++) {
            const deliveryCount = Math.floor(Math.random() * 5) + 1;
            const deliveries: any[] = [];

            for (let j = 0; j < deliveryCount; j++) {
              deliveries.push({
                deliveryId: `DEL_${i}_${j}`,
                destination: `Destination ${i}-${j}`,
                items: [{
                  itemId: `ITEM_${i}_${j}`,
                  quantity: Math.floor(Math.random() * 100) + 10,
                  weight: Math.random() * 50 + 5,
                  dimensions: {
                    length: Math.random() * 100 + 20,
                    width: Math.random() * 50 + 10,
                    height: Math.random() * 30 + 5,
                    volume: 0 // Will be calculated
                  }
                }],
                priority: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low'
              });
            }

            // Calculate volumes
            deliveries.forEach((delivery: any) => {
              delivery.items.forEach((item: any) => {
                item.dimensions.volume = item.dimensions.length * item.dimensions.width * item.dimensions.height;
              });
            });

            deliveryRoutes.push({
              routeId: `ROUTE_${i + 1}`,
              deliveries,
              totalDistance: Math.floor(Math.random() * 300) + 50,
              estimatedTime: Math.floor(Math.random() * 480) + 120,
              cost: Math.floor(Math.random() * 500) + 100
            });
          }

          return deliveryRoutes;
        },
        DELIVERY_CACHE_TTL.ROUTES
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return routes;

    } catch (error) {
      console.error('Error fetching active routes:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get delivery performance metrics
   */
  async getDeliveryPerformanceMetrics(timeRange: { days: number }): Promise<DeliveryPerformanceMetrics> {
    const startTime = Date.now();
    const cacheKey = `delivery:performance:${timeRange.days}`;

    this.stats.totalQueries++;

    try {
      const metrics = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const history = await this.getAll({ days: timeRange.days });

          if (history.length === 0) {
            return {
              onTimeRate: 0.8,
              averageDeliveryTime: 7,
              totalDeliveries: 0,
              delayFrequency: 0.2,
              averageDelay: 2
            };
          }

          const onTimeDeliveries = history.filter(d => !d.wasLate).length;
          const onTimeRate = onTimeDeliveries / history.length;

          const totalDeliveryTime = history.reduce((sum, d) => sum + d.actualTime, 0);
          const averageDeliveryTime = totalDeliveryTime / history.length;

          const lateDeliveries = history.filter(d => d.wasLate);
          const delayFrequency = lateDeliveries.length / history.length;

          const totalDelay = lateDeliveries.reduce((sum, d) => sum + (d.actualTime - d.promisedTime), 0);
          const averageDelay = lateDeliveries.length > 0 ? totalDelay / lateDeliveries.length : 0;

          return {
            onTimeRate,
            averageDeliveryTime,
            totalDeliveries: history.length,
            delayFrequency,
            averageDelay
          };
        },
        DELIVERY_CACHE_TTL.PERFORMANCE
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return metrics;

    } catch (error) {
      console.error('Error fetching delivery performance metrics:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get delivery trends
   */
  async getDeliveryTrends(timeRange: { days: number }): Promise<DeliveryTrendData[]> {
    const startTime = Date.now();
    const cacheKey = `delivery:trends:${timeRange.days}`;

    this.stats.totalQueries++;

    try {
      const trends = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const history = await this.getAll({ days: timeRange.days });
          const weeklyData = new Map<string, any>();

          history.forEach(delivery => {
            const weekStart = new Date(delivery.deliveryDate);
            weekStart.setDate(weekStart.getDate() - weekStart.getDay());
            const weekKey = weekStart.toISOString().split('T')[0];

            if (!weeklyData.has(weekKey)) {
              weeklyData.set(weekKey, {
                week: weekStart,
                totalDeliveries: 0,
                onTimeDeliveries: 0,
                totalTime: 0,
                totalDistance: 0,
                totalCost: 0
              });
            }

            const weekData = weeklyData.get(weekKey)!;
            weekData.totalDeliveries++;
            if (!delivery.wasLate) weekData.onTimeDeliveries++;
            weekData.totalTime += delivery.actualTime;
            weekData.totalDistance += delivery.distance || 0;
            weekData.totalCost += delivery.value;
          });

          return Array.from(weeklyData.values()).map(week => ({
            week: week.week,
            onTimeRate: week.totalDeliveries > 0 ? week.onTimeDeliveries / week.totalDeliveries : 0,
            averageDeliveryTime: week.totalDeliveries > 0 ? week.totalTime / week.totalDeliveries : 0,
            totalDeliveries: week.totalDeliveries,
            totalDistance: week.totalDistance,
            totalCost: week.totalCost
          })).sort((a, b) => a.week.getTime() - b.week.getTime());
        },
        DELIVERY_CACHE_TTL.TRENDS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return trends;

    } catch (error) {
      console.error('Error fetching delivery trends:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get common delay reasons
   */
  async getDelayReasons(timeRange: { days: number }): Promise<DelayReasonAnalysis[]> {
    const startTime = Date.now();
    const cacheKey = `delivery:delay-reasons:${timeRange.days}`;

    this.stats.totalQueries++;

    try {
      const reasons = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const history = await this.getAll({ days: timeRange.days });
          const lateDeliveries = history.filter(d => d.wasLate && d.delayReason);

          const reasonCounts = new Map<string, { count: number; totalDelay: number }>();

          lateDeliveries.forEach(delivery => {
            if (delivery.delayReason) {
              const existing = reasonCounts.get(delivery.delayReason) || { count: 0, totalDelay: 0 };
              existing.count++;
              existing.totalDelay += (delivery.actualTime - delivery.promisedTime);
              reasonCounts.set(delivery.delayReason, existing);
            }
          });

          return Array.from(reasonCounts.entries()).map(([reason, data]) => ({
            reason,
            frequency: data.count,
            percentage: lateDeliveries.length > 0 ? (data.count / lateDeliveries.length) * 100 : 0,
            averageDelay: data.count > 0 ? data.totalDelay / data.count : 0,
            impact: (data.count > 5 ? 'high' : data.count > 2 ? 'medium' : 'low') as 'high' | 'medium' | 'low'
          })).sort((a, b) => b.frequency - a.frequency);
        },
        DELIVERY_CACHE_TTL.PERFORMANCE
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return reasons;

    } catch (error) {
      console.error('Error fetching delay reasons:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Create new delivery record
   */
  async createDelivery(delivery: Partial<DeliveryHistoryRecord>): Promise<string> {
    const startTime = Date.now();
    const cacheKey = 'delivery:create';

    this.stats.totalQueries++;

    try {
      const deliveryId = `DEL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Mock implementation - in real app, this would insert into database
      console.log('Creating new delivery record:', { deliveryId, ...delivery });

      // Invalidate cache
      this.cache.invalidateByDataTypes(['delivery']);

      this.updateStats(startTime);
      return deliveryId;

    } catch (error) {
      console.error('Error creating delivery record:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Update delivery status
   */
  async updateDeliveryStatus(deliveryId: string, status: string, actualDeliveryTime?: number): Promise<void> {
    const startTime = Date.now();

    this.stats.totalQueries++;

    try {
      // Mock implementation - in real app, this would update the database
      console.log('Updating delivery status:', { deliveryId, status, actualDeliveryTime });

      // Invalidate cache
      this.cache.invalidateByDataTypes(['delivery']);

      this.updateStats(startTime);

    } catch (error) {
      console.error(`Error updating delivery status for ${deliveryId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get deliveries by route
   */
  async getDeliveriesByRoute(routeId: string): Promise<DeliveryHistoryRecord[]> {
    const startTime = Date.now();
    const cacheKey = `delivery:route:${routeId}`;

    this.stats.totalQueries++;

    try {
      const deliveries = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const allHistory = await this.getAll({ days: 30 });
          return allHistory.filter(d => d.route === routeId);
        },
        DELIVERY_CACHE_TTL.HISTORY
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return deliveries;

    } catch (error) {
      console.error(`Error fetching deliveries for route ${routeId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get seasonal delivery patterns
   */
  async getSeasonalPatterns(): Promise<SeasonalPattern[]> {
    const startTime = Date.now();
    const cacheKey = 'delivery:seasonal-patterns';

    this.stats.totalQueries++;

    try {
      const patterns = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate seasonal patterns
          return [
            { season: 'Spring', months: [2, 3, 4], deliveryTimeFactor: 1.0, riskFactor: 0.9, description: 'Frühling: Normale Lieferzeiten, niedriges Risiko' },
            { season: 'Summer', months: [5, 6, 7], deliveryTimeFactor: 0.9, riskFactor: 0.8, description: 'Sommer: Kürzere Lieferzeiten, niedrigeres Risiko' },
            { season: 'Autumn', months: [8, 9, 10], deliveryTimeFactor: 1.1, riskFactor: 1.0, description: 'Herbst: Längere Lieferzeiten, normales Risiko' },
            { season: 'Winter', months: [11, 0, 1], deliveryTimeFactor: 1.3, riskFactor: 1.4, description: 'Winter: Längere Lieferzeiten, höheres Risiko' }
          ];
        },
        DELIVERY_CACHE_TTL.TRENDS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return patterns;

    } catch (error) {
      console.error('Error fetching seasonal patterns:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Repository-Statistiken abrufen
   */
  async getStats(): Promise<RepositoryStats> {
    this.stats.hitRate = this.stats.totalQueries > 0 ?
      (this.stats.cacheHits / this.stats.totalQueries) * 100 : 0;
    this.stats.lastAccessed = new Date();

    return { ...this.stats };
  }

  /**
   * Repository-Cache invalidieren
   */
  async invalidateCache(key?: string): Promise<void> {
    if (key) {
      this.cache.invalidateByDataTypes([key]);
    } else {
      this.cache.invalidateByDataTypes(['delivery']);
    }
  }

  /**
   * Statistiken aktualisieren
   */
  private updateStats(startTime: number): void {
    const queryTime = Date.now() - startTime;
    this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
    this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
    this.stats.lastAccessed = new Date();
  }
}