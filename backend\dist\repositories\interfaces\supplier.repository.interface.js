"use strict";
/**
 * Supplier Repository Interface
 *
 * Definiert die Schnittstelle für Supplier-Datenoperationen
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierRepositoryImpl = void 0;
const supplier_repository_1 = require("../supplier.repository");
Object.defineProperty(exports, "SupplierRepositoryImpl", { enumerable: true, get: function () { return supplier_repository_1.SupplierRepositoryImpl; } });
