"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemController = void 0;
const system_repository_1 = require("../repositories/system.repository");
class SystemController {
    constructor() {
        this.repository = new system_repository_1.SystemRepositoryImpl();
    }
    /**
     * Get service level data
     * GET /api/system/service-level?startDate=2024-01-01&endDate=2024-01-31
     */
    async getServiceLevelData(req, res) {
        try {
            const { startDate, endDate, systemType, minServiceLevel, maxServiceLevel } = req.query;
            const filter = startDate || endDate || systemType || minServiceLevel || maxServiceLevel ?
                {
                    startDate: startDate,
                    endDate: endDate,
                    systemType: systemType,
                    minServiceLevel: minServiceLevel ? parseFloat(minServiceLevel) : undefined,
                    maxServiceLevel: maxServiceLevel ? parseFloat(maxServiceLevel) : undefined
                } : undefined;
            const data = await this.repository.serviceLevel.getAll(filter);
            res.json({
                success: true,
                data,
                count: data.length,
                filters: filter
            });
        }
        catch (error) {
            console.error('Error fetching service level data:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Service-Level-Daten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get current service level
     * GET /api/system/service-level/current
     */
    async getCurrentServiceLevel(req, res) {
        try {
            const data = await this.repository.serviceLevel.getCurrentServiceLevel();
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching current service level:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen des aktuellen Service-Levels',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get service level statistics
     * GET /api/system/service-level/stats
     */
    async getServiceLevelStats(req, res) {
        try {
            const { startDate, endDate, systemType, minServiceLevel, maxServiceLevel } = req.query;
            const filter = startDate || endDate || systemType || minServiceLevel || maxServiceLevel ?
                {
                    startDate: startDate,
                    endDate: endDate,
                    systemType: systemType,
                    minServiceLevel: minServiceLevel ? parseFloat(minServiceLevel) : undefined,
                    maxServiceLevel: maxServiceLevel ? parseFloat(maxServiceLevel) : undefined
                } : undefined;
            const data = await this.repository.serviceLevel.getServiceLevelStats(filter);
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching service level stats:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Service-Level-Statistiken',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get daily performance data
     * GET /api/system/performance?startDate=2024-01-01&endDate=2024-01-31
     */
    async getDailyPerformance(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const filter = startDate || endDate ? { startDate: startDate, endDate: endDate } : undefined;
            const data = await this.repository.dailyPerformance.getAll(filter);
            res.json({
                success: true,
                data,
                count: data.length,
                filters: filter
            });
        }
        catch (error) {
            console.error('Error fetching daily performance data:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der täglichen Performance-Daten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get performance trend analysis
     * GET /api/system/performance/trends?days=30
     */
    async getPerformanceTrend(req, res) {
        try {
            const { days } = req.query;
            const daysNum = days ? parseInt(days) : 30;
            const data = await this.repository.dailyPerformance.getPerformanceTrend(daysNum);
            res.json({
                success: true,
                data,
                days: daysNum,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching performance trend:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Performance-Trend-Analyse',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get picking data
     * GET /api/system/picking
     */
    async getPickingData(req, res) {
        try {
            const data = await this.repository.picking.getAll();
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('Error fetching picking data:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Picking-Daten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get picking efficiency
     * GET /api/system/picking/efficiency
     */
    async getPickingEfficiency(req, res) {
        try {
            const data = await this.repository.picking.getPickingEfficiency();
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching picking efficiency:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Picking-Effizienz',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get returns data
     * GET /api/system/returns
     */
    async getReturnsData(req, res) {
        try {
            const data = await this.repository.returns.getAll();
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('Error fetching returns data:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Returns-Daten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get returns analysis
     * GET /api/system/returns/analysis
     */
    async getReturnsAnalysis(req, res) {
        try {
            const data = await this.repository.returns.getReturnsAnalysis();
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching returns analysis:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Returns-Analyse',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get delivery positions data
     * GET /api/system/delivery-positions
     */
    async getDeliveryPositions(req, res) {
        try {
            const data = await this.repository.deliveryPositions.getAll();
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('Error fetching delivery positions data:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Delivery-Positions-Daten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get delivery analysis
     * GET /api/system/delivery-positions/analysis
     */
    async getDeliveryAnalysis(req, res) {
        try {
            const data = await this.repository.deliveryPositions.getDeliveryAnalysis();
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching delivery analysis:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Delivery-Analyse',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get tagesleistung data
     * GET /api/system/tagesleistung
     */
    async getTagesleistungData(req, res) {
        try {
            const data = await this.repository.tagesleistung.getAll();
            res.json({
                success: true,
                data,
                count: data.length
            });
        }
        catch (error) {
            console.error('Error fetching tagesleistung data:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Tagesleistungs-Daten',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get daily performance statistics
     * GET /api/system/tagesleistung/stats
     */
    async getDailyPerformanceStats(req, res) {
        try {
            const data = await this.repository.tagesleistung.getDailyPerformanceStats();
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching daily performance stats:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der täglichen Performance-Statistiken',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get system stats
     * GET /api/system/stats?startDate=2024-01-01&endDate=2024-01-31
     */
    async getSystemStats(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const filter = startDate || endDate ? { startDate: startDate, endDate: endDate } : undefined;
            const data = await this.repository.systemStats.getAll(filter);
            res.json({
                success: true,
                data,
                count: data.length,
                filters: filter
            });
        }
        catch (error) {
            console.error('Error fetching system stats:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der System-Statistiken',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get system health dashboard
     * GET /api/system/health
     */
    async getSystemHealthDashboard(req, res) {
        try {
            const data = await this.repository.systemStats.getSystemHealthDashboard();
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching system health dashboard:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen des System-Health-Dashboards',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get complete system dashboard
     * GET /api/system/dashboard?startDate=2024-01-01&endDate=2024-01-31
     */
    async getSystemDashboard(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const filter = startDate || endDate ? { startDate: startDate, endDate: endDate } : undefined;
            const data = await this.repository.getSystemDashboard(filter);
            res.json({
                success: true,
                data,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error fetching system dashboard:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen des System-Dashboards',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
}
exports.SystemController = SystemController;
