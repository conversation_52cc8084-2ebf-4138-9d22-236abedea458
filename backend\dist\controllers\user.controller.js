"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const user_repository_1 = require("../repositories/user.repository");
class UserController {
    constructor() {
        this.repository = new user_repository_1.UserRepository();
    }
    /**
     * Find user by email or username
     * GET /api/user/search?email=<EMAIL>&username=testuser
     */
    async findByEmailOrUsername(req, res) {
        try {
            const { email, username } = req.query;
            if (!email && !username) {
                return res.status(400).json({
                    success: false,
                    error: 'E-Mail oder Benutzername ist erforderlich'
                });
            }
            const user = await this.repository.findByEmailOrUsername(email, username);
            res.json({
                success: true,
                data: user,
                found: !!user
            });
        }
        catch (error) {
            console.error('Error finding user by email or username:', error);
            res.status(500).json({
                success: false,
                error: '<PERSON><PERSON> beim Suchen des Benutzers',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Find user by email
     * GET /api/user/email/:email
     */
    async findByEmail(req, res) {
        try {
            const { email } = req.params;
            if (!email) {
                return res.status(400).json({
                    success: false,
                    error: 'E-Mail ist erforderlich'
                });
            }
            const user = await this.repository.findByEmail(email);
            res.json({
                success: true,
                data: user,
                found: !!user
            });
        }
        catch (error) {
            console.error('Error finding user by email:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Suchen des Benutzers nach E-Mail',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Find user by username
     * GET /api/user/username/:username
     */
    async findByUsername(req, res) {
        try {
            const { username } = req.params;
            if (!username) {
                return res.status(400).json({
                    success: false,
                    error: 'Benutzername ist erforderlich'
                });
            }
            const user = await this.repository.findByUsername(username);
            res.json({
                success: true,
                data: user,
                found: !!user
            });
        }
        catch (error) {
            console.error('Error finding user by username:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Suchen des Benutzers nach Benutzername',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Find user by ID
     * GET /api/user/:id
     */
    async findById(req, res) {
        try {
            const { id } = req.params;
            if (!id || isNaN(parseInt(id))) {
                return res.status(400).json({
                    success: false,
                    error: 'Gültige Benutzer-ID ist erforderlich'
                });
            }
            const user = await this.repository.findById(parseInt(id));
            res.json({
                success: true,
                data: user,
                found: !!user
            });
        }
        catch (error) {
            console.error('Error finding user by ID:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Suchen des Benutzers nach ID',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Create new user
     * POST /api/user
     */
    async createUser(req, res) {
        try {
            const { email, username, firstName, lastName, passwordHash } = req.body;
            if (!email || !username || !passwordHash) {
                return res.status(400).json({
                    success: false,
                    error: 'E-Mail, Benutzername und Passwort-Hash sind erforderlich'
                });
            }
            const userData = {
                email,
                username,
                firstName,
                lastName,
                passwordHash
            };
            const user = await this.repository.createUser(userData);
            res.status(201).json({
                success: true,
                data: user,
                message: 'Benutzer erfolgreich erstellt'
            });
        }
        catch (error) {
            console.error('Error creating user:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Erstellen des Benutzers',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get all users (basic implementation)
     * GET /api/user/all
     */
    async getAllUsers(req, res) {
        try {
            // For now, return a message that this endpoint is not fully implemented
            // In a real implementation, you would need to add pagination support to the repository
            res.status(501).json({
                success: false,
                error: 'Endpoint noch nicht vollständig implementiert',
                message: 'Pagination für User-Repository muss noch implementiert werden'
            });
        }
        catch (error) {
            console.error('Error fetching all users:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen aller Benutzer',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
    /**
     * Get users by role (basic implementation)
     * GET /api/user/role/:roleName
     */
    async getUsersByRole(req, res) {
        try {
            const { roleName } = req.params;
            if (!roleName) {
                return res.status(400).json({
                    success: false,
                    error: 'Rollenname ist erforderlich'
                });
            }
            // For now, return a message that this endpoint is not fully implemented
            // In a real implementation, you would need to add role filtering to the repository
            res.status(501).json({
                success: false,
                error: 'Endpoint noch nicht vollständig implementiert',
                message: 'Role-Filtering für User-Repository muss noch implementiert werden'
            });
        }
        catch (error) {
            console.error('Error fetching users by role:', error);
            res.status(500).json({
                success: false,
                error: 'Fehler beim Abrufen der Benutzer nach Rolle',
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            });
        }
    }
}
exports.UserController = UserController;
