# RAG Setup Anleitung

## Problem
Die RAG-Funktionalität funktioniert nicht, weil die erforderlichen Tabellen in der PostgreSQL-Datenbank fehlen.

## Lösung
Die RAG-Tabellen müssen in der PostgreSQL-Datenbank erstellt werden.

### Option 1: Automatische Initialisierung (Empfohlen)

1. **Backend-Server starten** (falls noch nicht geschehen):
   ```bash
   cd backend
   pnpm run dev
   ```

2. **RAG-Datenbank initialisieren**:
   ```bash
   cd backend
   npm run rag:init-postgresql
   ```

   oder direkt:
   ```bash
   npx ts-node src/scripts/init-rag-postgresql.ts
   ```

   <PERSON><PERSON>ript erstellt:
   - Die erforderlichen Tabellen (`documents`, `chunks`, `embeddings`, `categories`)
   - Beispiel-Kategorien
   - Beispieldokumente mit Embeddings

### Option 2: <PERSON><PERSON>ll<PERSON>

1. **PostgreSQL-Verbindung herstellen**:
   ```bash
   psql postgresql://leitstand_dashboard:dashboard_password@localhost:5434/rag_knowledge
   ```

2. **SQL-Script ausführen**:
   ```bash
   cd backend
   psql postgresql://leitstand_dashboard:dashboard_password@localhost:5434/rag_knowledge -f scripts/init-rag-postgresql.sql
   ```

### Option 3: Direkte SQL-Befehle

Falls die obigen Optionen nicht funktionieren, können die Tabellen manuell erstellt werden:

```sql
-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    source TEXT NOT NULL,
    source_path TEXT,
    content_type TEXT DEFAULT 'text/plain',
    language TEXT DEFAULT 'de',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    indexed_at TIMESTAMP,
    status TEXT DEFAULT 'pending'
);

-- Chunks table
CREATE TABLE IF NOT EXISTS chunks (
    id TEXT PRIMARY KEY,
    document_id TEXT NOT NULL,
    content TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    token_count INTEGER,
    start_position INTEGER,
    end_position INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Embeddings table
CREATE TABLE IF NOT EXISTS embeddings (
    id TEXT PRIMARY KEY,
    chunk_id TEXT NOT NULL,
    vector FLOAT[] NOT NULL,
    model_name TEXT NOT NULL DEFAULT 'text-embedding-3-small',
    dimension INTEGER NOT NULL DEFAULT 1536,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chunk_id) REFERENCES chunks(id) ON DELETE CASCADE
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_source ON documents(source);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_embeddings_chunk_id ON embeddings(chunk_id);
```

## Überprüfung

Nach der Initialisierung können die API-Endpunkte getestet werden:

```bash
# Test basic RAG endpoint
curl -X GET http://localhost:3001/api/rag/test

# Test statistics endpoint
curl -X GET http://localhost:3001/api/rag/statistics

# Test recent queries endpoint
curl -X GET http://localhost:3001/api/rag/queries/recent
```

## Debugging

Falls immer noch Probleme auftreten:

1. **Browser-Konsole öffnen** und nach `[RAG Debug]` Nachrichten suchen
2. **Backend-Logs** überprüfen für detaillierte Fehlermeldungen
3. **PostgreSQL-Logs** überprüfen für Datenbankfehler

## Nächste Schritte

Nach erfolgreicher Initialisierung:
1. Die RAG-Funktionalität sollte im Frontend funktionieren
2. Dokumente können hochgeladen werden
3. Semantische Suche sollte verfügbar sein
4. Statistiken werden korrekt angezeigt
5. DocumentList zeigt alle hochgeladenen Dokumente an

## Wichtige Änderungen

- **PostgreSQLDocumentService**: Neue PostgreSQL-kompatible Dokumentverwaltung
- **PostgreSQLRAGService**: PostgreSQL-kompatible Vektoroperationen
- **Frontend-Debugging**: Erweiterte Debug-Funktionalität in DocumentList
- **Datenbank**: Alle Services verwenden jetzt die `rag_knowledge` PostgreSQL-Datenbank