import {
  pgTable,
  text,
  timestamp,
  integer,
  real,
  index,
  serial,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

// RAG Database Schema for Drizzle ORM
// Separate schema for RAG knowledge base

// Documents table - stores document metadata
export const documents = pgTable(
  "documents",
  {
    id: text("id").primaryKey(),
    title: text("title").notNull(),
    content: text("content").notNull(),
    source: text("source").notNull(),
    sourcePath: text("source_path"),
    contentType: text("content_type").default("text/plain"),
    language: text("language").default("de"),
    createdAt: timestamp("created_at", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    updatedAt: timestamp("updated_at", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
    indexedAt: timestamp("indexed_at", { mode: "date" }),
    status: text("status").default("pending"),
  },
  (table) => ({
    sourceIdx: index("documents_source_idx").on(table.source),
    statusIdx: index("documents_status_idx").on(table.status),
    createdAtIdx: index("documents_created_at_idx").on(table.createdAt),
  }),
);

// Chunks table - stores document chunks
export const chunks = pgTable(
  "chunks",
  {
    id: text("id").primaryKey(),
    documentId: text("document_id").notNull(),
    content: text("content").notNull(),
    chunkIndex: integer("chunk_index").notNull(),
    tokenCount: integer("token_count"),
    startPosition: integer("start_position"),
    endPosition: integer("end_position"),
    createdAt: timestamp("created_at", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => ({
    documentIdIdx: index("chunks_document_id_idx").on(table.documentId),
    chunkIndexIdx: index("chunks_chunk_index_idx").on(table.chunkIndex),
  }),
);

// Embeddings table - stores vector embeddings
export const embeddings = pgTable(
  "embeddings",
  {
    id: text("id").primaryKey(),
    chunkId: text("chunk_id").notNull(),
    vector: real("vector").array().notNull(), // PostgreSQL array of floats
    modelName: text("model_name").notNull().default("text-embedding-3-small"),
    dimension: integer("dimension").notNull().default(1536),
    createdAt: timestamp("created_at", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => ({
    chunkIdIdx: index("embeddings_chunk_id_idx").on(table.chunkId),
  }),
);

// Categories table - stores document categories
export const categories = pgTable(
  "categories",
  {
    id: text("id").primaryKey(),
    name: text("name").notNull().unique(),
    description: text("description"),
    parentId: text("parent_id"),
    createdAt: timestamp("created_at", { mode: "date" }).default(
      sql`CURRENT_TIMESTAMP`,
    ),
  },
  (table) => ({
    nameIdx: index("categories_name_idx").on(table.name),
    parentIdIdx: index("categories_parent_id_idx").on(table.parentId),
  }),
);

// Type exports for use in other files
export type Document = typeof documents.$inferSelect;
export type NewDocument = typeof documents.$inferInsert;
export type Chunk = typeof chunks.$inferSelect;
export type NewChunk = typeof chunks.$inferInsert;
export type Embedding = typeof embeddings.$inferSelect;
export type NewEmbedding = typeof embeddings.$inferInsert;
export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;