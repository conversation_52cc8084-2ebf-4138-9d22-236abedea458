/**
 * SFM Dashboard Drizzle Client
 * Separate Client für die SFM-Dashboard-Datenbank gemäß PrismaRegeln.md
 *
 * Dieser Client wird für alle SFM-Dashboard-bezogenen Datenbankoperationen verwendet.
 * Er nutzt die gleiche PostgreSQL-Datenbank wie der Hauptclient.
 * <PERSON><PERSON><PERSON><PERSON> von Prisma zu Drizzle ORM entsprechend der Projektmigration.
 */

import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from '../src/db/schema';
import {
  loadDatabaseConfig,
  createPoolConfig,
  validateDatabaseConfig
} from '../src/config/database.config';

// SFM Dashboard-spezifischer Drizzle Client für PostgreSQL
// Verwendet die gleiche Datenbankverbindung wie der Hauptclient
const dbConfig = loadDatabaseConfig();

if (!validateDatabaseConfig(dbConfig)) {
  console.error('❌ Ungültige Datenbankkonfiguration für SFM Dashboard Client. Anwendung wird beendet.');
  process.exit(1);
}

const poolConfig = createPoolConfig(dbConfig);
const pool = new Pool(poolConfig);
const sfmClient = drizzle(pool, { schema });

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await pool.end();
});

process.on('SIGINT', async () => {
  await pool.end();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await pool.end();
  process.exit(0);
});

export { sfmClient };
export default sfmClient;