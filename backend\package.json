{"name": "sfm-chatbot-backend", "version": "1.0.0", "description": "Backend für den SFM Chatbot mit OpenRouter-Integration", "main": "src/server.js", "scripts": {"start": "ts-node src/server.ts", "dev": "nodemon", "build": "tsc", "serve": "node dist/server.js", "shutdown": "node -e \"const http = require('http'); http.get('http://localhost:3001/api/shutdown');\";", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:chat-integration": "jest --testPathPattern=chat-integration-complete", "test:chat-performance": "jest --testPathPattern=chat-performance", "test:chat-e2e": "jest --testPathPattern=chat-e2e-scenarios", "test:chat-all": "ts-node src/tests/run-integration-tests.ts", "rag:init": "ts-node src/scripts/init-rag-database.ts", "rag:reset": "del /f database\\rag_knowledge.db* 2>nul & pnpm run rag:init", "rag:populate-categories": "ts-node scripts/populate-categories.ts", "rag:init-postgresql": "ts-node src/scripts/init-rag-postgresql.ts", "db:migrate": "ts-node src/scripts/migrate-database.ts", "db:migrate:pnpm": "pnpm ts-node src/scripts/migrate-database.ts"}, "dependencies": {"@libsql/client": "^0.15.14", "@prisma/client": "6.15.0", "@types/bcryptjs": "^2.4.6", "@types/handlebars": "^4.0.40", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.6.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.5", "express": "^4.18.2", "express-rate-limit": "6.10.0", "handlebars": "^4.7.8", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.5", "openai": "^4.43.0", "pg": "^8.16.3", "uuid": "^11.0.4", "zod": "^3.25.76"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.12", "@types/multer": "^1.4.13", "@types/node": "^20.3.1", "@types/nodemailer": "^7.0.1", "@types/pg": "^8.15.5", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.0.1", "prisma": "6.15.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.9.2", "vite": "^7.0.2"}, "engines": {"node": ">=14.0.0"}}