import React, { useEffect, useState, memo } from "react";
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  XAxis,
  YAxis,
  ReferenceLine,
  Cell,
} from "recharts";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp } from "lucide-react";
import { AnimatePresence } from "motion/react";
import { cn } from "@/lib/utils";
import { useMotionValueEvent, useSpring } from "framer-motion";
import apiService from "@/services/api.service";

/**
 * TagesleistungChart Komponente
 * 
 * Zeigt Produktionsdaten als Balkendiagramm mit produzierten Tonnagen, Direktverladung KIAA, 
 * Umschlag, kg pro Colli und Elefanten an.
 * Verwendet multiple Y-Achsen für bessere Sichtbarkeit der unterschiedlichen Skalen.
 * 
 * @param data Optional: Array von Datenpunkten mit Produktionsdaten
 * @param dateRange Optional: Datumsbereich für die Filterung der Daten
 */

// Definition des Datentyps für die Produktionsdaten
interface TagesleistungDataPoint {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

// Definition des Datentyps für Datenbankzeilen
interface DatabaseRow {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

interface TagesleistungChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: TagesleistungDataPoint[];
  dateRange?: DateRange;
}

export const TagesleistungChart = memo(function TagesleistungChart({ data: propData, dateRange }: TagesleistungChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<TagesleistungDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string>(new Date().toLocaleDateString());
  
  // State für interaktive Elemente aus LineBarChart
  const [activeIndex, setActiveIndex] = React.useState<number | undefined>(undefined);
  const [activeBarKey, setActiveBarKey] = React.useState<string | null>(null);

  // Konfiguration für das Diagramm mit CSS-Variablen
  const chartConfig = {
    produzierte_tonnagen: {
      label: t("produzierteTonnagen"),
      color: "var(--chart-1)",
    },
    direktverladung_kiaa: {
      label: t("direktverladungKIAA"),
      color: "var(--chart-2)",
    },
    umschlag: {
      label: t("umschlag"),
      color: "var(--chart-3)",
    },
    kg_pro_colli: {
      label: t("kgProColli"),
      color: "var(--chart-4)",
    },
    elefanten: {
      label: t("elefanten"),
      color: "var(--chart-5)", // Standardfarbe für Elefanten hinzugefügt
    },
  };

  // Logik für maximale Werte und Animationen aus LineBarChart
  const maxValueData = React.useMemo(() => {
    if (chartData.length === 0) return { index: 0, value: 0, key: 'produzierte_tonnagen' };
    
    // Finde den maximalen Wert über alle Datenreihen
    let maxValue = 0;
    let maxIndex = 0;
    let maxKey = 'produzierte_tonnagen';
    
    chartData.forEach((dataPoint, index) => {
      Object.entries(dataPoint).forEach(([key, value]) => {
        if (key !== 'date' && typeof value === 'number' && value > maxValue) {
          maxValue = value;
          maxIndex = index;
          maxKey = key;
        }
      });
    });
    
    // Wenn ein aktiver Index gesetzt ist, verwende diesen Wert
    if (activeIndex !== undefined && activeBarKey) {
      return {
        index: activeIndex,
        value: chartData[activeIndex][activeBarKey as keyof TagesleistungDataPoint] as number,
        key: activeBarKey
      };
    }
    
    return { index: maxIndex, value: maxValue, key: maxKey };
  }, [chartData, activeIndex, activeBarKey]);

  const maxValueSpring = useSpring(maxValueData.value, {
    stiffness: 100,
    damping: 20,
  });

  const [springyValue, setSpringyValue] = React.useState(maxValueData.value);

  useMotionValueEvent(maxValueSpring, "change", (latest) => {
    setSpringyValue(Number(latest.toFixed(0)));
  });

  React.useEffect(() => {
    maxValueSpring.set(maxValueData.value);
  }, [maxValueData.value, maxValueSpring]);

  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      setChartData(propData);
    } else {
      // Lade Daten aus der Datenbank
      const loadData = async () => {
        try {
          setLoading(true);
          setError(null);

          // Konvertiere dateRange zu API-Format
          const startDate = dateRange?.from ? dateRange.from.toISOString().split('T')[0] : undefined;
          const endDate = dateRange?.to ? dateRange.to.toISOString().split('T')[0] : undefined;

          // Daten aus der Datenbank laden
          const result = await apiService.getTagesleistungData(startDate, endDate);

          if (result && Array.isArray(result) && result.length > 0) {

            // Wandle Datenbankzeilen in das richtige Format um
            const processedData: TagesleistungDataPoint[] = result.map((row: unknown) => {
              const dbRow = row as any; // Flexiblere Typisierung für Date-Handling

              // Backend liefert echte Datumswerte
              let dateValue: string;
              if (typeof dbRow.date === 'string') {
                dateValue = dbRow.date;
              } else if (dbRow.date instanceof Date) {
                dateValue = dbRow.date.toISOString().split('T')[0];
              } else if (typeof dbRow.date === 'number') {
                dateValue = new Date(dbRow.date).toISOString().split('T')[0];
              } else {
                console.warn('TagesleistungChart: Null/undefined Datum - überspringe Datensatz:', dbRow);
                return { date: '', produzierte_tonnagen: 0, direktverladung_kiaa: 0, umschlag: 0, kg_pro_colli: 0, elefanten: 0 };
              }

              return {
                date: dateValue,
                produzierte_tonnagen: Number(dbRow.produzierte_tonnagen) || 0,
                direktverladung_kiaa: Number(dbRow.direktverladung_kiaa) || 0,
                umschlag: Number(dbRow.umschlag) || 0,
                kg_pro_colli: Number(dbRow.kg_pro_colli) || 0,
                elefanten: Number(dbRow.elefanten) || 0
              };
            });

            // Filtere Daten basierend auf dem ausgewählten Datumsbereich
            let filteredData = processedData;
            if (dateRange?.from || dateRange?.to) {
              filteredData = processedData.filter((item) => {
                try {
                  const itemDate = new Date(item.date);

                  if (dateRange?.from && itemDate < dateRange.from) return false;
                  if (dateRange?.to && itemDate > dateRange.to) return false;

                  return true;
                } catch (e) {
                  return true; // Bei Fehlern beim Datumsvergleich den Datenpunkt behalten
                }
              });
            }

            // Sortiere die Daten nach Datum in aufsteigender Reihenfolge (älteste zuerst)
            // Dies stellt sicher, dass die X-Achse von links (älteste) nach rechts (neueste) angezeigt wird
            filteredData.sort((a, b) => {
              try {
                const dateA = new Date(a.date).getTime();
                const dateB = new Date(b.date).getTime();
                return dateA - dateB; // Aufsteigende Sortierung
              } catch (e) {
                return 0; // Bei Fehlern beim Datumsvergleich Reihenfolge beibehalten
              }
            });

            setChartData(filteredData);
            setLastUpdate(new Date().toLocaleDateString());
          } else {
            setChartData([]);
          }
        } catch (err) {
          setError('Fehler beim Laden der Produktionsdaten');
          setChartData([]);
        } finally {
          setLoading(false);
        }
      };

      loadData();
    }
  }, [propData, dateRange]); // dateRange als Abhängigkeit hinzugefügt

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-red-500">
        <p className="font-bold">{t("error")}</p>
        <p>{error}</p>
      </div>
    );
  }

  if (chartData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-105 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>TAGESLEISTUNG</CardTitle>
        <CardDescription>
          Produzierte Tonnagen, Direktverladung KIAA, Umschlag, kg pro Colli und Elefanten
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          <ChartContainer config={chartConfig} className="w-full h-60">
            <ComposedChart
              data={chartData}
              margin={{ top: 5, right: 5, left: 35, bottom: 5 }}
              onMouseLeave={() => setActiveIndex(undefined)}
            >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              height={40}
              tickFormatter={(value) => {
                try {
                  const date = new Date(value);
                  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                } catch {
                  return value;
                }
              }}
            />
            <YAxis 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              tick={{ fontSize: 12 }}
              width={35}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelClassName="font-bold"
                  labelFormatter={(label) => {
                    // Formatiere das Datum im Tooltip als DD.MM.YYYY
                    try {
                      const date = new Date(label);
                      return `${t("date")}: ${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
                    } catch {
                      return `${t("date")}: ${label}`;
                    }
                  }}
                />
              }
            />
            <ChartLegend content={({ payload }) => (
              <ChartLegendContent 
                payload={payload} 
                className="p-2 rounded-md"
              />
            )} />
            <Bar
              dataKey="produzierte_tonnagen"
              name="Produzierte Tonnagen"
              stroke="#000000"
              strokeWidth={2}
              fill="var(--color-produzierte_tonnagen)"
              radius={4}
              onMouseEnter={() => setActiveBarKey('produzierte_tonnagen')}
            >
              {chartData.map((_, index) => (
                <Cell
                  className="duration-200"
                  opacity={index === maxValueData.index && maxValueData.key === 'produzierte_tonnagen' ? 0.2 : 1}
                  key={`produzierte_tonnagen-${index}`}
                  onMouseEnter={() => {
                    setActiveIndex(index);
                    setActiveBarKey('produzierte_tonnagen');
                  }}
                />
              ))}
            </Bar>
            <Bar
              dataKey="direktverladung_kiaa"
              name="Direktverladung KIAA"
              stroke="#000000"
              strokeWidth={2}
              fill="var(--color-direktverladung_kiaa)"
              radius={4}
              onMouseEnter={() => setActiveBarKey('direktverladung_kiaa')}
            >
              {chartData.map((_, index) => (
                <Cell
                  className="duration-200"
                  opacity={index === maxValueData.index && maxValueData.key === 'direktverladung_kiaa' ? 0.2 : 1}
                  key={`direktverladung_kiaa-${index}`}
                  onMouseEnter={() => {
                    setActiveIndex(index);
                    setActiveBarKey('direktverladung_kiaa');
                  }}
                />
              ))}
            </Bar>
            <Bar
              dataKey="umschlag"
              name="Umschlag"
              stroke="#000000"
              strokeWidth={2}
              fill="var(--color-umschlag)"
              radius={4}
              onMouseEnter={() => setActiveBarKey('umschlag')}
            >
              {chartData.map((_, index) => (
                <Cell
                  className="duration-200"
                  opacity={index === maxValueData.index && maxValueData.key === 'umschlag' ? 0.2 : 1}
                  key={`umschlag-${index}`}
                  onMouseEnter={() => {
                    setActiveIndex(index);
                    setActiveBarKey('umschlag');
                  }}
                />
              ))}
            </Bar>
            <Bar
              dataKey="kg_pro_colli"
              name="kg pro Colli"
              stroke="#000000"
              strokeWidth={2}
              fill="var(--color-kg_pro_colli)"
              radius={4}
              onMouseEnter={() => setActiveBarKey('kg_pro_colli')}
            >
              {chartData.map((_, index) => (
                <Cell
                  className="duration-200"
                  opacity={index === maxValueData.index && maxValueData.key === 'kg_pro_colli' ? 0.2 : 1}
                  key={`kg_pro_colli-${index}`}
                  onMouseEnter={() => {
                    setActiveIndex(index);
                    setActiveBarKey('kg_pro_colli');
                  }}
                />
              ))}
            </Bar>
            <Bar
              dataKey="elefanten"
              name="Elefanten"
              stroke="#000000"
              strokeWidth={2}
              fill="var(--color-elefanten)"
              radius={4}
              onMouseEnter={() => setActiveBarKey('elefanten')}
            >
              {chartData.map((_, index) => (
                <Cell
                  className="duration-200"
                  opacity={index === maxValueData.index && maxValueData.key === 'elefanten' ? 0.2 : 1}
                  key={`elefanten-${index}`}
                  onMouseEnter={() => {
                    setActiveIndex(index);
                    setActiveBarKey('elefanten');
                  }}
                />
              ))}
            </Bar>
            <ReferenceLine
              opacity={1}
              y={springyValue}
              stroke="var(--secondary-foreground)"
              strokeWidth={1}
              
              label={<CustomReferenceLabel value={maxValueData.value} />}
            />
          </ComposedChart>
        </ChartContainer>
        </AnimatePresence>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Tagesleistung über die ausgewählte Zeitperiode: {maxValueData.value}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Letzte Aktualisierung: {lastUpdate}
            </div>
          </div>
          <div className="text-muted-foreground pt-12 ml-auto flex items-end gap-2 leading-none">
            {error ? (
              <Badge variant="destructive">Datenbank nicht verfügbar</Badge>
            ) : (
              <Badge variant="outline2">Letzte Aktualisierung: {new Date().toLocaleDateString()}</Badge>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});

// Komponente für das benutzerdefinierte Label der Referenzlinie aus LineBarChart
interface CustomReferenceLabelProps {
  viewBox?: {
    x?: number;
    y?: number;
  };
  value: number;
}

const CustomReferenceLabel: React.FC<CustomReferenceLabelProps> = (props) => {
  const { viewBox, value } = props;
  const x = viewBox?.x ?? 0;
  const y = viewBox?.y ?? 0;

  // Breite basierend auf der Wertlänge anpassen
  const width = React.useMemo(() => {
    const characterWidth = 8; // Durchschnittliche Breite eines Zeichens in Pixel
    const padding = 10;
    return value.toString().length * characterWidth + padding;
  }, [value]);

  return (
    <>
      <rect
        x={x - 35} // CHART_MARGIN Wert direkt verwenden
        y={y - 9}
        width={width}
        height={18}
        fill="var(--secondary-foreground)"
        rx={4}
      />
      <text
        fontWeight={600}
        x={x - 35 + 6} // CHART_MARGIN Wert direkt verwenden
        y={y + 4}
        fill="var(--primary-foreground)"
      >
        {value}
      </text>
    </>
  );
};