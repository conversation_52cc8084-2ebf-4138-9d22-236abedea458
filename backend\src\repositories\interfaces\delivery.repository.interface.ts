/**
 * Delivery Repository Interface
 *
 * Definiert die Schnittstelle für Delivery-Datenoperationen
 */

export interface DeliveryRepository {
  getAll(filter?: any): Promise<any[]>;
  getDeliveryStats(): Promise<any>;
  getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<any[]>;
  getDeliveryHistory(timeRange: { days: number }): Promise<any[]>;
  getActiveRoutes(): Promise<any[]>;
  getDeliveryPerformanceMetrics(timeRange: { days: number }): Promise<any>;
  getDeliveryTrends(timeRange: { days: number }): Promise<any[]>;
  getDelayReasons(timeRange: { days: number }): Promise<any[]>;
  createDelivery(delivery: any): Promise<string>;
  updateDeliveryStatus(deliveryId: string, status: string, actualDeliveryTime?: number): Promise<void>;
  getDeliveriesByRoute(routeId: string): Promise<any[]>;
  getSeasonalPatterns(): Promise<any[]>;
  getStats(): Promise<any>;
  invalidateCache(key?: string): Promise<void>;
}