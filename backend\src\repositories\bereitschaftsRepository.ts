import { db } from '../db';
import { bereitschaftsPersonen, bereitschaftsWochen, bereitschaftsAusnahmen, bereitschaftsKonfiguration } from '../db/schema';
import { eq, and, or, desc, asc, gte, lte, lt, gt } from 'drizzle-orm';
import { startOfWeek, addWeeks, addDays, format } from 'date-fns';
import { de } from 'date-fns/locale';

export interface BereitschaftsPersonData {
  id?: number;
  name: string;
  telefon: string;
  email: string;
  abteilung: string;
  aktiv?: boolean;
  reihenfolge?: number;
}

export interface BereitschaftsWocheData {
  id?: number;
  personId: number;
  wochenStart: Date;
  wochenEnde: Date;
  von: Date;
  bis: Date;
  aktiv?: boolean;
  notiz?: string;
}

export interface BereitschaftsAusnahmeData {
  id?: number;
  personId: number;
  von: Date;
  bis: Date;
  grund: string;
  ersatzPersonId?: number;
  aktiv?: boolean;
}

export interface BereitschaftsKonfigurationData {
  id?: number;
  wechselTag?: number;
  wechselUhrzeit?: string;
  rotationAktiv?: boolean;
  benachrichtigungTage?: number;
  emailBenachrichtigung?: boolean;
}

export class BereitschaftsRepository {
  private db = db;

  // Personen verwalten
  async getAllPersonen() {
    const personen = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.aktiv, true))
      .orderBy(asc(bereitschaftsPersonen.reihenfolge));

    // Fetch bereitschaftsWochen for each person separately
    const personenWithWochen = await Promise.all(
      personen.map(async (person) => {
        const wochen = await this.db.select().from(bereitschaftsWochen)
          .where(and(
            eq(bereitschaftsWochen.personId, person.id),
            eq(bereitschaftsWochen.aktiv, true)
          ))
          .orderBy(desc(bereitschaftsWochen.wochenStart))
          .limit(5);
        
        return { ...person, bereitschaftsWochen: wochen };
      })
    );

    return personenWithWochen;
  }

  async getPersonById(id: number) {
    const result = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, id))
      .limit(1);
    
    const person = result[0];
    if (!person) return null;

    const [wochen, ausnahmen] = await Promise.all([
      this.db.select().from(bereitschaftsWochen)
        .where(and(
          eq(bereitschaftsWochen.personId, id),
          eq(bereitschaftsWochen.aktiv, true)
        ))
        .orderBy(desc(bereitschaftsWochen.wochenStart)),

      this.db.select().from(bereitschaftsAusnahmen)
        .where(and(
          eq(bereitschaftsAusnahmen.personId, id),
          eq(bereitschaftsAusnahmen.aktiv, true)
        ))
        .orderBy(desc(bereitschaftsAusnahmen.von))
    ]);

    return {
      ...person,
      bereitschaftsWochen: wochen,
      bereitschaftsAusnahmen: ausnahmen
    };
  }

  async createPerson(data: BereitschaftsPersonData) {
    // Get max reihenfolge
    const maxResult = await this.db.select({
      maxReihenfolge: bereitschaftsPersonen.reihenfolge
    })
    .from(bereitschaftsPersonen)
    .orderBy(desc(bereitschaftsPersonen.reihenfolge))
    .limit(1);

    const maxReihenfolge = maxResult[0]?.maxReihenfolge ?? 0;
    const now = new Date().toISOString();

    const result = await this.db.insert(bereitschaftsPersonen)
      .values({
        name: data.name,
        telefon: data.telefon,
        email: data.email,
        abteilung: data.abteilung,
        aktiv: !!data.aktiv,
        reihenfolge: data.reihenfolge ?? maxReihenfolge + 1,
        createdAt: now,
        updatedAt: now
      })
      .returning();

    return result[0];
  }

  async updatePerson(id: number, data: Partial<BereitschaftsPersonData>) {
    const updateData: any = { updatedAt: new Date().toISOString() };

    if (data.name !== undefined) updateData.name = data.name;
    if (data.telefon !== undefined) updateData.telefon = data.telefon;
    if (data.email !== undefined) updateData.email = data.email;
    if (data.abteilung !== undefined) updateData.abteilung = data.abteilung;
    if (data.aktiv !== undefined) updateData.aktiv = !!data.aktiv;
    if (data.reihenfolge !== undefined) updateData.reihenfolge = data.reihenfolge;

    const result = await this.db.update(bereitschaftsPersonen)
      .set(updateData)
      .where(eq(bereitschaftsPersonen.id, id))
      .returning();

    return result[0];
  }

  async deletePerson(id: number) {
    const result = await this.db.update(bereitschaftsPersonen)
      .set({ aktiv: false, updatedAt: new Date().toISOString() })
      .where(eq(bereitschaftsPersonen.id, id))
      .returning();

    return result[0];
  }

  async updatePersonenReihenfolge(personenIds: number[]) {
    const updates = personenIds.map(async (id, index) =>
      this.db.update(bereitschaftsPersonen)
        .set({ reihenfolge: index + 1 })
        .where(eq(bereitschaftsPersonen.id, id))
        .returning()
    );

    return await Promise.all(updates);
  }

  // Wochen verwalten
  async getWochenPlan(startDate: Date, anzahlWochen: number) {
    const endDate = addWeeks(startDate, anzahlWochen);
    // Konvertiere Dates zu ISO-Strings für DATETIME-Vergleich
    const startString = startDate.toISOString();
    const endString = endDate.toISOString();

    const wochen = await this.db.select().from(bereitschaftsWochen)
      .where(and(
        eq(bereitschaftsWochen.aktiv, true),
        gte(bereitschaftsWochen.wochenStart, startString),
        lt(bereitschaftsWochen.wochenStart, endString)
      ))
      .orderBy(asc(bereitschaftsWochen.wochenStart));

    // Fetch person data for each week
    const wochenWithPerson = await Promise.all(
      wochen.map(async (woche) => {
        const person = await this.db.select().from(bereitschaftsPersonen)
          .where(eq(bereitschaftsPersonen.id, woche.personId))
          .limit(1);

        return { ...woche, person: person[0] || null };
      })
    );

    return wochenWithPerson;
  }

  // Alle Wochen laden (inklusive inaktive) für vollständige Anzeige
  async getAllWochen(startDate: Date, anzahlWochen: number) {
    const endDate = addWeeks(startDate, anzahlWochen);
    // Konvertiere Dates zu ISO-Strings für DATETIME-Vergleich
    const startString = startDate.toISOString();
    const endString = endDate.toISOString();

    const wochen = await this.db.select().from(bereitschaftsWochen)
      .where(and(
        gte(bereitschaftsWochen.wochenStart, startString),
        lt(bereitschaftsWochen.wochenStart, endString)
      ))
      .orderBy(asc(bereitschaftsWochen.wochenStart));

    // Fetch person data for each week
    const wochenWithPerson = await Promise.all(
      wochen.map(async (woche) => {
        const person = await this.db.select().from(bereitschaftsPersonen)
          .where(eq(bereitschaftsPersonen.id, woche.personId))
          .limit(1);

        return { ...woche, person: person[0] || null };
      })
    );

    return wochenWithPerson;
  }

  async getAktuelleBereitschaft() {
    // Konvertiere aktuelles Datum zu ISO-String für DATETIME-Vergleich
    const heute = new Date().toISOString();
    
    const result = await this.db.select().from(bereitschaftsWochen)
      .where(and(
        eq(bereitschaftsWochen.aktiv, true),
        lte(bereitschaftsWochen.von, heute),
        gt(bereitschaftsWochen.bis, heute)
      ))
      .limit(1);

    const woche = result[0];
    if (!woche) return null;

    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, woche.personId))
      .limit(1);

    return { ...woche, person: person[0] || null };
  }

  async createWoche(data: BereitschaftsWocheData) {
    const now = new Date().toISOString();
    const startDate = new Date(data.wochenStart);

    // Calculate kalenderwoche and jahr from wochenStart
    const kalenderwoche = this.getWeekNumber(startDate);
    const jahr = startDate.getFullYear();

    // Convert Date objects to ISO strings for DATETIME storage
    const wocheData = {
      personId: data.personId,
      wochenStart: data.wochenStart.toISOString(),
      wochenEnde: data.wochenEnde.toISOString(),
      von: data.von.toISOString(),
      bis: data.bis.toISOString(),
      aktiv: !!data.aktiv,
      notiz: data.notiz || null,
      createdAt: now,
      updatedAt: now
    };

    const result = await this.db.insert(bereitschaftsWochen)
      .values(wocheData)
      .returning();

    const woche = result[0];

    // Fetch person data
    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, woche.personId))
      .limit(1);

    return { ...woche, person: person[0] || null };
  }

  // Helper method to calculate week number
  private getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }

  async updateWoche(id: number, data: Partial<BereitschaftsWocheData>) {
    // Convert Date objects to ISO strings for DATETIME storage
    const updateData: any = { updatedAt: new Date().toISOString() };

    if (data.personId !== undefined) updateData.personId = data.personId;
    if (data.wochenStart) {
      updateData.wochenStart = data.wochenStart.toISOString();
    }
    if (data.wochenEnde) updateData.wochenEnde = data.wochenEnde.toISOString();
    if (data.von) updateData.von = data.von.toISOString();
    if (data.bis) updateData.bis = data.bis.toISOString();
    if (data.aktiv !== undefined) updateData.aktiv = !!data.aktiv;
    if (data.notiz !== undefined) updateData.notiz = data.notiz;

    const result = await this.db.update(bereitschaftsWochen)
      .set(updateData)
      .where(eq(bereitschaftsWochen.id, id))
      .returning();

    const woche = result[0];

    // Fetch person data
    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, woche.personId))
      .limit(1);

    return { ...woche, person: person[0] || null };
  }

  async deleteWoche(id: number) {
    const result = await this.db.update(bereitschaftsWochen)
      .set({ aktiv: false, updatedAt: new Date().toISOString() })
      .where(eq(bereitschaftsWochen.id, id))
      .returning();

    return result[0];
  }

  // Automatische Wochenplanung generieren
  async generiereWochenplan(startDate: Date, anzahlWochen: number) {
    const personen = await this.getAllPersonen();
    if (personen.length === 0) {
      throw new Error('Keine aktiven Bereitschaftspersonen gefunden');
    }

    const konfiguration = await this.getKonfiguration();
    const wechselTag = konfiguration?.wechselTag ?? 5; // Freitag
    const wechselUhrzeit = konfiguration?.wechselUhrzeit ?? '08:00';

    const wochen: BereitschaftsWocheData[] = [];

    for (let i = 0; i < anzahlWochen; i++) {
      const wochenStart = addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), i);
      const wochenEnde = addWeeks(wochenStart, 1);
      
      // Berechne Freitag der Woche
      const freitag = addDays(wochenStart, wechselTag - 1);
      const naechsterFreitag = addDays(freitag, 7);

      // Rotiere durch die Personen
      const personIndex = i % personen.length;
      const person = personen[personIndex];

      wochen.push({
        personId: person.id,
        wochenStart,
        wochenEnde,
        von: freitag,
        bis: naechsterFreitag,
        aktiv: true
      });
    }

    // Lösche bestehende Wochen im Zeitraum
    const startString = startOfWeek(startDate, { weekStartsOn: 1 }).toISOString();
    const endString = addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), anzahlWochen).toISOString();

    await this.db.update(bereitschaftsWochen)
      .set({ aktiv: false, updatedAt: new Date().toISOString() })
      .where(and(
        gte(bereitschaftsWochen.wochenStart, startString),
        lt(bereitschaftsWochen.wochenStart, endString)
      ));

    // Erstelle neue Wochen
    const erstellteWochen = await Promise.all(
      wochen.map(woche => this.createWoche(woche))
    );

    return erstellteWochen;
  }

  // Ausnahmen verwalten
  async getAllAusnahmen() {
    const ausnahmen = await this.db.select().from(bereitschaftsAusnahmen)
      .where(eq(bereitschaftsAusnahmen.aktiv, true))
      .orderBy(desc(bereitschaftsAusnahmen.von));

    // Fetch person data for each ausnahme
    const ausnahmenWithPerson = await Promise.all(
      ausnahmen.map(async (ausnahme) => {
        const person = await this.db.select().from(bereitschaftsPersonen)
          .where(eq(bereitschaftsPersonen.id, ausnahme.personId))
          .limit(1);

        return { ...ausnahme, person: person[0] || null };
      })
    );

    return ausnahmenWithPerson;
  }

  async createAusnahme(data: BereitschaftsAusnahmeData) {
    const now = new Date().toISOString();

    const result = await this.db.insert(bereitschaftsAusnahmen)
      .values({
        personId: data.personId,
        von: data.von.toISOString(),
        bis: data.bis.toISOString(),
        grund: data.grund,
        ersatzPersonId: data.ersatzPersonId || null,
        aktiv: !!data.aktiv,
        createdAt: now,
        updatedAt: now
      })
      .returning();

    const ausnahme = result[0];

    // Fetch person data
    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, ausnahme.personId))
      .limit(1);

    return { ...ausnahme, person: person[0] || null };
  }

  async updateAusnahme(id: number, data: Partial<BereitschaftsAusnahmeData>) {
    const updateData: any = { updatedAt: new Date().toISOString() };

    if (data.personId !== undefined) updateData.personId = data.personId;
    if (data.von !== undefined) updateData.von = data.von.toISOString();
    if (data.bis !== undefined) updateData.bis = data.bis.toISOString();
    if (data.grund !== undefined) updateData.grund = data.grund;
    if (data.ersatzPersonId !== undefined) updateData.ersatzPersonId = data.ersatzPersonId;
    if (data.aktiv !== undefined) updateData.aktiv = !!data.aktiv;

    const result = await this.db.update(bereitschaftsAusnahmen)
      .set(updateData)
      .where(eq(bereitschaftsAusnahmen.id, id))
      .returning();

    const ausnahme = result[0];

    // Fetch person data
    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, ausnahme.personId))
      .limit(1);

    return { ...ausnahme, person: person[0] || null };
  }

  async deleteAusnahme(id: number) {
    const result = await this.db.update(bereitschaftsAusnahmen)
      .set({ aktiv: false, updatedAt: new Date().toISOString() })
      .where(eq(bereitschaftsAusnahmen.id, id))
      .returning();

    return result[0];
  }

  // Konfiguration verwalten
  async getKonfiguration() {
    const result = await this.db.select().from(bereitschaftsKonfiguration)
      .orderBy(desc(bereitschaftsKonfiguration.createdAt))
      .limit(1);

    const config = result[0];

    // Standardkonfiguration falls keine existiert
    if (!config) {
      return await this.createKonfiguration({});
    }

    // Return config with proper field mapping
    return {
      id: config.id,
      wechselTag: config.wechselTag,
      wechselUhrzeit: config.wechselUhrzeit,
      rotationAktiv: !!config.rotationAktiv,
      benachrichtigungTage: config.benachrichtigungTage,
      emailBenachrichtigung: !!config.emailBenachrichtigung,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    };
  }

  async createKonfiguration(data: BereitschaftsKonfigurationData) {
    const now = new Date().toISOString();

    const result = await this.db.insert(bereitschaftsKonfiguration)
      .values({
        wechselTag: data.wechselTag ?? 5,
        wechselUhrzeit: data.wechselUhrzeit ?? '08:00',
        rotationAktiv: !!data.rotationAktiv,
        benachrichtigungTage: data.benachrichtigungTage ?? 2,
        emailBenachrichtigung: !!data.emailBenachrichtigung,
        createdAt: now,
        updatedAt: now
      })
      .returning();

    const created = result[0];

    // Return mapped result
    return {
      id: created.id,
      wechselTag: created.wechselTag,
      wechselUhrzeit: created.wechselUhrzeit,
      rotationAktiv: !!created.rotationAktiv,
      benachrichtigungTage: created.benachrichtigungTage,
      emailBenachrichtigung: !!created.emailBenachrichtigung,
      createdAt: created.createdAt,
      updatedAt: created.updatedAt
    };
  }

  async updateKonfiguration(data: Partial<BereitschaftsKonfigurationData>) {
    const existingResult = await this.db.select().from(bereitschaftsKonfiguration)
      .orderBy(desc(bereitschaftsKonfiguration.createdAt))
      .limit(1);

    const existingConfig = existingResult[0];
    if (!existingConfig) {
      throw new Error('Keine Konfiguration gefunden');
    }

    // Map frontend fields to database fields
    const updateData: any = { updatedAt: new Date().toISOString() };
    if (data.wechselTag !== undefined) updateData.wechselTag = data.wechselTag;
    if (data.wechselUhrzeit !== undefined) updateData.wechselUhrzeit = data.wechselUhrzeit;
    if (data.rotationAktiv !== undefined) updateData.rotationAktiv = data.rotationAktiv ? 1 : 0;
    if (data.benachrichtigungTage !== undefined) updateData.benachrichtigungTage = data.benachrichtigungTage;
    if (data.emailBenachrichtigung !== undefined) updateData.emailBenachrichtigung = data.emailBenachrichtigung ? 1 : 0;

    const result = await this.db.update(bereitschaftsKonfiguration)
      .set(updateData)
      .where(eq(bereitschaftsKonfiguration.id, existingConfig.id))
      .returning();

    const updated = result[0];

    // Return mapped result
    return {
      id: updated.id,
      wechselTag: updated.wechselTag,
      wechselUhrzeit: updated.wechselUhrzeit,
      rotationAktiv: !!updated.rotationAktiv,
      benachrichtigungTage: updated.benachrichtigungTage,
      emailBenachrichtigung: !!updated.emailBenachrichtigung,
      createdAt: updated.createdAt,
      updatedAt: updated.updatedAt
    };
  }

  // Hilfsmethoden
  async getPersonenInZeitraum(von: Date, bis: Date) {
    // Konvertiere Dates zu ISO-Strings für DATETIME-Vergleich in der Datenbank
    const vonString = von.toISOString();
    const bisString = bis.toISOString();

    const wochen = await this.db.select().from(bereitschaftsWochen)
      .where(and(
        eq(bereitschaftsWochen.aktiv, true),
        or(
          and(
            lte(bereitschaftsWochen.von, bisString),
            gte(bereitschaftsWochen.bis, vonString)
          )
        )
      ))
      .orderBy(asc(bereitschaftsWochen.von));

    // Fetch person data for each woche
    const wochenWithPerson = await Promise.all(
      wochen.map(async (woche) => {
        const person = await this.db.select().from(bereitschaftsPersonen)
          .where(eq(bereitschaftsPersonen.id, woche.personId))
          .limit(1);

        return { ...woche, person: person[0] || null };
      })
    );

    return wochenWithPerson;
  }

  async validateWochenplan(wochen: BereitschaftsWocheData[]) {
    const errors: string[] = [];

    // Prüfe auf Überschneidungen
    for (let i = 0; i < wochen.length; i++) {
      for (let j = i + 1; j < wochen.length; j++) {
        const woche1 = wochen[i];
        const woche2 = wochen[j];

        if (woche1.von < woche2.bis && woche1.bis > woche2.von) {
          errors.push(`Überschneidung zwischen Woche ${i + 1} und ${j + 1}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const bereitschaftsRepository = new BereitschaftsRepository();