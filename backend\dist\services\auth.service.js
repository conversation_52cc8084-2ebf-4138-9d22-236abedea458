"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jwt = __importStar(require("jsonwebtoken"));
const user_repository_1 = require("../repositories/user.repository");
class AuthService {
    constructor() {
        this.userRepository = new user_repository_1.UserRepository();
    }
    async register(email, username, firstName, lastName, password) {
        // Check if user already exists by email or username
        const existingUser = await this.userRepository.findByEmailOrUsername(email, username);
        if (existingUser) {
            if (existingUser.email === email) {
                throw {
                    code: 'USER_EXISTS',
                    message: 'Ein Benutzer mit dieser E-Mail-Adresse existiert bereits.'
                };
            }
            if (existingUser.username === username) {
                throw {
                    code: 'USER_EXISTS',
                    message: 'Ein Benutzer mit diesem Benutzernamen existiert bereits.'
                };
            }
        }
        // Hash the password
        const saltRounds = 12;
        const passwordHash = await bcryptjs_1.default.hash(password, saltRounds);
        // Create the user
        const newUser = await this.userRepository.createUser({
            email,
            username,
            firstName, // Vorname
            lastName, // Nachname
            passwordHash: passwordHash // Gehashtes Passwort
        });
        return newUser;
    }
    async login(username, password) {
        // Find user by username
        const user = await this.userRepository.findByUsername(username);
        if (!user) {
            throw {
                code: 'INVALID_CREDENTIALS',
                message: 'Ungültige Anmeldedaten.'
            };
        }
        // Compare password with stored hash
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.passwordHash);
        if (!isPasswordValid) {
            throw {
                code: 'INVALID_CREDENTIALS',
                message: 'Ungültige Anmeldedaten.'
            };
        }
        // Konvertiere DB-Rolle zu Frontend-kompatiblen deutschen Rollen
        const dbRole = user.role || 'user';
        const roleMapping = {
            'admin': 'Administrator',
            'administrator': 'Administrator',
            'user': 'Benutzer',
            'guest': 'Besucher',
            'visitor': 'Besucher'
        };
        const mappedRole = roleMapping[dbRole.toLowerCase()] || 'Benutzer';
        const userRoles = [mappedRole];
        console.log('🔄 Role mapping - DB role:', dbRole);
        console.log('🔄 Role mapping - Mapped to German:', mappedRole);
        console.log('🔄 Role mapping - Final roles array:', userRoles);
        // Create JWT token
        const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
        const token = jwt.sign({
            userId: user.id,
            username: user.username,
            roles: userRoles // Rollen in den Token aufnehmen
        }, jwtSecret, { expiresIn: '15m' });
        return {
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                firstName: user.firstName, // Vorname im User-Objekt zurückgeben
                lastName: user.lastName, // Nachname im User-Objekt zurückgeben
                roles: userRoles // Send as array to frontend
            }
        };
    }
    async refreshToken(currentToken) {
        try {
            const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
            // Verify the current token (even if expired, we want to check if it's valid structure)
            let decoded;
            try {
                decoded = jwt.verify(currentToken, jwtSecret, { ignoreExpiration: true });
            }
            catch (error) {
                throw {
                    code: 'INVALID_TOKEN',
                    message: 'Ungültiger Token.'
                };
            }
            // Check if token is not too old (max 1 hour after expiration for refresh)
            const now = Math.floor(Date.now() / 1000);
            const tokenAge = now - decoded.exp;
            if (tokenAge > 3600) { // 1 hour
                throw {
                    code: 'TOKEN_TOO_OLD',
                    message: 'Token ist zu alt für eine Aktualisierung.'
                };
            }
            // Get fresh user data to ensure user still exists and roles are current
            const user = await this.userRepository.findByUsername(decoded.username);
            if (!user) {
                throw {
                    code: 'USER_NOT_FOUND',
                    message: 'Benutzer nicht gefunden.'
                };
            }
            // Konvertiere DB-Rolle zu Frontend-kompatiblen deutschen Rollen
            const dbRole = user.role || 'user';
            const roleMapping = {
                'admin': 'Administrator',
                'administrator': 'Administrator',
                'user': 'Benutzer',
                'guest': 'Besucher',
                'visitor': 'Besucher'
            };
            const mappedRole = roleMapping[dbRole.toLowerCase()] || 'Benutzer';
            const userRoles = [mappedRole];
            console.log('🔄 RefreshToken - Role mapping - DB role:', dbRole);
            console.log('🔄 RefreshToken - Role mapping - Mapped to German:', mappedRole);
            console.log('🔄 RefreshToken - Role mapping - Final roles array:', userRoles);
            // Generate new token
            const newToken = jwt.sign({
                userId: user.id,
                username: user.username,
                roles: userRoles
            }, jwtSecret, { expiresIn: '15m' });
            return {
                token: newToken,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    roles: userRoles
                }
            };
        }
        catch (error) {
            console.error('[AUTH-REFRESH-ERROR]', error);
            throw error;
        }
    }
}
exports.AuthService = AuthService;
