export interface RunbookStep {
  id: string;
  description: string;
  order: number;
}

export interface Runbook {
  id: number;
  title: string;
  content?: string; // Markdown content (optional for backwards compatibility)
  overallDescription?: string; // For step mode
  steps?: RunbookStep[]; // New step-based structure
  affected_systems?: string[];
  category?: string[]; // Changed from tags to category
  created_at: string;
  updated_at: string;
}

export interface RunbookCreateData {
  title: string;
  content?: string;
  overallDescription?: string;
  steps?: RunbookStep[];
  affected_systems?: string[];
  category?: string[]; // Changed from tags to category
}

export interface RunbookUpdateData extends Partial<RunbookCreateData> {}
