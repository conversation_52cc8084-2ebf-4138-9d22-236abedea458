import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, Phone, ChevronLeft, ChevronRight, Settings, RefreshCw } from 'lucide-react';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import { BereitschaftsWoche, BereitschaftsPerson } from '@/types/bereitschafts';

// Types sind jetzt aus @/types/bereitschafts importiert

interface BereitschaftsPlanProps {
  refreshKey?: number;
  onConfigureClick?: () => void;
}

// Beispieldaten entfernt - verwende echte Datenbank-Daten

export const BereitschaftsPlan: React.FC<BereitschaftsPlanProps> = ({
  refreshKey,
  onConfigureClick
}) => {
  const [loading, setLoading] = useState(false);
  const [aktuelleBereitschaft, setAktuelleBereitschaft] = useState<BereitschaftsWoche | null>(null);
  const [bereitschaftsWochen, setBereitschaftsWochen] = useState<BereitschaftsWoche[]>([]);

  useEffect(() => {
    loadBereitschaftsdaten();
  }, [refreshKey]);

  const loadBereitschaftsdaten = async () => {
    try {
      setLoading(true);
      console.log('🔄 Lade Bereitschaftsdaten aus der Datenbank...');

      // Lade echte Daten aus der Datenbank
      const [aktuelleBereitschaftData, wochenplanData] = await Promise.all([
        bereitschaftsService.getAktuelleBereitschaft(),
        bereitschaftsService.getWochenplan('2024-01-01', 104) // Lade 104 Wochen ab 2024, um alle zukünftigen Wochen zu erfassen
      ]);

      console.log('✅ Aktuelle Bereitschaft:', aktuelleBereitschaftData);
      console.log('✅ Wochenplan:', wochenplanData);

      setAktuelleBereitschaft(aktuelleBereitschaftData);
      setBereitschaftsWochen(wochenplanData);

    } catch (error) {
      console.error('❌ Fehler beim Laden der Bereitschaftsdaten:', error);
      // Bei Fehlern verwende leere Arrays
      setAktuelleBereitschaft(null);
      setBereitschaftsWochen([]);
    } finally {
      setLoading(false);
    }
  };

  // Hilfsfunktion zur Bereinigung von fehlerhaften Datumsformaten
  const cleanDateString = (dateStr: string | undefined | null): string | null => {
    if (!dateStr) return null;
    
    try {
      // Spezielles Problem: "2025-07-13T22:00:00T00:00:00.000ZZ"
      // Entferne den doppelten Zeitanteil
      let cleaned = dateStr;
      
      // Muster: YYYY-MM-DDTHH:MM:SST00:00:00.000ZZ
      const doubleTimePattern = /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})T00:00:00\.000ZZ$/;
      if (doubleTimePattern.test(cleaned)) {
        cleaned = cleaned.replace(doubleTimePattern, '$1Z');
      }
      
      // Muster: YYYY-MM-DDTHH:MM:SST23:59:59.999ZZ
      const doubleTimePattern2 = /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})T23:59:59\.999ZZ$/;
      if (doubleTimePattern2.test(cleaned)) {
        cleaned = cleaned.replace(doubleTimePattern2, '$1Z');
      }
      
      // Muster: YYYY-MM-DDTHH:MM:SST00:00:00.000Z
      const doubleTimePattern3 = /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})T00:00:00\.000Z$/;
      if (doubleTimePattern3.test(cleaned)) {
        cleaned = cleaned.replace(doubleTimePattern3, '$1Z');
      }
      
      // Fallback für andere fehlerhafte Formate
      if (cleaned.endsWith('.000ZZ')) {
        cleaned = cleaned.replace('.000ZZ', 'Z');
      } else if (cleaned.endsWith('.000Z')) {
        cleaned = cleaned.replace('.000Z', 'Z');
      } else if (cleaned.endsWith('.999ZZ')) {
        cleaned = cleaned.replace('.999ZZ', 'Z');
      } else if (cleaned.endsWith('.999Z')) {
        cleaned = cleaned.replace('.999Z', 'Z');
      }
      
      // Testen, ob das bereinigte Datum gültig ist
      const testDate = new Date(cleaned);
      if (isNaN(testDate.getTime())) {
        console.warn('Konnte Datum nicht bereinigen:', dateStr, '→', cleaned);
        return null;
      }
      
      return cleaned;
    } catch (error) {
      console.error('Fehler bei der Datumsbereinigung:', dateStr, error);
      return null;
    }
  };

  const formatDate = (dateString: string) => {
    const cleanedDateStr = cleanDateString(dateString);
    if (!cleanedDateStr) {
      return 'Ungültiges Datum';
    }
    
    const date = new Date(cleanedDateStr);
    return date.toLocaleDateString('de-DE', {
      weekday: 'long',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getKalenderwoche = (dateString: string) => {
    const cleanedDateStr = cleanDateString(dateString);
    if (!cleanedDateStr) {
      return 'N/A';
    }
    
    const date = new Date(cleanedDateStr);
    const startOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - startOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Bereitschaftsplan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="ml-2">Bereitschaftsplan wird geladen...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="min-h-[700px] overflow-hidden border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Bereitschaftsplan
          </CardTitle>
          <div className="flex items-center gap-2">
            {onConfigureClick && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('🔧 Konfiguration Button geklickt');
                  onConfigureClick();
                }}
                className="text-blue-600 border-blue-300 hover:bg-blue-50"
                title="Bereitschaftsplan konfigurieren"
              >
                <Settings className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadBereitschaftsdaten()}
              disabled={loading}
              title="Bereitschaftsdaten neu laden"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Aktuelle Bereitschaft Highlight */}
        {aktuelleBereitschaft && aktuelleBereitschaft.person ? (
          <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-green-600" />
              <span className="font-semibold text-green-800">Aktuelle Bereitschaft</span>
              <Badge variant="default" className="bg-green-600">
                AKTIV
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-lg text-gray-900">
                  {aktuelleBereitschaft.person.name}
                </h4>
                <p className="text-sm text-gray-600 mb-2">
                  {aktuelleBereitschaft.person.abteilung}
                </p>
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <a
                    href={`tel:${aktuelleBereitschaft.person.telefon}`}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {aktuelleBereitschaft.person.telefon}
                  </a>
                </div>
              </div>

              <div className="text-right">
                <div className="text-sm text-gray-600">
                  Bereitschaft bis:
                </div>
                <div className="font-semibold text-gray-900">
                  {formatDate(aktuelleBereitschaft.bis)}
                </div>
                <div className="text-sm text-gray-600">
                  um 08:00 Uhr
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">Keine aktuelle Bereitschaft</span>
            </div>
            <p className="text-sm text-gray-500">
              Es ist aktuell keine Bereitschaftsperson zugewiesen. Bitte konfigurieren Sie den Bereitschaftsplan.
            </p>
          </div>
        )}

        {/* Wochenplan */}
        <div className="space-y-2">
          <h4 className="font-semibold text-gray-900 flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Wochenplan
          </h4>

          {bereitschaftsWochen.length > 0 ? (
            <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
              {bereitschaftsWochen
                .sort((a, b) => {
                  // Aktuelle Woche (die mit AKTIV Badge) zuerst
                  const istAktuelleWocheA = aktuelleBereitschaft && aktuelleBereitschaft.id === a.id;
                  const istAktuelleWocheB = aktuelleBereitschaft && aktuelleBereitschaft.id === b.id;

                  if (istAktuelleWocheA && !istAktuelleWocheB) return -1;
                  if (!istAktuelleWocheA && istAktuelleWocheB) return 1;

                  // Dann nach Datum sortieren (chronologisch)
                  const dateA = cleanDateString(a.von);
                  const dateB = cleanDateString(b.von);
                  if (!dateA || !dateB) return 0;

                  const timeA = new Date(dateA).getTime();
                  const timeB = new Date(dateB).getTime();

                  // Wenn beide in der Vergangenheit liegen, umgekehrte Reihenfolge (neueste zuerst)
                  const jetzt = new Date().getTime();
                  if (timeA < jetzt && timeB < jetzt) {
                    return timeB - timeA; // Neueste vergangene Woche zuerst
                  }

                  // Ansonsten chronologisch (älteste zuerst)
                  return timeA - timeB;
                })
                .map((woche, index) => {
                const istAktuelleWoche = aktuelleBereitschaft && aktuelleBereitschaft.id === woche.id;
                
                return (
                  <div
                    key={woche.id}
                    className={`p-3 rounded-lg border transition-all ${
                      istAktuelleWoche
                        ? 'bg-green-50 border-green-300 shadow-sm'
                        : 'bg-white border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">
                            {woche.person?.name || 'Unbekannte Person'}
                          </span>
                          {istAktuelleWoche && (
                            <Badge variant="default" className="bg-green-600 text-xs">
                              AKTIV
                            </Badge>
                          )}
                          {woche.hatAusnahme && (
                            <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
                              AUSNAHME
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          {woche.person?.abteilung || 'Keine Abteilung'}
                        </div>
                        {woche.notiz && (
                          <div className="text-xs text-gray-500 mt-1">
                            {woche.notiz}
                          </div>
                        )}
                      </div>

                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900">
                          KW {getKalenderwoche(woche.wochenStart)}
                        </div>
                        <div className="text-xs text-gray-600">
                          {(() => {
                            const cleanedVonStr = cleanDateString(woche.von);
                            const cleanedBisStr = cleanDateString(woche.bis);
                            
                            const vonStr = cleanedVonStr
                              ? new Date(cleanedVonStr).toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' })
                              : 'N/A';
                            const bisStr = cleanedBisStr
                              ? new Date(cleanedBisStr).toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' })
                              : 'N/A';
                              
                            return `${vonStr} - ${bisStr}`;
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
              <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-1">Kein Wochenplan vorhanden</p>
              <p className="text-xs text-gray-500">
                Erstellen Sie einen Wochenplan über die Konfiguration, um Bereitschaften zu planen.
              </p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Legende als separater Footer-Bereich */}
      <div className="px-6 pb-6 pt-2 border-t border-gray-200">
        <div className="text-xs text-gray-600">
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
              <span>Aktuelle Bereitschaft</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded"></div>
              <span>Diese Woche</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded"></div>
              <span>Ausnahme</span>
            </div>
          </div>
          <div className="mt-1 text-gray-500">
            Wechsel erfolgt jeden Freitag um 08:00 Uhr
          </div>
        </div>
      </div>
    </Card>
  );
};