"use strict";
/**
 * VectorDatabaseService - Vector Storage and Similarity Search mit libsql
 *
 * Handles vector embeddings storage, retrieval, and similarity search
 * using separate SQLite database with binary vector storage and cosine similarity calculations
 * <PERSON><PERSON><PERSON><PERSON> von better-sqlite3 auf libsql für bessere Node.js v23 Kompatibilität
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDatabaseService = void 0;
const client_1 = require("@libsql/client");
const uuid_1 = require("uuid");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const rag_types_1 = require("../../types/rag.types");
class VectorDatabaseService {
    constructor(dbPath) {
        // Use PostgreSQL instead of SQLite
        this.dbPath = dbPath || 'postgresql://leitstand_dashboard:dashboard_password@localhost:5434/leitstand_dashboard';
        // Initialize database asynchronously
        this.initializeDatabase().catch(error => {
            console.error('Failed to initialize VectorDatabaseService:', error);
        });
    }
    /**
     * Initialize separate RAG SQLite database
     */
    async initializeDatabase() {
        try {
            // Ensure directory exists
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }
            // Initialize libsql client
            this.client = (0, client_1.createClient)({ url: `file:${this.dbPath}` });
            // Set SQLite pragmas for performance
            await this.client.execute('PRAGMA journal_mode = WAL');
            await this.client.execute('PRAGMA synchronous = NORMAL');
            await this.client.execute('PRAGMA cache_size = 1000');
            await this.client.execute('PRAGMA temp_store = memory');
            // Read and execute schema
            const schemaPath = path.join(process.cwd(), 'database', 'init-rag-database.sql');
            if (fs.existsSync(schemaPath)) {
                const schema = fs.readFileSync(schemaPath, 'utf-8');
                // Split schema into individual statements and execute them
                const statements = schema.split(';').filter(stmt => stmt.trim());
                for (const statement of statements) {
                    if (statement.trim()) {
                        await this.client.execute(statement.trim());
                    }
                }
            }
            else {
                console.warn('RAG database schema file not found, creating basic tables');
                await this.createBasicTables();
            }
            console.log(`RAG Vector Database initialized: ${this.dbPath}`);
        }
        catch (error) {
            console.error('Failed to initialize RAG database:', error);
            throw error;
        }
    }
    /**
     * Create basic tables if schema file is missing
     */
    async createBasicTables() {
        const createTables = [
            `CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        source TEXT NOT NULL,
        source_path TEXT,
        content_type TEXT DEFAULT 'text/plain',
        language TEXT DEFAULT 'de',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        indexed_at DATETIME,
        status TEXT DEFAULT 'pending'
      )`,
            `CREATE TABLE IF NOT EXISTS chunks (
        id TEXT PRIMARY KEY,
        document_id TEXT NOT NULL,
        content TEXT NOT NULL,
        chunk_index INTEGER NOT NULL,
        token_count INTEGER,
        start_position INTEGER,
        end_position INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
      )`,
            `CREATE TABLE IF NOT EXISTS embeddings (
        id TEXT PRIMARY KEY,
        chunk_id TEXT NOT NULL,
        vector BLOB NOT NULL,
        model_name TEXT NOT NULL DEFAULT 'text-embedding-3-small',
        dimension INTEGER NOT NULL DEFAULT 1536,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chunk_id) REFERENCES chunks(id) ON DELETE CASCADE
      )`,
            `CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )`,
            `CREATE INDEX IF NOT EXISTS idx_documents_source ON documents(source)`,
            `CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status)`,
            `CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id)`,
            `CREATE INDEX IF NOT EXISTS idx_embeddings_chunk_id ON embeddings(chunk_id)`
        ];
        // Execute all table creation statements
        for (const statement of createTables) {
            await this.client.execute(statement);
        }
    }
    /**
     * Store vector embedding for a document chunk
     */
    async storeEmbedding(chunkId, embedding, model = 'text-embedding-3-small') {
        try {
            // Validate vector dimensions (flexible for different models)
            if (embedding.length < 256 || embedding.length > 2048) {
                throw new Error(`Invalid vector dimensions: got ${embedding.length}, expected between 256-2048`);
            }
            const id = (0, uuid_1.v4)();
            const embeddingBuffer = Buffer.from(embedding.buffer);
            const now = new Date().toISOString();
            await this.client.execute({
                sql: `INSERT INTO embeddings (id, chunk_id, vector, model_name, dimension, created_at)
              VALUES (?, ?, ?, ?, ?, ?)`,
                args: [id, chunkId, embeddingBuffer, model, embedding.length, now]
            });
            return {
                id,
                chunkId,
                model,
                embedding,
                dimensions: embedding.length,
                createdAt: new Date(now)
            };
        }
        catch (error) {
            console.error('Error storing vector embedding:', error);
            throw {
                code: rag_types_1.RAGErrorCode.VECTOR_STORAGE_FAILED,
                message: 'Failed to store vector embedding',
                details: { chunkId, model, error: error.message },
                timestamp: new Date()
            };
        }
    }
    /**
     * Store multiple embeddings in batch
     */
    async storeBatchEmbeddings(embeddings) {
        const results = [];
        const batchSize = 100; // Process in batches to avoid memory issues
        for (let i = 0; i < embeddings.length; i += batchSize) {
            const batch = embeddings.slice(i, i + batchSize);
            const batchPromises = batch.map(({ chunkId, embedding, model }) => this.storeEmbedding(chunkId, embedding, model));
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            catch (error) {
                console.error(`Error in batch ${i / batchSize + 1}:`, error);
                // Continue with next batch
            }
        }
        return results;
    }
    /**
     * Perform similarity search using cosine similarity
     */
    async searchSimilar(queryEmbedding, options = {}) {
        const { categoryIds, limit = 10, threshold = 0.7, language = 'de' } = options;
        try {
            // Build SQL query to get embeddings with chunks and documents
            let query = `
        SELECT 
          e.id as embedding_id,
          e.vector,
          e.model_name,
          c.id as chunk_id,
          c.content as chunk_content,
          c.chunk_index,
          c.token_count,
          c.start_position,
          c.end_position,
          d.id as document_id,
          d.title as document_title,
          d.source,
          d.content_type,
          d.language,
          'default' as category_name
        FROM embeddings e
        JOIN chunks c ON e.chunk_id = c.id
        JOIN documents d ON c.document_id = d.id
        WHERE d.status = 'indexed' AND d.language = ?
      `;
            const params = [language];
            // Add category filter if specified (simplified for now)
            if (categoryIds && categoryIds.length > 0) {
                query += ` AND d.source IN (${categoryIds.map(() => '?').join(',')})`;
                params.push(...categoryIds);
            }
            const result = await this.client.execute({ sql: query, args: params });
            const rows = result.rows;
            // Calculate similarities
            const similarities = [];
            for (const row of rows) {
                // Deserialize vector from ArrayBuffer (libsql format)
                const vectorArrayBuffer = row.vector;
                if (!vectorArrayBuffer) {
                    console.warn('⚠️ [VECTOR-DB] Skipping row with missing vector data');
                    continue;
                }
                const vector = new Float32Array(vectorArrayBuffer);
                const similarity = this.cosineSimilarity(queryEmbedding, vector);
                if (similarity >= threshold) {
                    similarities.push({
                        row: row,
                        similarity
                    });
                }
            }
            // Sort by similarity (descending) and limit results
            similarities.sort((a, b) => b.similarity - a.similarity);
            const topResults = similarities.slice(0, limit);
            // Format results
            const results = topResults.map((result, index) => ({
                chunk: {
                    id: result.row.chunk_id,
                    documentId: result.row.document_id,
                    chunkIndex: result.row.chunk_index,
                    content: result.row.chunk_content,
                    tokenCount: result.row.token_count,
                    startPosition: result.row.start_position,
                    endPosition: result.row.end_position,
                    createdAt: new Date(),
                    document: {
                        id: result.row.document_id,
                        knowledgeBaseId: 1, // Simplified for now
                        title: result.row.document_title,
                        content: '', // Don't load full content for search results
                        contentType: result.row.content_type,
                        source: result.row.source,
                        language: result.row.language,
                        hash: '',
                        isActive: true,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        knowledgeBase: {
                            id: 1,
                            name: result.row.category_name,
                            description: '',
                            category: 'business',
                            isActive: true,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        }
                    }
                },
                similarity: result.similarity,
                rank: index + 1
            }));
            return results;
        }
        catch (error) {
            console.error('Error in similarity search:', error);
            throw {
                code: rag_types_1.RAGErrorCode.SIMILARITY_SEARCH_FAILED,
                message: 'Failed to perform similarity search',
                details: { error: error.message, options },
                timestamp: new Date()
            };
        }
    }
    /**
     * Get embedding by chunk ID
     */
    async getEmbeddingByChunkId(chunkId, model) {
        try {
            let query = 'SELECT * FROM embeddings WHERE chunk_id = ?';
            const params = [chunkId];
            if (model) {
                query += ' AND model_name = ?';
                params.push(model);
            }
            const result = await this.client.execute({ sql: query, args: params });
            const embedding = result.rows[0];
            if (!embedding) {
                return null;
            }
            return {
                id: embedding.id,
                chunkId: embedding.chunk_id,
                model: embedding.model_name,
                embedding: new Float32Array(embedding.vector.buffer),
                dimensions: embedding.dimension,
                createdAt: new Date(embedding.created_at)
            };
        }
        catch (error) {
            console.error('Error getting embedding by chunk ID:', error);
            return null;
        }
    }
    /**
     * Delete embedding by chunk ID
     */
    async deleteEmbedding(chunkId, model) {
        try {
            let query = 'DELETE FROM embeddings WHERE chunk_id = ?';
            const params = [chunkId];
            if (model) {
                query += ' AND model_name = ?';
                params.push(model);
            }
            await this.client.execute({ sql: query, args: params });
            return true;
        }
        catch (error) {
            console.error('Error deleting embedding:', error);
            return false;
        }
    }
    /**
     * Get vector database statistics
     */
    async getStatistics() {
        var _a, _b, _c, _d, _e, _f, _g;
        try {
            // Sichere Abfrage der Grundstatistiken mit libsql
            const totalEmbeddings = await this.client.execute('SELECT COUNT(*) as count FROM embeddings');
            const totalChunks = await this.client.execute('SELECT COUNT(*) as count FROM chunks');
            const totalDocuments = await this.client.execute({
                sql: 'SELECT COUNT(*) as count FROM documents WHERE status = ?',
                args: ['indexed']
            });
            // Sichere Abfrage der Kategorien mit Fallback
            let totalCategories;
            try {
                // Prüfe, ob categories-Tabelle existiert
                const tableExists = await this.client.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'");
                if (tableExists.rows.length > 0) {
                    totalCategories = await this.client.execute('SELECT COUNT(*) as count FROM categories');
                }
                else {
                    // Fallback: Verwende eindeutige document_ids als Knowledge Bases
                    totalCategories = await this.client.execute('SELECT COUNT(DISTINCT document_id) as count FROM chunks');
                }
            }
            catch (categoryError) {
                console.warn('Categories table not accessible, using document count as fallback:', categoryError);
                // Fallback: Verwende eindeutige document_ids als Knowledge Bases
                totalCategories = await this.client.execute('SELECT COUNT(DISTINCT document_id) as count FROM chunks');
            }
            // Sichere Abfrage der durchschnittlichen Chunk-Größe
            const avgChunkSize = await this.client.execute('SELECT AVG(token_count) as avg FROM chunks');
            // Schätze Speichergröße (durchschnittliche Embedding-Größe * 4 Bytes pro Float)
            const avgDimensions = await this.client.execute('SELECT AVG(dimension) as avg FROM embeddings');
            const storageSize = ((_a = totalEmbeddings.rows[0]) === null || _a === void 0 ? void 0 : _a.count) * (((_b = avgDimensions.rows[0]) === null || _b === void 0 ? void 0 : _b.avg) || 1024) * 4;
            return {
                totalEmbeddings: ((_c = totalEmbeddings.rows[0]) === null || _c === void 0 ? void 0 : _c.count) || 0,
                totalChunks: ((_d = totalChunks.rows[0]) === null || _d === void 0 ? void 0 : _d.count) || 0,
                totalDocuments: ((_e = totalDocuments.rows[0]) === null || _e === void 0 ? void 0 : _e.count) || 0,
                totalKnowledgeBases: ((_f = totalCategories.rows[0]) === null || _f === void 0 ? void 0 : _f.count) || 0,
                averageChunkSize: Math.round(((_g = avgChunkSize.rows[0]) === null || _g === void 0 ? void 0 : _g.avg) || 0),
                storageSize: storageSize || 0
            };
        }
        catch (error) {
            console.error('Error getting vector database statistics:', error);
            return {
                totalEmbeddings: 0,
                totalChunks: 0,
                totalDocuments: 0,
                totalKnowledgeBases: 0,
                averageChunkSize: 0,
                storageSize: 0
            };
        }
    }
    // Vector Operations Implementation
    /**
     * Calculate cosine similarity between two vectors
     */
    cosineSimilarity(a, b) {
        if (a.length !== b.length) {
            throw new Error('Vectors must have the same dimensions');
        }
        const dotProd = this.dotProduct(a, b);
        const magnitudeA = this.magnitude(a);
        const magnitudeB = this.magnitude(b);
        if (magnitudeA === 0 || magnitudeB === 0) {
            return 0;
        }
        return dotProd / (magnitudeA * magnitudeB);
    }
    /**
     * Calculate dot product of two vectors
     */
    dotProduct(a, b) {
        let sum = 0;
        for (let i = 0; i < a.length; i++) {
            sum += a[i] * b[i];
        }
        return sum;
    }
    /**
     * Calculate magnitude (length) of a vector
     */
    magnitude(vector) {
        let sum = 0;
        for (let i = 0; i < vector.length; i++) {
            sum += vector[i] * vector[i];
        }
        return Math.sqrt(sum);
    }
    /**
     * Normalize a vector to unit length
     */
    normalize(vector) {
        const mag = this.magnitude(vector);
        if (mag === 0) {
            return new Float32Array(vector.length);
        }
        const normalized = new Float32Array(vector.length);
        for (let i = 0; i < vector.length; i++) {
            normalized[i] = vector[i] / mag;
        }
        return normalized;
    }
    /**
     * Cleanup resources - libsql client cleanup
     */
    async disconnect() {
        if (this.client) {
            await this.client.close();
        }
    }
}
exports.VectorDatabaseService = VectorDatabaseService;
exports.default = VectorDatabaseService;
