/**
 * RAG Management Route
 */

import { createRoute } from '@tanstack/react-router';
import { lazy } from 'react';
import { RootRoute } from './__root';

// Lazy load the RAG management component
const RAGManagementPage = lazy(() => import('@/modules/ai/pages/RAGManagementPage'));

export const Route = createRoute({
  getParentRoute: () => RootRoute,
  path: '/rag',
  component: () => <RAGManagementPage />,
});