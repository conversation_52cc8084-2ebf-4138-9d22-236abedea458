"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const router = (0, express_1.Router)();
const controller = new user_controller_1.UserController();
// Rate limiting for user endpoints (stricter limits for auth-related endpoints)
const userLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.NODE_ENV === "development" ? 100 : 50, // Stricter limit for user operations
    message: "Too many requests to user API from this IP",
    standardHeaders: true,
    legacyHeaders: false,
});
// Apply rate limiting to all routes
router.use(userLimiter);
// User search routes
router.get('/search', controller.findByEmailOrUsername.bind(controller));
router.get('/email/:email', controller.findByEmail.bind(controller));
router.get('/username/:username', controller.findByUsername.bind(controller));
router.get('/:id', controller.findById.bind(controller));
// User management routes
router.post('/', controller.createUser.bind(controller));
router.get('/all', controller.getAllUsers.bind(controller));
router.get('/role/:roleName', controller.getUsersByRole.bind(controller));
exports.default = router;
