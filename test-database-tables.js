/**
 * Test-Script für die Datenbank-Tabellen
 * Überprüft direkt, welche Tabellen in der Datenbank existieren
 */

const { Client } = require('pg');

// Datenbankkonfiguration aus der Drizzle-Konfiguration
const dbConfig = {
  host: 'localhost',
  port: 5434, // Der Port aus der MCP-Server-Konfiguration
  user: 'leitstand_dashboard',
  password: 'dashboard_password',
  database: 'leitstand_dashboard',
  ssl: false
};

async function testDatabaseTables() {
  console.log('🗄️ Teste Datenbank-Tabellen...\n');

  const client = new Client(dbConfig);

  try {
    console.log('🔌 Verbinde mit Datenbank...');
    await client.connect();
    console.log('✅ Verbindung erfolgreich hergestellt\n');

    // 1. Alle Tabellen in der Datenbank anzeigen
    console.log('📋 Alle Tabellen in der Datenbank:');
    const tablesResult = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    if (tablesResult.rows.length === 0) {
      console.log('❌ Keine Tabellen gefunden!');
    } else {
      console.log('📊 Gefundene Tabellen:');
      tablesResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.table_name}`);
      });
    }

    console.log('\n');

    // 2. Speziell nach bereitschafts-Tabellen suchen
    console.log('🔍 Suche nach bereitschafts-Tabellen:');
    const bereitschaftsTablesResult = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name LIKE '%bereitschafts%'
      ORDER BY table_name;
    `);

    if (bereitschaftsTablesResult.rows.length === 0) {
      console.log('❌ Keine bereitschafts-Tabellen gefunden!');
    } else {
      console.log('📋 bereitschafts-Tabellen:');
      bereitschaftsTablesResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.table_name}`);
      });
    }

    console.log('\n');

    // 3. Teste spezifische bereitschafts-Tabellen
    const bereitschaftsTables = [
      'bereitschafts_personen',
      'bereitschafts_wochen',
      'bereitschafts_ausnahmen',
      'bereitschafts_konfiguration'
    ];

    for (const tableName of bereitschaftsTables) {
      console.log(`🔍 Teste Tabelle: ${tableName}`);
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${tableName} LIMIT 1`);
        console.log(`  ✅ Tabelle ${tableName} existiert (${result.rows[0].count} Datensätze)`);
      } catch (error) {
        console.log(`  ❌ Tabelle ${tableName} existiert nicht: ${error.message}`);
      }
    }

    console.log('\n');

    // 4. Zeige Struktur der bereitschafts_personen Tabelle falls vorhanden
    try {
      console.log('📋 Struktur der bereitschafts_personen Tabelle:');
      const structureResult = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'bereitschafts_personen'
        ORDER BY ordinal_position;
      `);

      if (structureResult.rows.length > 0) {
        console.log('  Spalten:');
        structureResult.rows.forEach(row => {
          console.log(`    - ${row.column_name} (${row.data_type}, nullable: ${row.is_nullable})`);
        });
      } else {
        console.log('  Keine Spalten gefunden');
      }
    } catch (error) {
      console.log(`  Fehler beim Abrufen der Tabellenstruktur: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Fehler beim Testen der Datenbank:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await client.end();
    console.log('\n🔌 Datenbankverbindung geschlossen');
  }
}

// Führe Tests aus
testDatabaseTables();