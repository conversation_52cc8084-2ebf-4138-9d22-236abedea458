/**
 * Einfacher bereitschafts API Test
 * Testet die API direkt ohne das komplexe Backend-System
 */

const express = require('express');
const cors = require('cors');
const { Client } = require('pg');

// Datenbankkonfiguration
const dbConfig = {
  host: 'localhost',
  port: 5434,
  user: 'leitstand_dashboard',
  password: 'dashboard_password',
  database: 'leitstand_dashboard',
  ssl: false
};

const app = express();
const port = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Datenbank-Client
const dbClient = new Client(dbConfig);

// Endpunkte
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Simple bereitschafts API läuft!',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/bereitschafts/personen', async (req, res) => {
  try {
    const result = await dbClient.query(`
      SELECT id, name, telefon, email, abteilung, aktiv, reihenfolge, created_at, updated_at
      FROM bereitschafts_personen
      WHERE aktiv = true
      ORDER BY reihenfolge ASC
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Fehler beim Laden der Personen:', error);
    res.status(500).json({
      error: 'Personen konnten nicht geladen werden',
      details: error.message
    });
  }
});

app.get('/api/bereitschafts/wochenplan', async (req, res) => {
  try {
    const result = await dbClient.query(`
      SELECT id, person_id, wochen_start, wochen_ende, von, bis, aktiv, notiz, created_at, updated_at
      FROM bereitschafts_wochen
      WHERE aktiv = true
      ORDER BY wochen_start ASC
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Fehler beim Laden des Wochenplans:', error);
    res.status(500).json({
      error: 'Wochenplan konnte nicht geladen werden',
      details: error.message
    });
  }
});

app.get('/api/bereitschafts/aktuelle-bereitschaft', async (req, res) => {
  try {
    const now = new Date().toISOString();
    const result = await dbClient.query(`
      SELECT id, person_id, wochen_start, wochen_ende, von, bis, aktiv, notiz, created_at, updated_at
      FROM bereitschafts_wochen
      WHERE aktiv = true
      AND von <= $1
      AND bis > $1
      LIMIT 1
    `, [now]);

    res.json(result.rows[0] || null);
  } catch (error) {
    console.error('Fehler beim Laden der aktuellen Bereitschaft:', error);
    res.status(500).json({
      error: 'Aktuelle Bereitschaft konnte nicht geladen werden',
      details: error.message
    });
  }
});

app.get('/api/bereitschafts/konfiguration', async (req, res) => {
  try {
    const result = await dbClient.query(`
      SELECT id, wechsel_tag, wechsel_uhrzeit, rotation_aktiv, benachrichtigung_tage, email_benachrichtigung, created_at, updated_at
      FROM bereitschafts_konfiguration
      ORDER BY created_at DESC
      LIMIT 1
    `);

    res.json(result.rows[0] || null);
  } catch (error) {
    console.error('Fehler beim Laden der Konfiguration:', error);
    res.status(500).json({
      error: 'Konfiguration konnte nicht geladen werden',
      details: error.message
    });
  }
});

// Server starten
async function startServer() {
  try {
    console.log('🔌 Verbinde mit Datenbank...');
    await dbClient.connect();
    console.log('✅ Datenbankverbindung hergestellt');

    app.listen(port, () => {
      console.log(`🚀 Einfacher bereitschafts API Server läuft auf Port ${port}`);
      console.log(`📋 Teste Endpunkte:`);
      console.log(`   Health: http://localhost:${port}/api/health`);
      console.log(`   Personen: http://localhost:${port}/api/bereitschafts/personen`);
      console.log(`   Wochenplan: http://localhost:${port}/api/bereitschafts/wochenplan`);
      console.log(`   Aktuelle: http://localhost:${port}/api/bereitschafts/aktuelle-bereitschaft`);
      console.log(`   Konfiguration: http://localhost:${port}/api/bereitschafts/konfiguration`);
    });
  } catch (error) {
    console.error('❌ Fehler beim Starten des Servers:', error);
    process.exit(1);
  }
}

startServer();