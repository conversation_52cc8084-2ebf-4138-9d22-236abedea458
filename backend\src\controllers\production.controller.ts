import { Request, Response } from "express";
import { ProductionRepositoryImpl } from "../repositories/production.repository";

export class ProductionController {
  private repository: ProductionRepositoryImpl;

  constructor() {
    this.repository = new ProductionRepositoryImpl();
  }

  /**
   * Get schnitte data
   * GET /api/production/schnitte?startDate=2024-01-01&endDate=2024-01-31
   */
  async getSchnitteData(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      const filter = startDate || endDate ? { startDate: startDate as string, endDate: endDate as string } : undefined;

      const data = await this.repository.schnitte.getAll(filter);

      res.json({
        success: true,
        data,
        count: data.length,
        filters: filter
      });
    } catch (error) {
      console.error('Error fetching schnitte data:', error);
      res.status(500).json({
        success: false,
        error: '<PERSON><PERSON> beim Abrufen der Schnittedaten',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get schnitte data grouped by date
   * GET /api/production/schnitte/grouped
   */
  async getSchnitteGroupedByDate(req: Request, res: Response) {
    try {
      const data = await this.repository.schnitte.getGroupedByDate();

      res.json({
        success: true,
        data,
        count: Object.keys(data).length
      });
    } catch (error) {
      console.error('Error fetching grouped schnitte data:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der gruppierten Schnittedaten',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get schnitte daily sums
   * GET /api/production/schnitte/sums
   */
  async getSchnitteDailySums(req: Request, res: Response) {
    try {
      const data = await this.repository.schnitte.getDailySums();

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('Error fetching schnitte daily sums:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der täglichen Schnittsummen',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get machine performance data
   * GET /api/production/schnitte/performance
   */
  async getMachinePerformance(req: Request, res: Response) {
    try {
      const data = await this.repository.schnitte.getMachinePerformance();

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('Error fetching machine performance:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Maschinen-Performance',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get machine efficiency data
   * GET /api/production/efficiency?machineId=M1&minEfficiency=80
   */
  async getMachineEfficiency(req: Request, res: Response) {
    try {
      const { machineId, productionType, minEfficiency, maxEfficiency } = req.query;
      const filter = machineId || productionType || minEfficiency || maxEfficiency ?
        { machineId: machineId as string, productionType: productionType as any, minEfficiency: minEfficiency ? parseFloat(minEfficiency as string) : undefined, maxEfficiency: maxEfficiency ? parseFloat(maxEfficiency as string) : undefined } : undefined;

      const data = await this.repository.maschinenEfficiency.getAll(filter);

      res.json({
        success: true,
        data,
        count: data.length,
        filters: filter
      });
    } catch (error) {
      console.error('Error fetching machine efficiency:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Maschinen-Effizienz',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get top performing machines
   * GET /api/production/efficiency/top?limit=5
   */
  async getTopPerformers(req: Request, res: Response) {
    try {
      const { limit } = req.query;
      const limitNum = limit ? parseInt(limit as string) : 5;

      const data = await this.repository.maschinenEfficiency.getTopPerformers(limitNum);

      res.json({
        success: true,
        data,
        count: data.length,
        limit: limitNum
      });
    } catch (error) {
      console.error('Error fetching top performers:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Top-Performers',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get low performing machines
   * GET /api/production/efficiency/low?threshold=50
   */
  async getLowPerformers(req: Request, res: Response) {
    try {
      const { threshold } = req.query;
      const thresholdNum = threshold ? parseFloat(threshold as string) : 50;

      const data = await this.repository.maschinenEfficiency.getLowPerformers(thresholdNum);

      res.json({
        success: true,
        data,
        count: data.length,
        threshold: thresholdNum
      });
    } catch (error) {
      console.error('Error fetching low performers:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Low-Performers',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get average efficiency stats
   * GET /api/production/efficiency/average
   */
  async getAverageEfficiency(req: Request, res: Response) {
    try {
      const data = await this.repository.maschinenEfficiency.getAverageEfficiency();

      res.json({
        success: true,
        data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching average efficiency:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der durchschnittlichen Effizienz',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get ablaengerei data
   * GET /api/production/ablaengerei?startDate=2024-01-01&endDate=2024-01-31
   */
  async getAblaengereiData(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      const filter = startDate || endDate ? { startDate: startDate as string, endDate: endDate as string } : undefined;

      const data = await this.repository.ablaengerei.getAll(filter);

      res.json({
        success: true,
        data,
        count: data.length,
        filters: filter
      });
    } catch (error) {
      console.error('Error fetching ablaengerei data:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Ablaengerei-Daten',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get ablaengerei daily stats
   * GET /api/production/ablaengerei/stats
   */
  async getAblaengereiDailyStats(req: Request, res: Response) {
    try {
      const data = await this.repository.ablaengerei.getDailyStats();

      res.json({
        success: true,
        data,
        count: data.length
      });
    } catch (error) {
      console.error('Error fetching ablaengerei daily stats:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der täglichen Ablaengerei-Statistiken',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get warehouse performance
   * GET /api/production/ablaengerei/warehouse-performance
   */
  async getWarehousePerformance(req: Request, res: Response) {
    try {
      const data = await this.repository.ablaengerei.getWarehousePerformance();

      res.json({
        success: true,
        data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching warehouse performance:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Warehouse-Performance',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get cutting chart data
   * GET /api/production/cutting-chart?startDate=2024-01-01&endDate=2024-01-31
   */
  async getCuttingChartData(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      const filter = startDate || endDate ? { startDate: startDate as string, endDate: endDate as string } : undefined;

      const data = await this.repository.cuttingChart.getAll(filter);

      res.json({
        success: true,
        data,
        count: data.length,
        filters: filter
      });
    } catch (error) {
      console.error('Error fetching cutting chart data:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Cutting-Chart-Daten',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get cutting trend analysis
   * GET /api/production/cutting-chart/trends?days=30
   */
  async getCuttingTrendAnalysis(req: Request, res: Response) {
    try {
      const { days } = req.query;
      const daysNum = days ? parseInt(days as string) : 30;

      const data = await this.repository.cuttingChart.getTrendAnalysis(daysNum);

      res.json({
        success: true,
        data,
        days: daysNum,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching cutting trend analysis:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Trend-Analyse',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get lager cuts data
   * GET /api/production/lager-cuts?startDate=2024-01-01&endDate=2024-01-31
   */
  async getLagerCutsData(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      const filter = startDate || endDate ? { startDate: startDate as string, endDate: endDate as string } : undefined;

      const data = await this.repository.lagerCuts.getAll(filter);

      res.json({
        success: true,
        data,
        count: data.length,
        filters: filter
      });
    } catch (error) {
      console.error('Error fetching lager cuts data:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Lager-Cuts-Daten',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get warehouse distribution analysis
   * GET /api/production/lager-cuts/distribution
   */
  async getWarehouseDistribution(req: Request, res: Response) {
    try {
      const data = await this.repository.lagerCuts.getDistributionAnalysis();

      res.json({
        success: true,
        data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching warehouse distribution:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Warehouse-Verteilung',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * Get overall production statistics
   * GET /api/production/stats
   */
  async getOverallProductionStats(req: Request, res: Response) {
    try {
      const data = await this.repository.getOverallProductionStats();

      res.json({
        success: true,
        data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching overall production stats:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Abrufen der Produktionsstatistiken',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}