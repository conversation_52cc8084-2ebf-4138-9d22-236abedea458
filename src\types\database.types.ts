/**
 * Datenbanktypen und Interfaces für das SFM Dashboard
 * 
 * Diese Datei enthält alle TypeScript-Interfaces und Typen für die Datenbankoperationen.
 * Ausgelagert aus database.ts für bessere Wartbarkeit und Modularität.
 */

// Basis-Interfaces für Datenbankoperationen
export interface DatabaseRow {
  [key: string]: unknown;
}

// ===== ABLÄNGEREI INTERFACES =====

/**
 * Ablängerei-Datensatz aus der Ablaengerei-Tabelle
 */
export interface AblaengereiRecord {
  id: number;
  datum: string;
  cutLagerK220: number;
  cutLagerR220: number;
  lagerCut220: number;
  cutLagerK240: number;
  cutLagerR240: number;
  lagerCut240: number;
  cutTT: number;
  cutTR: number;
  cutRR: number;
  cutGesamt: number;
  pickCut: number;
  cutLager200: number;
  cutLagerK200: number;
  lagerCut200: number;
}

/**
 * Schnittdaten-Punkt für Charts
 */
export interface CuttingDataPoint {
  name: string;
  date: Date;
  cutTT: number;
  cutTR: number;
  cutRR: number;
  pickCut: number;
}

/**
 * Lager-Schnitte-Datenpunkt für Charts
 */
export interface LagerCutsDataPoint {
  name: string;
  date: Date;
  lagerSumme: number;
  cutLagerKSumme: number;
  cutLagerRSumme: number;
}

// ===== WARENEINGANG INTERFACES =====

/**
 * WE-Datenpunkt (Wareneingang)
 */
export interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;  // Entspricht atrl in dispatch_data
  weManl: number | null;  // Entspricht aril in dispatch_data
}

// ===== SYSTEM INTERFACES =====

/**
 * System-ATrL-Datenpunkt aus der System-Tabelle
 */
export interface SystemAtrlDataPoint {
  Datum: string;
  ninioSapAtrl: number;
  nioWitronAtrl: number;
  nioSiemensAtrl: number;
  nioProzessAtrl: number;
  nioSonstigesAtrl: number;
}

/**
 * System-ARiL-Datenpunkt aus der System-Tabelle
 */
export interface SystemArilDataPoint {
  Datum: string;
  ninioSapAril: number;
  nioWitronAril: number;
  nioSiemensAril: number;
  nioProzessAril: number;
  nioSonstigesAril: number;
}

/**
 * System FTS Verfügbarkeitsdatenpunkt aus der System-Tabelle
 */
export interface SystemFTSDataPoint {
  id: number;
  Datum: string;
  verfuegbarkeitFTS: number;
}

// ===== LAGER INTERFACES =====

/**
 * ATrL-Datenpunkt aus der ATrL-Tabelle
 */
export interface AtrlDataPoint {
  id?: number;
  Datum: string;
  weAtrl: number | null;
  EinlagerungAblKunde: number | null;
  EinlagerungAblRest: number | null;
  umlagerungen: number | null;
  waTaPositionen: number | null;
  AuslagerungAbl: number | null;
}

/**
 * ARiL-Datenpunkt aus der ARiL-Tabelle
 */
export interface ArilDataPoint {
  id?: number;
  Datum: string;
  waTaPositionen: number;
  cuttingLagerKunde: number;
  cuttingLagerRest: number;
  Umlagerungen: number;
  lagerCutting: number;
}

// ===== SCHNITTE INTERFACES =====

/**
 * Schnitte-Datenpunkt aus der schnitte-Tabelle
 * Enthält alle Maschinendaten für Halle 1 und Halle 3
 */
export interface SchnitteDataPoint {
  id?: number;
  Datum: string;
  // Halle 1 Maschinen
  "M5-R-H1": number;    // Maschine 5, Rundstahl, Halle 1
  "M6-T-H1": number;    // Maschine 6, Träger, Halle 1
  "M7-R-H1": number;    // Maschine 7, Rundstahl, Halle 1
  "M8-T-H1": number;    // Maschine 8, Träger, Halle 1
  "M9-R-H1": number;    // Maschine 9, Rundstahl, Halle 1
  "M10-T-H1": number;   // Maschine 10, Träger, Halle 1
  "M11-R-H1": number;   // Maschine 11, Rundstahl, Halle 1
  "M12-T-H1": number;   // Maschine 12, Träger, Halle 1
  "M13-R-H1": number;   // Maschine 13, Rundstahl, Halle 1
  "M14-T-H1": number;   // Maschine 14, Träger, Halle 1
  "M15-R-H1": number;   // Maschine 15, Rundstahl, Halle 1
  "M16-T-H1": number;   // Maschine 16, Träger, Halle 1
  "M17-R-H1": number;   // Maschine 17, Rundstahl, Halle 1
  "M18-T-H1": number;   // Maschine 18, Träger, Halle 1
  "M19-T-H1": number;   // Maschine 19, Träger, Halle 1
  "M20-T-H1": number;   // Maschine 20, Träger, Halle 1
  "M21-R-H1": number;   // Maschine 21, Rundstahl, Halle 1
  "M23-T-H1": number;   // Maschine 23, Träger, Halle 1
  "M25-RR-H1": number;  // Maschine 25, Rundstahl-Rundstahl, Halle 1
  "M26-T-H1": number;   // Maschine 26, Träger, Halle 1
  "Sum-H1": number;     // Summe Halle 1
  // Halle 3 Maschinen
  "M1-T-H3": number;    // Maschine 1, Träger, Halle 3
  "M2-T-H3": number;    // Maschine 2, Träger, Halle 3
  "M3-R-H3": number;    // Maschine 3, Rundstahl, Halle 3
  "M4-T-H3": number;    // Maschine 4, Träger, Halle 3
  "M22-T-H3": number;   // Maschine 22, Träger, Halle 3
  "M24-T-H3": number;   // Maschine 24, Träger, Halle 3
  "M27-R-H3": number;   // Maschine 27, Rundstahl, Halle 3
  "Sum-H3": number;     // Summe Halle 3
}

// ===== SERVICE LEVEL INTERFACES =====

/**
 * Service Level Datenpunkt
 */
export interface ServiceLevelDataPoint {
  datum: string;
  servicegrad: number;
  csr?: number; // Alias für servicegrad für Kompatibilität
}

/**
 * Picking-Datenpunkt
 */
export interface PickingDataPoint {
  date: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
  name?: string; // Alias für Kompatibilität
  value?: number; // Alias für atrl für Kompatibilität
}

/**
 * Lieferpositions-Datenpunkt
 */
export interface DeliveryDataPoint {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

/**
 * QM-Datenpunkt (Qualitätsmeldungen)
 */
export interface QMDataPoint {
  name: string;
  value: number;
  fill: string;
  date?: string;
}

/**
 * Retourendaten
 */
export interface ReturnsDataPoint {
  date: string;
  returns: number;
  name?: string; // Alias für Kompatibilität
  value?: number; // Alias für returns für Kompatibilität
}

/**
 * Tagesleistungs-Datenpunkt
 */
export interface TagesleistungDataPoint {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

/**
 * Lagerauslastung200-Datenpunkt
 */
export interface Lagerauslastung200DataPoint {
  aufnahmeDatum: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

/**
 * Lagerauslastung240-Datenpunkt
 */
export interface Lagerauslastung240DataPoint {
  aufnahmeDatum: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

// ===== EXPORT ALLE TYPEN =====

/**
 * Union Type aller verfügbaren Datenpunkt-Typen
 */
export type AllDataPoints = 
  | AblaengereiRecord
  | CuttingDataPoint
  | LagerCutsDataPoint
  | WEDataPoint
  | SystemAtrlDataPoint
  | SystemArilDataPoint
  | SystemFTSDataPoint
  | AtrlDataPoint
  | ArilDataPoint
  | SchnitteDataPoint
  | ServiceLevelDataPoint
  | PickingDataPoint
  | DeliveryDataPoint
  | QMDataPoint
  | TagesleistungDataPoint
  | Lagerauslastung200DataPoint
  | Lagerauslastung240DataPoint;

/**
 * Basis-Konfiguration für Datenbankverbindungen
 */
export interface DatabaseConfig {
  dbPath: string;
  timeout?: number;
  readonly?: boolean;
} 