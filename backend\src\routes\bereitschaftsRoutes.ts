import { Router } from 'express';
import { bereitschaftsController } from '../controllers/bereitschaftsController';

const router = Router();

// Personen-Routes
router.get('/personen', bereitschaftsController.getAllPersonen.bind(bereitschaftsController));
router.put('/personen/reihenfolge', bereitschaftsController.updatePersonenReihenfolge.bind(bereitschaftsController));
router.get('/personen/:id', bereitschaftsController.getPersonById.bind(bereitschaftsController));
router.post('/personen', bereitschaftsController.createPerson.bind(bereitschaftsController));
router.put('/personen/:id', bereitschaftsController.updatePerson.bind(bereitschaftsController));
router.delete('/personen/:id', bereitschaftsController.deletePerson.bind(bereitschaftsController));

// Wochenplan-Routes
router.get('/wochenplan', bereitschaftsController.getWochenplan.bind(bereitschaftsController));
router.get('/wochenplan/all', bereitschaftsController.getAllWochen.bind(bereitschaftsController));
router.get('/aktuelle-bereitschaft', bereitschaftsController.getAktuelleBereitschaft.bind(bereitschaftsController));
router.post('/wochenplan/generieren', bereitschaftsController.generiereWochenplan.bind(bereitschaftsController));
router.put('/wochenplan/:id', bereitschaftsController.updateWoche.bind(bereitschaftsController));
router.delete('/wochenplan/:id', bereitschaftsController.deleteWoche.bind(bereitschaftsController));

// Ausnahmen-Routes
router.get('/ausnahmen', bereitschaftsController.getAllAusnahmen.bind(bereitschaftsController));
router.post('/ausnahmen', bereitschaftsController.createAusnahme.bind(bereitschaftsController));
router.put('/ausnahmen/:id', bereitschaftsController.updateAusnahme.bind(bereitschaftsController));
router.delete('/ausnahmen/:id', bereitschaftsController.deleteAusnahme.bind(bereitschaftsController));

// Konfiguration-Routes
router.get('/konfiguration', bereitschaftsController.getKonfiguration.bind(bereitschaftsController));
router.put('/konfiguration', bereitschaftsController.updateKonfiguration.bind(bereitschaftsController));

// Erweiterte Routes
router.get('/plan-mit-ausnahmen', bereitschaftsController.getBereitschaftsplanMitAusnahmen.bind(bereitschaftsController));
router.get('/statistiken', bereitschaftsController.getBereitschaftsStatistiken.bind(bereitschaftsController));

export default router;