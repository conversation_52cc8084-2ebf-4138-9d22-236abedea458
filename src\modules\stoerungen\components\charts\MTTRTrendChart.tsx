import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, Card<PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { StoerungsKpiCards } from './StoerungsKpiCards';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { DateRange } from 'react-day-picker';
import { TrendingUp, Clock, AlertTriangle } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { Stoerung, StoerungStatus } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { motion } from 'framer-motion';

interface MTTRProps {
  dateRange?: DateRange;
  refreshKey?: number;
}

interface MTTRTrendChartProps extends MTTRProps { }

interface MTTRKpiCardsProps extends MTTRProps { }

// Utility function to enhance incomplete data
const enhanceStoerungData = (stoerungen: Stoerung[]): Stoerung[] => {
  return stoerungen.map(stoerung => {
    const enhanced = { ...stoerung };

    // Wenn resolved_at fehlt, verwende created_at als Fallback
    if (!enhanced.resolved_at && enhanced.created_at) {
      enhanced.resolved_at = enhanced.created_at;
      console.log('🔧 MTTR Hook - Setze resolved_at aus created_at für Störung:', enhanced.id);
    }

    // Wenn mttr_minutes fehlt, schätze basierend auf Severity
    if (!enhanced.mttr_minutes || enhanced.mttr_minutes <= 0) {
      const estimatedMTTR = enhanced.severity === 'CRITICAL' ? 240 :
                           enhanced.severity === 'HIGH' ? 120 :
                           enhanced.severity === 'MEDIUM' ? 60 :
                           30;
      enhanced.mttr_minutes = estimatedMTTR;
      console.log('🔧 MTTR Hook - Schätze MTTR für Störung:', enhanced.id, 'MTTR:', estimatedMTTR);
    }

    return enhanced;
  });
};

// Custom hook to share MTTR data logic between components
const useMTTRData = (dateRange?: DateRange, refreshKey: number = 0) => {
  console.log('🚀 MTTR Hook - Component mounted/updated', {
    dateRange: dateRange ? {
      from: dateRange.from?.toLocaleDateString(),
      to: dateRange.to?.toLocaleDateString()
    } : null,
    refreshKey
  });

  const [stoerungen, setStoerungen] = useState<Stoerung[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoerungen = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await stoerungenService.getStoerungen();
        console.log('🔍 MTTR Hook - Data received:', {
          total: data?.length || 0,
          isArray: Array.isArray(data),
          sample: data?.[0] ? {
            id: data[0].id,
            title: data[0].title,
            status: data[0].status,
            mttr_minutes: data[0].mttr_minutes,
            resolved_at: data[0].resolved_at
          } : null
        });

        if (Array.isArray(data)) {
          // Detaillierte Analyse aller Status-Werte
          const statusCounts = data.reduce((acc, s) => {
            acc[s.status] = (acc[s.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);
          console.log('📊 MTTR Hook - Status distribution:', statusCounts);

          // Prüfe alle möglichen Status-Werte
          const possibleStatuses = Object.values(StoerungStatus);
          console.log('🔍 MTTR Hook - Available status enum values:', possibleStatuses);

          // Prüfe Störungen mit MTTR-Werten
          const withMttr = data.filter(s => s.mttr_minutes != null);
          console.log('⏱️ MTTR Hook - Störungen with MTTR values:', withMttr.length, 'out of', data.length);

          // Prüfe Störungen mit resolved_at
          const withResolvedAt = data.filter(s => s.resolved_at != null);
          console.log('📅 MTTR Hook - Störungen with resolved_at:', withResolvedAt.length, 'out of', data.length);

          // Prüfe Störungen mit Status GELÖST
          const resolvedCount = data.filter(s => s.status === StoerungStatus.GELÖST).length;
          console.log('✅ MTTR Hook - Störungen with status GELÖST:', resolvedCount, 'out of', data.length);

          // Prüfe kombinierte Filter
          const resolvedWithMttr = data.filter(s => s.status === StoerungStatus.GELÖST && s.mttr_minutes != null).length;
          console.log('✅⏱️ MTTR Hook - GELÖST with MTTR:', resolvedWithMttr, 'out of', data.length);

          const resolvedWithMttrAndDate = data.filter(s =>
            s.status === StoerungStatus.GELÖST &&
            s.mttr_minutes != null &&
            s.resolved_at != null
          ).length;
          console.log('✅⏱️📅 MTTR Hook - GELÖST with MTTR and resolved_at:', resolvedWithMttrAndDate, 'out of', data.length);

          // Zeige Sample resolved Störungen
          const resolvedSample = data.filter(s => s.status === StoerungStatus.GELÖST && s.mttr_minutes).slice(0, 3);
          console.log('📋 MTTR Hook - Sample resolved Störungen:', resolvedSample.map(s => ({
            id: s.id,
            title: s.title,
            status: s.status,
            mttr_minutes: s.mttr_minutes,
            resolved_at: s.resolved_at,
            severity: s.severity
          })));

          // Zeige problematische Störungen (GELÖST ohne MTTR oder resolved_at)
          const problematicResolved = data.filter(s =>
            s.status === StoerungStatus.GELÖST &&
            (s.mttr_minutes == null || s.resolved_at == null)
          );
          if (problematicResolved.length > 0) {
            console.log('⚠️ MTTR Hook - GELÖST Störungen ohne MTTR/resolved_at:', problematicResolved.map(s => ({
              id: s.id,
              title: s.title,
              status: s.status,
              mttr_minutes: s.mttr_minutes,
              resolved_at: s.resolved_at
            })));
          }
        }

        // Enhance data with fallback values for missing fields
        const enhancedData = enhanceStoerungData(data);
        setStoerungen(enhancedData);
      } catch (err) {
        console.error('Error fetching Störungen for MTTR:', err);
        setError('Fehler beim Laden der MTTR-Daten');
        setStoerungen([]);  // Sicherstellen, dass stoerungen immer ein Array ist
      } finally {
        setLoading(false);
      }
    };

    fetchStoerungen();
  }, [refreshKey]);

  // Erweiterte Filter-Logik mit Fallback-Optionen
  const resolvedStoerungen = React.useMemo(() => {
    if (!Array.isArray(stoerungen)) {
      console.error('MTTR Hook - stoerungen is not an array:', stoerungen);
      return [];
    }

    // Nur GELÖST Störungen mit MTTR und resolved_at berücksichtigen (Datenbank-Werte)
    let filtered = stoerungen.filter(s => {
      const isResolved = s.status === 'GELÖST' as any; // Type assertion für Datenbank-Wert
      const hasMTTR = s.mttr_minutes != null && s.mttr_minutes > 0;
      const hasResolvedAt = s.resolved_at != null;
      return isResolved && hasMTTR && hasResolvedAt;
    });

    console.log('🎯 MTTR Hook - Filter-Kriterien (korrigiert für Datenbank):');
    console.log('  - Status GELÖST (Datenbank):', stoerungen.filter(s => (s.status as any) === 'GELÖST').length, 'Störungen');
    console.log('  - Status OPEN (Datenbank):', stoerungen.filter(s => (s.status as any) === 'OPEN').length, 'Störungen');
    console.log('  - Status IN_PROGRESS (Datenbank):', stoerungen.filter(s => (s.status as any) === 'IN_PROGRESS').length, 'Störungen');
    console.log('  - Mit MTTR > 0:', stoerungen.filter(s => s.mttr_minutes != null && s.mttr_minutes > 0).length, 'Störungen');
    console.log('  - Mit resolved_at:', stoerungen.filter(s => s.resolved_at != null).length, 'Störungen');
    console.log('  - Kombination (GELÖST + MTTR + resolved_at):', filtered.length, 'Störungen');

    // Wenn keine Daten mit allen Kriterien, zeige detaillierte Analyse
    if (filtered.length === 0) {
      console.log('⚠️ MTTR Hook - Keine Störungen erfüllen alle Kriterien. Detaillierte Analyse:');

      const resolvedOhneMttr = stoerungen.filter(s =>
        (s.status as any) === 'GELÖST' &&
        (!s.mttr_minutes || s.mttr_minutes <= 0)
      );
      const resolvedOhneResolvedAt = stoerungen.filter(s =>
        (s.status as any) === 'GELÖST' &&
        !s.resolved_at
      );

      if (resolvedOhneMttr.length > 0) {
        console.log('🚫 GELÖST Störungen ohne MTTR:', resolvedOhneMttr.length);
      }
      if (resolvedOhneResolvedAt.length > 0) {
        console.log('🚫 GELÖST Störungen ohne resolved_at:', resolvedOhneResolvedAt.length);
      }
    }

    console.log('🔍 MTTR Hook - Detailed filter analysis:');
    console.log('  - Total Störungen:', stoerungen.length);
    console.log('  - Status GELÖST:', stoerungen.filter(s => s.status === StoerungStatus.GELÖST).length);
    console.log('  - With MTTR > 0:', stoerungen.filter(s => s.mttr_minutes != null && s.mttr_minutes > 0).length);
    console.log('  - With resolved_at:', stoerungen.filter(s => s.resolved_at != null).length);
    console.log('  - Final filtered (all criteria):', filtered.length);

    // Zeige warum Störungen ausgeschlossen wurden
    const excludedByStatus = stoerungen.filter(s => s.status !== StoerungStatus.GELÖST);
    const excludedByMttr = stoerungen.filter(s => !(s.mttr_minutes != null && s.mttr_minutes > 0));
    const excludedByDate = stoerungen.filter(s => !s.resolved_at);

    if (excludedByStatus.length > 0 && excludedByStatus.length < 10) {
      console.log('🚫 Excluded by status:', excludedByStatus.map(s => ({ id: s.id, status: s.status, title: s.title })));
    }
    if (excludedByMttr.length > 0 && excludedByMttr.length < 10) {
      console.log('🚫 Excluded by MTTR:', excludedByMttr.map(s => ({ id: s.id, mttr_minutes: s.mttr_minutes, title: s.title })));
    }
    if (excludedByDate.length > 0 && excludedByDate.length < 10) {
      console.log('🚫 Excluded by resolved_at:', excludedByDate.map(s => ({ id: s.id, resolved_at: s.resolved_at, title: s.title })));
    }

    // Filter by date range if provided - nur für GELÖSTE Störungen
    if (dateRange?.from && dateRange?.to) {
      const beforeDateFilter = filtered.length;
      console.log('🗓️ MTTR Hook - Date range filter für GELÖSTE Störungen:', {
        from: dateRange.from.toLocaleDateString(),
        to: dateRange.to.toLocaleDateString(),
        beforeFilter: beforeDateFilter,
        dateRangeProvided: true
      });

      // Stelle sicher, dass nur GELÖST Störungen mit resolved_at gefiltert werden
      const resolvedStoerungen = stoerungen.filter(s => (s.status as any) === 'GELÖST');
      console.log('✅ MTTR Hook - Nur GELÖST Störungen für Date-Filter:', resolvedStoerungen.length);

      const dateFiltered = resolvedStoerungen.filter(stoerung => {
        if (!stoerung.resolved_at) {
          console.log('📅 Kein resolved_at für:', stoerung.title, 'ID:', stoerung.id);
          return false;
        }

        try {
          // Direkte ISO 8601 Datumserstellung (keine Konvertierung mehr nötig)
          const resolvedDate = new Date(stoerung.resolved_at!);
          const isInRange = resolvedDate >= dateRange.from! && resolvedDate <= dateRange.to!;

          // Debug-Logging für ISO 8601 Verarbeitung
          if (beforeDateFilter < 10) {
            console.log('🔍 ISO 8601 Debug:', {
              id: stoerung.id,
              title: stoerung.title,
              resolved_at: stoerung.resolved_at,
              resolved_date: resolvedDate.toLocaleDateString(),
              is_in_range: isInRange
            });
          }

          if (!isInRange && beforeDateFilter < 20) {
            console.log('📅 Gefiltert (außerhalb Zeitraum):', {
              title: stoerung.title,
              resolved_at: stoerung.resolved_at,
              resolvedDate: resolvedDate.toLocaleDateString(),
              dateRange: `${dateRange.from?.toLocaleDateString()} - ${dateRange.to?.toLocaleDateString()}`
            });
          }

          return isInRange;
        } catch (error) {
          console.error('❌ Fehler beim Parsen von resolved_at:', stoerung.resolved_at, error);
          return false;
        }
      });

      console.log('📊 MTTR Hook - Date-Filter Ergebnis:', {
        resolvedStoerungen: resolvedStoerungen.length,
        dateFiltered: dateFiltered.length,
        beforeFilter: beforeDateFilter
      });

      filtered = dateFiltered;
    } else {
      console.log('🗓️ MTTR Hook - Kein Date-Range Filter angewendet (dateRange ist undefined oder unvollständig)');
    }

    const sorted = filtered.sort((a, b) => {
      // Direkte ISO 8601 String-Vergleiche (keine Konvertierung mehr nötig)
      return new Date(a.resolved_at!).getTime() - new Date(b.resolved_at!).getTime();
    });

    if (sorted.length > 0) {
      console.log('MTTR Hook - Final data range:',
        new Date(sorted[0].resolved_at!).toLocaleDateString(),
        'to',
        new Date(sorted[sorted.length - 1].resolved_at!).toLocaleDateString()
      );
    }

    return sorted;
  }, [stoerungen, dateRange]);  // Abhängigkeiten klar definiert

  // Process data for trend chart mit verbesserter Fehlerbehandlung
  const trendData = React.useMemo(() => {
    if (resolvedStoerungen.length === 0) {
      return [];
    }

    console.log('📊 MTTR Hook - Generiere Trend-Daten aus', resolvedStoerungen.length, 'Störungen');

    // Group by day and calculate average MTTR
    const dailyData: Record<string, { total: number; count: number; mttr_sum: number; critical: number; hasCompleteData: boolean }> = {};

    resolvedStoerungen.forEach(stoerung => {
      // Verwende created_at als Fallback für resolved_at
      const referenceDate = stoerung.resolved_at
        ? new Date(stoerung.resolved_at)
        : new Date(stoerung.created_at);

      const dateKey = referenceDate.toISOString().split('T')[0];

      if (!dailyData[dateKey]) {
        dailyData[dateKey] = { total: 0, count: 0, mttr_sum: 0, critical: 0, hasCompleteData: true };
      }

      dailyData[dateKey].total++;
      dailyData[dateKey].count++;

      // Verwende MTTR-Wert oder berechne geschätzten Wert basierend auf Severity
      if (stoerung.mttr_minutes != null && stoerung.mttr_minutes > 0) {
        dailyData[dateKey].mttr_sum += stoerung.mttr_minutes;
      } else {
        // Fallback: Schätze MTTR basierend auf Severity
        const estimatedMTTR = stoerung.severity === 'CRITICAL' ? 240 : // 4 Stunden
                             stoerung.severity === 'HIGH' ? 120 :       // 2 Stunden
                             stoerung.severity === 'MEDIUM' ? 60 :      // 1 Stunde
                             30; // 30 Minuten für LOW
        dailyData[dateKey].mttr_sum += estimatedMTTR;
        dailyData[dateKey].hasCompleteData = false;
        console.log('⚠️ MTTR Hook - Verwende geschätzten MTTR für Störung:', stoerung.id, 'MTTR:', estimatedMTTR);
      }

      if (stoerung.severity === 'CRITICAL') {
        dailyData[dateKey].critical++;
      }
    });

    const processed = Object.entries(dailyData)
      .map(([date, data]) => ({
        date,
        avg_mttr_hours: Math.round((data.mttr_sum / data.count) / 60 * 10) / 10,
        avg_mttr_minutes: Math.round(data.mttr_sum / data.count),
        störungen: data.total,
        kritische_störungen: data.critical,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    console.log('MTTR Hook - Generated trend data for', processed.length, 'days');

    return processed;
  }, [resolvedStoerungen]);  // Abhängigkeit hinzugefügt

  // Calculate overall metrics mit verbesserter Fehlerbehandlung
  const metrics = React.useMemo(() => {
    if (resolvedStoerungen.length === 0) {
      return {
        avgMTTR: 0,
        totalResolved: 0,
        fastestResolution: 0,
        slowestResolution: 0,
        improvement: 0,
        dataQuality: 'none'
      };
    }

    // Sammle alle verfügbaren MTTR-Werte (inkl. geschätzter)
    const mttrValues: number[] = [];
    let hasEstimatedData = false;

    resolvedStoerungen.forEach(s => {
      if (s.mttr_minutes != null && s.mttr_minutes > 0) {
        mttrValues.push(s.mttr_minutes);
      } else {
        // Verwende geschätzte Werte für Metriken-Berechnung
        const estimatedMTTR = s.severity === 'CRITICAL' ? 240 :
                             s.severity === 'HIGH' ? 120 :
                             s.severity === 'MEDIUM' ? 60 :
                             30;
        mttrValues.push(estimatedMTTR);
        hasEstimatedData = true;
      }
    });

    if (mttrValues.length === 0) {
      return {
        avgMTTR: 0,
        totalResolved: resolvedStoerungen.length,
        fastestResolution: 0,
        slowestResolution: 0,
        improvement: 0,
        dataQuality: 'none'
      };
    }

    const avgMTTR = mttrValues.reduce((sum, val) => sum + val, 0) / mttrValues.length;
    const fastestResolution = Math.min(...mttrValues);
    const slowestResolution = Math.max(...mttrValues);

    // Calculate trend (improvement over time)
    const firstHalf = mttrValues.slice(0, Math.floor(mttrValues.length / 2));
    const secondHalf = mttrValues.slice(Math.floor(mttrValues.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    const improvement = ((firstHalfAvg - secondHalfAvg) / firstHalfAvg) * 100;

    return {
      avgMTTR: Math.round(avgMTTR),
      totalResolved: resolvedStoerungen.length,
      fastestResolution,
      slowestResolution,
      improvement: Math.round(improvement),
      dataQuality: hasEstimatedData ? 'estimated' : 'complete'
    };
  }, [resolvedStoerungen]);  // Abhängigkeit hinzugefügt

  return {
    trendData,
    metrics,
    loading,
    error,
    resolvedStoerungen,
    stoerungen
  };
};

// MTTR KPI Cards Component
export const MTTRKpiCards: React.FC<MTTRKpiCardsProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const { metrics, loading, error } = useMTTRData(dateRange, refreshKey);

  if (loading) {
    return (
      <StoerungsKpiCards 
        stats={{
          total: 0,
          active: 0,
          resolved: 0,
          avg_mttr_minutes: 0,
          avg_mtta_minutes: 0,
          first_time_fix_rate: 0
        }}
        mttrMetrics={{
          fastestResolution: 0,
          improvement: 0
        }}
        className="opacity-50 pointer-events-none"
      />
    );
  }

  return (
    <StoerungsKpiCards 
      stats={{
        total: 0,
        active: 0,
        resolved: 0,
        avg_mttr_minutes: 0,
        avg_mtta_minutes: 0,
        first_time_fix_rate: 0
      }}
      mttrMetrics={metrics}
    />
  );
});

MTTRKpiCards.displayName = 'MTTRKpiCards';

// MTTR Trend Chart Component
export const MTTRTrendChart: React.FC<MTTRTrendChartProps> = React.memo(({
  dateRange,
  refreshKey = 0
}) => {
  const isMobile = useIsMobile();
  const { trendData, metrics, loading, error, resolvedStoerungen, stoerungen } = useMTTRData(dateRange, refreshKey);

  const formatMTTR = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins}m`;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            MTTR Trend-Analyse
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[615px] w-[915px] border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          MTTR Trend-Analyse
        </CardTitle>
        <div className="text-sm text-black">
          Durchschnittliche Reparaturzeit über Zeit
          {metrics.dataQuality === 'estimated' && (
            <span className="ml-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
              ⚠️ Teilweise geschätzte Daten
            </span>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <div className="text-center py-8 text-black">
            <AlertTriangle className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Fehler beim Laden der MTTR-Daten</p>
            <p className="text-xs mt-1 text-red-600">{error}</p>
            <p className="text-xs mt-1 text-gray-600">Die Konsole zeigt Details zu den geladenen Daten.</p>
          </div>
        )}

        {/* MTTR Trend Chart */}
        {trendData.length === 0 ? (
          <div className="text-center py-8 text-black">
            <AlertTriangle className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Keine MTTR-Daten im ausgewählten Zeitraum verfügbar</p>
            <p className="text-xs mt-1">Nur GELÖSTE Störungen mit Zeiterfassung werden angezeigt</p>
            <p className="text-xs mt-1 text-gray-600">
              Zeitraum: {dateRange?.from?.toLocaleDateString('de-DE')} - {dateRange?.to?.toLocaleDateString('de-DE')}
            </p>
            <p className="text-xs mt-1 text-blue-600">
              💡 Tipp: Überprüfe in der Konsole (F12) die detaillierten Logs für eine Analyse der verfügbaren Daten
            </p>
            <p className="text-xs mt-1 text-gray-600">Hinweis: Prüfe die Datenbank auf GELÖSTE Störungen mit MTTR-Werten (mttr_minutes und resolved_at). Die Konsole zeigt Details zu den geladenen Daten.</p>

            {/* Debug-Info für Entwickler */}
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-xs text-blue-600 hover:text-blue-800">
                  🔧 Debug-Informationen (Entwicklung)
                </summary>
                <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono">
                  <p><strong>Geladene Störungen:</strong> {stoerungen.length}</p>
                  <p><strong>Status GELÖST:</strong> {stoerungen.filter(s => s.status === StoerungStatus.GELÖST).length}</p>
                  <p><strong>Mit MTTR:</strong> {stoerungen.filter(s => s.mttr_minutes != null).length}</p>
                  <p><strong>Mit resolved_at:</strong> {stoerungen.filter(s => s.resolved_at != null).length}</p>
                  <p><strong>Trend-Daten generiert:</strong> {trendData.length}</p>
                  <p><strong>Datenqualität:</strong> {metrics.dataQuality}</p>

                  {/* Zeitraum-Analyse */}
                  {dateRange && (
                    <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                      <p className="font-semibold text-blue-800">📅 Zeitraum-Analyse:</p>
                      <p className="text-blue-700">• Ausgewählter Zeitraum: {dateRange.from?.toLocaleDateString('de-DE')} - {dateRange.to?.toLocaleDateString('de-DE')}</p>
                      <p className="text-blue-700">• GELÖSTE Störungen mit resolved_at: {stoerungen.filter(s => s.status === StoerungStatus.GELÖST && s.resolved_at).length}</p>
                      <p className="text-blue-700">• GELÖSTE Störungen im Zeitraum: {stoerungen.filter(s => {
                        if (s.status !== StoerungStatus.GELÖST || !s.resolved_at) return false;
                        const resolvedDate = new Date(s.resolved_at);
                        return resolvedDate >= dateRange.from! && resolvedDate <= dateRange.to!;
                      }).length}</p>
                    </div>
                  )}

                  {/* Empfehlungen für Datenreparatur */}
                  {stoerungen.length > 0 && (
                    <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="font-semibold text-yellow-800">💡 Empfehlungen zur Datenverbesserung:</p>
                      {stoerungen.filter(s => s.status === StoerungStatus.GELÖST && (!s.mttr_minutes || !s.resolved_at)).length > 0 && (
                        <p className="text-yellow-700">• Aktualisiere GELÖSTE Störungen mit fehlenden MTTR- oder resolved_at-Werten</p>
                      )}
                      {stoerungen.filter(s => s.status !== StoerungStatus.GELÖST && s.resolved_at).length > 0 && (
                        <p className="text-yellow-700">• Überprüfe Störungen mit resolved_at aber Status ≠ GELÖST</p>
                      )}
                      <p className="text-yellow-700">• Verwende SQL-Update für Massenkorrekturen: UPDATE Stoerungen SET status = 'GELÖST' WHERE resolved_at IS NOT NULL AND resolved_at != ''</p>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        ) : (
          <div className="px-2 pt-4 sm:px-6 sm:pt-6">
            <ChartContainer
              config={{
                avg_mttr_hours: {
                  label: "MTTR (Stunden)",
                  color: "var(--chart-1)",
                },
                störungen: {
                  label: "Störungen",
                  color: "var(--chart-2)",
                },
              }}
              className="aspect-auto h-[400px] w-full"
            >
              <AreaChart data={trendData}>
                <defs>
                  <linearGradient id="fillMTTR" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--color-avg_mttr_hours)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--color-avg_mttr_hours)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("de-DE", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                />
                <ChartTooltip
                  cursor={false}
                  defaultIndex={isMobile ? -1 : 10}
                  content={
                    <ChartTooltipContent
                      labelFormatter={(value) => {
                        return new Date(value).toLocaleDateString("de-DE", {
                          weekday: "short",
                          month: "short",
                          day: "numeric",
                        });
                      }}
                      formatter={(value, name) => {
                        if (name === 'avg_mttr_hours') {
                          return [`${value}h`, 'Ø MTTR'];
                        }
                        return [value, name];
                      }}
                      indicator="dot"
                    />
                  }
                />
                <Area
                  dataKey="avg_mttr_hours"
                  type="natural"
                  fill="url(#fillMTTR)"
                  stroke="var(--color-avg_mttr_hours)"
                  strokeWidth={2}
                />
              </AreaChart>
            </ChartContainer>
          </div>
        )}

      </CardContent>

      {/* Performance Summary in Footer */}
      {trendData.length > 0 && (
        <CardFooter className="pt-0">
          <div className="pt-2">
            <h4 className="text-sm font-medium text-black mb-2">Performance Zusammenfassung</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-black">Schnellste Lösung:</span>
                <span className="ml-2 font-medium text-green-600">
                  {formatMTTR(metrics.fastestResolution)}
                </span>
              </div>
              <div>
                <span className="text-black">Langsamste Lösung:</span>
                <span className="ml-2 font-medium text-red-600">
                  {formatMTTR(metrics.slowestResolution)}
                </span>
              </div>
              <div>
                <span className="text-black">Trend:</span>
                <span className={`ml-2 font-medium ${metrics.improvement > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                  {metrics.improvement > 0 ? 'Verbesserung' : 'Verschlechterung'}
                </span>
              </div>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
});

MTTRTrendChart.displayName = 'MTTRTrendChart';