import { Router } from "express";
import { DeliveryController } from "../controllers/delivery.controller";
import rateLimit from "express-rate-limit";

const router = Router();
const controller = new DeliveryController();

// Rate limiting for delivery endpoints
const deliveryLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === "development" ? 500 : 100, // Limit each IP
  message: "Too many requests to delivery API from this IP",
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all routes
router.use(deliveryLimiter);

// Delivery history routes
router.get('/history', controller.getDeliveryHistory.bind(controller));
router.get('/supplier/:supplierId', controller.getSupplierDeliveryHistory.bind(controller));
router.get('/route/:routeId', controller.getDeliveriesByRoute.bind(controller));

// Statistics and analytics routes
router.get('/stats', controller.getDeliveryStats.bind(controller));
router.get('/performance', controller.getDeliveryPerformanceMetrics.bind(controller));
router.get('/trends', controller.getDeliveryTrends.bind(controller));
router.get('/delay-reasons', controller.getDelayReasons.bind(controller));
router.get('/seasonal-patterns', controller.getSeasonalPatterns.bind(controller));

// Active routes
router.get('/routes/active', controller.getActiveRoutes.bind(controller));

// CRUD operations
router.post('/', controller.createDelivery.bind(controller));
router.put('/:deliveryId/status', controller.updateDeliveryStatus.bind(controller));

export default router;