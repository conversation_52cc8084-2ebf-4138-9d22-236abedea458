import {
  StoerungCreateData,
  StoerungUpdateData,
  StoerungWithComments,
  StoerungCommentCreateData,
  StoerungsStats,
  SystemStatusData,
  SystemStatusUpdateData,
  StoerungAttachment,
  StoerungsAttachmentCreateData
} from '../types/stoerungen.types';

/**
 * Interface für Störungsmanagement-Repository
 */
export interface IStoerungenRepository {
  /**
   * Neue Störung erstellen
   */
  createStoerung(data: StoerungCreateData): Promise<StoerungWithComments>;

  /**
   * Störungen mit optionalen Filtern abrufen
   */
  getStoerungen(options?: {
    status?: string;
    severity?: string;
    category?: string;
    affected_system?: string;
    limit?: number;
    offset?: number;
  }): Promise<StoerungWithComments[]>;

  /**
   * Einzelne Störung nach ID abrufen
   */
  getStoerungById(id: number): Promise<StoerungWithComments | null>;

  /**
   * Störung aktualisieren
   */
  updateStoerung(id: number, data: StoerungUpdateData): Promise<StoerungWithComments>;

  /**
   * Störung löschen
   */
  deleteStoerung(id: number): Promise<boolean>;

  /**
   * Kommentar zu Störung hinzufügen
   */
  addComment(data: StoerungCommentCreateData): Promise<any>;

  /**
   * Störungsstatistiken abrufen
   */
  getStoerungsStats(): Promise<StoerungsStats>;

  /**
   * System-Status abrufen
   */
  getSystemStatus(): Promise<SystemStatusData[]>;

  /**
   * System-Status aktualisieren
   */
  updateSystemStatus(data: SystemStatusUpdateData): Promise<SystemStatusData>;

  /**
   * Attachments für eine Störung abrufen
   */
  getAttachments(stoerungId: number): Promise<StoerungAttachment[]>;

  /**
   * Attachment nach ID abrufen
   */
  getAttachmentById(id: number): Promise<StoerungAttachment | null>;

  /**
   * Neues Attachment erstellen
   */
  createAttachment(data: StoerungsAttachmentCreateData): Promise<StoerungAttachment>;

  /**
   * Attachment löschen
   */
  deleteAttachment(id: number): Promise<boolean>;

  /**
   * Attachment aktualisieren
   */
  updateAttachment(id: number, data: Partial<StoerungsAttachmentCreateData>): Promise<StoerungAttachment | null>;
}

/**
 * Filter-Interface für Störungsabfragen
 */
export interface StoerungenFilter {
  status?: string;
  severity?: string;
  category?: string;
  affectedSystem?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

/**
 * System-Status-Interface
 */
export interface SystemStatus {
  systemName: string;
  status: 'OK' | 'WARNING' | 'ERROR' | 'OFF';
  lastUpdated: Date;
  metadata?: Record<string, any>;
}

/**
 * Störungs-Kategorie-Interface
 */
export interface StoerungCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
}

/**
 * Störungs-Priorität-Interface
 */
export interface StoerungPriority {
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  name: string;
  description: string;
  responseTime: number; // Minuten
}

/**
 * Störungs-Eskalationsstufe
 */
export interface StoerungEscalationLevel {
  level: number;
  name: string;
  description: string;
  requiresApproval: boolean;
  notificationChannels: string[];
}
