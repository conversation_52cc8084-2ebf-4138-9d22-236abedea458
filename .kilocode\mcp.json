{"mcpServers": {"postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://leitstand_dashboard:dashboard_password@localhost:5434/leitstand_dashboard"], "alwaysAllow": ["query"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\my_ai\\4-Lapp\\dev\\Leitstand_App\\"], "alwaysAllow": ["list_allowed_directories", "get_file_info", "read_multiple_files", "read_file"]}}}