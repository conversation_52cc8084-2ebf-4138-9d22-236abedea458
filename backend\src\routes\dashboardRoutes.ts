import { Router } from 'express';
import { dashboardController } from '../controllers/dashboardController';

const router = Router();

/**
 * Dashboard API Routes
 * Alle Endpunkte für Dashboard-Daten
 */

// Service Level Daten
router.get('/service-level', dashboardController.getServiceLevelData);

// Lieferpositionen Daten
router.get('/delivery-positions', dashboardController.getDeliveryPositionsData);

// Tagesleistung Daten
router.get('/tagesleistung', dashboardController.getTagesleistungData);

// Picking Daten
router.get('/picking', dashboardController.getPickingData);

// Alle Dashboard-Daten (optimiert)
router.get('/all', dashboardController.getAllDashboardData);

// Dashboard-Statistiken
router.get('/statistics', dashboardController.getDashboardStatistics);

// Gesundheitsprüfung
router.get('/health', dashboardController.getHealth);

// Alternative Routen für verschiedene Chart-Typen
router.get('/charts/service-level', dashboardController.getServiceLevelData);
router.get('/charts/delivery-positions', dashboardController.getDeliveryPositionsData);
router.get('/charts/tagesleistung', dashboardController.getTagesleistungData);
router.get('/charts/picking', dashboardController.getPickingData);

// Batch-Operationen
router.get('/batch/all', dashboardController.getAllDashboardData);
router.get('/batch/statistics', dashboardController.getDashboardStatistics);

export default router;