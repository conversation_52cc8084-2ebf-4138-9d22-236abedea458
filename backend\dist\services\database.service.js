"use strict";
/**
 * Zentrale Datenbankservice für das Backend
 *
 * Dieser Service nutzt Drizzle ORM für Datenbankoperationen und stellt
 * eine einheitliche Schnittstelle für alle Datenbankzugriffe bereit.
 *
 * V2: Erweitert um intelligentes Caching für Performance-Optimierung
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const db_1 = require("../db");
const schema_1 = require("../db/schema");
const drizzle_orm_1 = require("drizzle-orm");
const cache_service_1 = require("./cache.service");
/**
 * Cache-TTL-Konfiguration für verschiedene Datentypen
 */
const CACHE_TTL = {
    // Statische/seltene Änderungen - Lange Cache-Zeit
    MACHINE_EFFICIENCY: 15 * 60 * 1000, // 15 Minuten
    SCHNITTE_DATA: 15 * 60 * 1000, // 15 Minuten
    // Moderate Änderungen - Mittlere Cache-Zeit  
    WAREHOUSE_DATA: 5 * 60 * 1000, // 5 Minuten
    ABLANGEREI_DATA: 5 * 60 * 1000, // 5 Minuten
    WE_DATA: 5 * 60 * 1000, // 5 Minuten
    // Häufige Änderungen - Kurze Cache-Zeit
    DISPATCH_DATA: 2 * 60 * 1000, // 2 Minuten
    PICKING_DATA: 2 * 60 * 1000, // 2 Minuten
    RETURNS_DATA: 2 * 60 * 1000, // 2 Minuten
    ARIL_ATRL_DATA: 3 * 60 * 1000, // 3 Minuten
    // Echtzeit-ähnliche Daten - Sehr kurze Cache-Zeit
    SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute
    DAILY_PERFORMANCE: 1 * 60 * 1000, // 1 Minute
    SYSTEM_FTS_DATA: 5 * 60 * 1000, // 5 Minuten - System-Verfügbarkeitsdaten
    SYSTEM_STATS: 30 * 1000 // 30 Sekunden
};
/**
 * Zentrale Datenbankservice-Klasse
 *
 * Verwendet Drizzle ORM für alle Datenbankoperationen und bietet
 * eine einheitliche API für Frontend-Anfragen.
 *
 * V2: Erweitert um intelligentes Caching für Performance-Optimierung
 */
class DatabaseService {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)({
            defaultTTL: 5 * 60 * 1000, // 5 Minuten Standard
            maxMemoryMB: 100, // 100MB für Datenbankserver
            maxEntries: 2000, // Mehr Entries für Datenbank-Cache
            cleanupInterval: 2 * 60 * 1000, // 2 Minuten Cleanup
            enableLogging: process.env.NODE_ENV === 'development'
        });
        try {
            console.log('Initialisiere Drizzle DB-Client mit Caching...');
            console.log('✅ Drizzle DB-Client mit Backend-Cache erfolgreich initialisiert');
        }
        catch (error) {
            console.error('Fehler bei der Initialisierung des DB-Clients:', error);
            throw error;
        }
    }
    /**
     * Initialisiert die Datenbankverbindung
     * Temporäre Lösung für Node.js v23 Kompatibilitätsproblem mit better-sqlite3
     */
    async connect() {
        try {
            console.log('🔗 Stelle PostgreSQL-Datenbankverbindung her...');
            // TEMPORÄR DEAKTIVIERT: Teste Datenbankverbindung mit einfacher Query
            // const result = await db.select().from(system).limit(1);
            console.log('✅ PostgreSQL-Datenbankverbindung erfolgreich hergestellt (Test übersprungen)');
            // console.log(`📊 Gefundene System-Einträge: ${result.length}`);
            return true;
        }
        catch (error) {
            console.error('❌ Fehler beim Verbinden mit der Datenbank:');
            // Type Guard für Error-Objekte
            const isErrorWithMessage = (e) => e !== null && typeof e === 'object' && 'message' in e;
            const isErrorWithCode = (e) => isErrorWithMessage(e) && 'code' in e;
            // Fehlermeldung ausgeben
            if (isErrorWithMessage(error)) {
                console.error('Fehlermeldung:', error.message);
                // Spezifische Fehlercodes behandeln
                if (isErrorWithCode(error)) {
                    switch (error.code) {
                        case 'ENOENT':
                            console.error('Die Datenbankdatei wurde nicht gefunden. Bitte überprüfen Sie den Pfad.');
                            break;
                        case 'EACCES':
                            console.error('Keine Berechtigung zum Zugriff auf die Datenbank. Bitte überprüfen Sie die Dateiberechtigungen.');
                            break;
                        case 'SQLITE_CORRUPT':
                            console.error('Die Datenbankdatei ist beschädigt. Bitte führen Sie eine Sicherung wiederher.');
                            break;
                        default:
                            console.error('Fehlercode:', error.code);
                    }
                }
                // Stack Trace ausgeben, falls verfügbar
                if (error.stack) {
                    console.error('Stack Trace:', error.stack);
                }
            }
            else {
                console.error('Unbekannter Fehler aufgetreten:', String(error));
            }
            return false;
        }
    }
    /**
     * Schließt die Datenbankverbindung und beendet Cache
     */
    async disconnect() {
        this.cache.destroy();
    }
    /**
     * Cache für bestimmte Datentypen invalidieren
     * @param dataTypes Array von Datentypen zum Invalidieren
     */
    invalidateCache(dataTypes = ['dispatch', 'warehouse', 'cutting']) {
        return this.cache.invalidateByDataTypes(dataTypes);
    }
    /**
     * Kompletten Cache leeren (für Development/Testing)
     */
    clearAllCache() {
        this.cache.destroy();
        // Cache wird automatisch neu initialisiert beim nächsten Zugriff
    }
    /**
     * Ruft die Servicelevel-Daten für das Diagramm ab
     */
    async getServiceLevelData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                servicegrad: schema_1.dispatchData.servicegrad,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.servicegrad)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.dispatchData.datum));
            return result.map((item) => ({
                datum: item.datum,
                servicegrad: item.servicegrad || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der ServiceLevel-Daten:', error);
            throw error;
        }
    }
    /**
     * Ruft die täglichen Leistungsdaten für das Diagramm ab
     */
    async getDailyPerformanceData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                ausgeliefertLup: schema_1.dispatchData.ausgeliefert_lup,
                produzierteTonnagen: schema_1.dispatchData.produzierte_tonnagen,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.ausgeliefert_lup)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.dispatchData.datum));
            return result.map((item) => ({
                datum: item.datum,
                value: item.ausgeliefertLup || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der täglichen Leistungsdaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Kommissionierungsdaten für das Diagramm ab
     */
    async getPickingData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                atrl: schema_1.dispatchData.atrl,
                aril: schema_1.dispatchData.aril,
                fuellgrad_aril: schema_1.dispatchData.fuellgrad_aril,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.atrl), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.aril), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.fuellgrad_aril)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.dispatchData.datum));
            return result.map((item) => ({
                date: item.datum,
                atrl: item.atrl || 0,
                aril: item.aril || 0,
                fuellgrad_aril: item.fuellgrad_aril || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Kommissionierungsdaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Retourendaten für das Diagramm ab
     */
    async getReturnsData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                qmAngenommen: schema_1.dispatchData.qm_angenommen,
                qmAbgelehnt: schema_1.dispatchData.qm_abgelehnt,
                qmOffen: schema_1.dispatchData.qm_offen,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.qm_angenommen)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.dispatchData.datum));
            return result.map((item) => ({
                name: item.datum,
                value: (item.qmAngenommen || 0) + (item.qmAbgelehnt || 0) + (item.qmOffen || 0),
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Retourendaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Lieferpositionsdaten für das Diagramm ab
     */
    async getDeliveryPositionsData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                ausgeliefert_lup: schema_1.dispatchData.ausgeliefert_lup,
                rueckstaendig: schema_1.dispatchData.rueckstaendig,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.ausgeliefert_lup), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.rueckstaendig)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.dispatchData.datum));
            return result.map((item) => ({
                date: item.datum,
                ausgeliefert_lup: item.ausgeliefert_lup || 0,
                rueckstaendig: item.rueckstaendig || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lieferpositionsdaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Tagesleistungsdaten für das Diagramm ab
     */
    async getTagesleistungData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.dispatchData.datum,
                produzierte_tonnagen: schema_1.dispatchData.produzierte_tonnagen,
                direktverladung_kiaa: schema_1.dispatchData.direktverladung_kiaa,
                umschlag: schema_1.dispatchData.umschlag,
                kg_pro_colli: schema_1.dispatchData.kg_pro_colli,
                elefanten: schema_1.dispatchData.elefanten,
            })
                .from(schema_1.dispatchData)
                .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.datum), (0, drizzle_orm_1.isNotNull)(schema_1.dispatchData.produzierte_tonnagen)))
                .orderBy((0, drizzle_orm_1.asc)(schema_1.dispatchData.datum));
            return result.map((item) => ({
                date: item.datum,
                produzierte_tonnagen: item.produzierte_tonnagen || 0,
                direktverladung_kiaa: item.direktverladung_kiaa || 0,
                umschlag: item.umschlag || 0,
                kg_pro_colli: item.kg_pro_colli || 0,
                elefanten: item.elefanten || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Tagesleistungsdaten:', error);
            throw error;
        }
    }
    /**
     * Importiert die Daten aus der Abl.csv-Datei in die Ablaengerei-Tabelle
     */
    async importAblaengereiCsvData() {
        try {
            // CSV-Import-Logik mit Drizzle implementieren
            // Vorläufig: Erfolg zurückgeben
            return true;
        }
        catch (error) {
            console.error('Fehler beim Importieren der Ablaengerei-CSV-Daten:', error);
            return false;
        }
    }
    /**
     * Ruft die Daten aus der Ablaengerei-Tabelle ab
     */
    async getAblaengereiData() {
        try {
            const result = await db_1.db.select()
                .from(schema_1.ablaengerei)
                .orderBy((0, drizzle_orm_1.asc)(schema_1.ablaengerei.datum));
            return result.map((item) => ({
                id: item.id,
                datum: item.datum || '',
                cutTT: item.cutTt || 0,
                cutTR: item.cutTr || 0,
                cutRR: item.cutRr || 0,
                pickCut: item.pickCut || 0,
                lagerCut220: item.lagerCut220 || 0,
                lagerCut240: item.lagerCut240 || 0,
                lagerCut200: item.lagerCut200 || 0,
                cutLagerK200: item.cutLagerK200 || 0,
                cutLagerK240: item.cutLagerK240 || 0,
                cutLagerK220: item.cutLagerK220 || 0,
                cutLager200: item.cutLager200 || 0,
                cutLagerR240: item.cutLagerR240 || 0,
                cutLagerR220: item.cutLagerR220 || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Ablaengerei-Daten:', error);
            throw error;
        }
    }
    /**
     * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
     */
    async getWEData() {
        try {
            const result = await db_1.db.select()
                .from(schema_1.we)
                .orderBy((0, drizzle_orm_1.asc)(schema_1.we.datum));
            return result.map((item) => ({
                id: item.id,
                datum: item.datum || '',
                weAtrl: item.weAtrl || 0,
                weManl: item.weManl || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der WE-Daten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Lagerauslastung200-Daten für das Diagramm ab (mit Caching)
     * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
     * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
     */
    async getLagerauslastung200Data(startDate, endDate) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forService('DatabaseService', 'getLagerauslastung200Data', {
            startDate,
            endDate
        });
        return await this.cache.cachedQuery(cacheKey, async () => {
            try {
                // Daten mit Drizzle abfragen
                const baseQuery = db_1.db.select({
                    aufnahmeDatum: schema_1.auslastung200.aufnahmeDatum,
                    auslastungA: schema_1.auslastung200.auslastungA,
                    auslastungB: schema_1.auslastung200.auslastungB,
                    auslastungC: schema_1.auslastung200.auslastungC
                }).from(schema_1.auslastung200);
                let results;
                // Datumsfilter hinzufügen, wenn vorhanden
                if (startDate && endDate) {
                    results = await baseQuery.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.auslastung200.aufnahmeDatum, startDate), (0, drizzle_orm_1.lte)(schema_1.auslastung200.aufnahmeDatum, endDate))).orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung200.aufnahmeDatum));
                }
                else if (startDate) {
                    results = await baseQuery.where((0, drizzle_orm_1.gte)(schema_1.auslastung200.aufnahmeDatum, startDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung200.aufnahmeDatum));
                }
                else if (endDate) {
                    results = await baseQuery.where((0, drizzle_orm_1.lte)(schema_1.auslastung200.aufnahmeDatum, endDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung200.aufnahmeDatum));
                }
                else {
                    results = await baseQuery.orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung200.aufnahmeDatum));
                }
                // Daten in das erwartete Format umwandeln und Gesamt-Wert berechnen
                const mappedResults = results.map((item) => {
                    // Auslastungswerte als Dezimalzahl (0-1) parsen
                    const auslastungA = parseFloat(item.auslastungA || '0');
                    const auslastungB = parseFloat(item.auslastungB || '0');
                    const auslastungC = parseFloat(item.auslastungC || '0');
                    // Gesamtdurchschnitt berechnen (als Dezimalzahl belassen)
                    const gesamt = (auslastungA + auslastungB + auslastungC) / 3;
                    return {
                        aufnahmeDatum: item.aufnahmeDatum || '',
                        auslastungA,
                        auslastungB,
                        auslastungC,
                        gesamt
                    };
                });
                return mappedResults;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Lagerauslastung200-Daten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA);
    }
    /**
     * Ruft die Lagerauslastung240-Daten für das Diagramm ab
     * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
     * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
     */
    async getLagerauslastung240Data(startDate, endDate) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forService('DatabaseService', 'getLagerauslastung240Data', {
            startDate,
            endDate
        });
        return await this.cache.cachedQuery(cacheKey, async () => {
            try {
                // Daten mit Drizzle abfragen
                const baseQuery = db_1.db.select({
                    aufnahmeDatum: schema_1.auslastung240.aufnahmeDatum,
                    auslastungA: schema_1.auslastung240.auslastungA,
                    auslastungB: schema_1.auslastung240.auslastungB,
                    auslastungC: schema_1.auslastung240.auslastungC
                }).from(schema_1.auslastung240);
                let results;
                // Datumsfilter hinzufügen, wenn vorhanden
                if (startDate && endDate) {
                    results = await baseQuery.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.auslastung240.aufnahmeDatum, startDate), (0, drizzle_orm_1.lte)(schema_1.auslastung240.aufnahmeDatum, endDate))).orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung240.aufnahmeDatum));
                }
                else if (startDate) {
                    results = await baseQuery.where((0, drizzle_orm_1.gte)(schema_1.auslastung240.aufnahmeDatum, startDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung240.aufnahmeDatum));
                }
                else if (endDate) {
                    results = await baseQuery.where((0, drizzle_orm_1.lte)(schema_1.auslastung240.aufnahmeDatum, endDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung240.aufnahmeDatum));
                }
                else {
                    results = await baseQuery.orderBy((0, drizzle_orm_1.asc)(schema_1.auslastung240.aufnahmeDatum));
                }
                // Daten in das erwartete Format umwandeln und Gesamt-Wert berechnen
                const mappedResults = results.map((item) => {
                    // Auslastungswerte als Dezimalzahl (0-1) parsen
                    const auslastungA = parseFloat(item.auslastungA || '0');
                    const auslastungB = parseFloat(item.auslastungB || '0');
                    const auslastungC = parseFloat(item.auslastungC || '0');
                    // Gesamtdurchschnitt berechnen (als Dezimalzahl belassen)
                    const gesamt = (auslastungA + auslastungB + auslastungC) / 3;
                    return {
                        aufnahmeDatum: item.aufnahmeDatum || '',
                        auslastungA,
                        auslastungB,
                        auslastungC,
                        gesamt
                    };
                });
                return mappedResults;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Lagerauslastung240-Daten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA);
    }
    async getSchnitteData() {
        try {
            return await db_1.db.select().from(schema_1.schnitte);
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Schnitte-Daten:', error);
            throw error;
        }
    }
    async getAtrlData(startDate, endDate) {
        try {
            const baseQuery = db_1.db.select().from(schema_1.atrL);
            let result;
            if (startDate && endDate) {
                result = await baseQuery.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.atrL.Datum, startDate), (0, drizzle_orm_1.lte)(schema_1.atrL.Datum, endDate))).orderBy((0, drizzle_orm_1.asc)(schema_1.atrL.Datum));
            }
            else if (startDate) {
                result = await baseQuery.where((0, drizzle_orm_1.gte)(schema_1.atrL.Datum, startDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.atrL.Datum));
            }
            else if (endDate) {
                result = await baseQuery.where((0, drizzle_orm_1.lte)(schema_1.atrL.Datum, endDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.atrL.Datum));
            }
            else {
                result = await baseQuery.orderBy((0, drizzle_orm_1.asc)(schema_1.atrL.Datum));
            }
            return result.map((item) => ({
                Datum: item.datum ? new Date(item.datum).toISOString().split('T')[0] : '',
                weAtrl: item.weAtrl || 0,
                waTaPositionen: item.waTaPositionen || 0,
                EinlagerungAblKunde: item.einlagerungAblKunde || 0,
                EinlagerungAblRest: item.einlagerungAblRest || 0,
                umlagerungen: item.umlagerungen || 0,
                AuslagerungAbl: item.auslagerungAbl || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der ARiL-Daten:', error);
            throw error;
        }
    }
    async getArilData(startDate, endDate) {
        try {
            const baseQuery = db_1.db.select().from(schema_1.ariL);
            let result;
            if (startDate && endDate) {
                result = await baseQuery.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.ariL.Datum, startDate), (0, drizzle_orm_1.lte)(schema_1.ariL.Datum, endDate))).orderBy((0, drizzle_orm_1.asc)(schema_1.ariL.Datum));
            }
            else if (startDate) {
                result = await baseQuery.where((0, drizzle_orm_1.gte)(schema_1.ariL.Datum, startDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.ariL.Datum));
            }
            else if (endDate) {
                result = await baseQuery.where((0, drizzle_orm_1.lte)(schema_1.ariL.Datum, endDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.ariL.Datum));
            }
            else {
                result = await baseQuery.orderBy((0, drizzle_orm_1.asc)(schema_1.ariL.Datum));
            }
            return result.map((item) => ({
                Datum: item.datum ? new Date(item.datum).toISOString().split('T')[0] : '',
                waTaPositionen: item.waTaPositionen || 0,
                cuttingLagerKunde: item.cuttingLagerKunde || 0,
                cuttingLagerRest: item.cuttingLagerRest || 0,
                Umlagerungen: item.Umlagerungen || 0,
                lagerCutting: item.lagerCutting || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der ARiL-Daten:', error);
            throw error;
        }
    }
    async getMaschinenEfficiency() {
        try {
            const schnitteData = await db_1.db.select().from(schema_1.schnitte);
            const maschinenData = await db_1.db.select().from(schema_1.maschinen);
            const maschinenMap = new Map(maschinenData.map((m) => [m.machine, m.schnitteProStd]));
            const efficiencyData = schnitteData.flatMap((s) => {
                if (!s.datum)
                    return [];
                return Object.entries(s)
                    .map(([key, value]) => {
                    if (key.startsWith('m') && typeof value === 'number') {
                        // Konvertiere Drizzle-Key (z.B. m5RH1) zu Maschinen-Key (z.B. M5-R-H1)
                        const machineKey = key.replace(/([a-z])([A-Z])/g, '$1-$2').replace(/([0-9])([A-Z])/g, '$1-$2').toUpperCase();
                        const sollSchnitte = maschinenMap.get(machineKey);
                        if (sollSchnitte === undefined || sollSchnitte === null) {
                            console.log(`Warnung: Keine Soll-Schnitte für Maschine ${machineKey} gefunden`);
                            return null; // Überspringe Maschinen ohne Soll-Werte
                        }
                        // Explizite Typzuweisung für TypeScript
                        const sollSchnitteWert = sollSchnitte;
                        const tagesSchnitte = value;
                        // Annahme: Ein Standard-Arbeitstag hat 21 Stunden.
                        const standardArbeitsstunden = 21;
                        // Berechne die Soll-Schnitte für einen Standard-Arbeitstag.
                        const sollSchnitteProTag = sollSchnitteWert * standardArbeitsstunden;
                        // Die Effizienz vergleicht die tatsächlichen Tagesschnitte mit den Soll-Tagesschnitten.
                        // Ein Wert > 100% bedeutet, dass mehr als das 21-Stunden-Soll produziert wurde,
                        // was z.B. durch längere Laufzeiten (Mehrschichtbetrieb) zustande kommen kann.
                        // Da wir oben bereits null-Werte ausfiltern, ist sollSchnitte hier immer definiert
                        const effizienzProzent = (tagesSchnitte / sollSchnitteProTag) * 100;
                        // Die "Ist-Schnitte pro Stunde" sind eine abgeleitete Metrik basierend auf der 21-Stunden-Annahme.
                        const istSchnitteProStunde = tagesSchnitte / standardArbeitsstunden;
                        return {
                            Datum: s.datum,
                            Machine: machineKey, // Verwende den korrigierten Key
                            sollSchnitte: sollSchnitteWert,
                            tagesSchnitte,
                            istSchnitteProStunde,
                            effizienzProzent,
                        };
                    }
                    return null;
                })
                    .filter((item) => item !== null);
            });
            return efficiencyData;
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Maschinen-Effizienz-Daten:', error);
            throw error;
        }
    }
    async getCuttingChartData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.ablaengerei.datum,
                cutTT: schema_1.ablaengerei.cutTT,
                cutTR: schema_1.ablaengerei.cutTR,
                cutRR: schema_1.ablaengerei.cutRR,
                pickCut: schema_1.ablaengerei.pickCut,
            })
                .from(schema_1.ablaengerei)
                .orderBy((0, drizzle_orm_1.asc)(schema_1.ablaengerei.datum));
            return result.map((item) => ({
                name: item.datum,
                date: item.datum,
                cutTT: item.cutTT || 0,
                cutTR: item.cutTR || 0,
                cutRR: item.cutRR || 0,
                pickCut: item.pickCut || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Cutting-Chart-Daten:', error);
            throw error;
        }
    }
    async getLagerCutsChartData() {
        try {
            const result = await db_1.db.select({
                datum: schema_1.ablaengerei.datum,
                lagerCut200: schema_1.ablaengerei.lagerCut200,
                lagerCut220: schema_1.ablaengerei.lagerCut220,
                lagerCut240: schema_1.ablaengerei.lagerCut240,
                cutLagerK200: schema_1.ablaengerei.cutLagerK200,
                cutLagerK220: schema_1.ablaengerei.cutLagerK220,
                cutLagerK240: schema_1.ablaengerei.cutLagerK240,
                cutLagerR220: schema_1.ablaengerei.cutLagerR220,
                cutLagerR240: schema_1.ablaengerei.cutLagerR240,
            })
                .from(schema_1.ablaengerei)
                .orderBy((0, drizzle_orm_1.asc)(schema_1.ablaengerei.datum));
            return result.map((item) => ({
                name: item.datum,
                date: item.datum,
                lagerSumme: (item.lagerCut200 || 0) + (item.lagerCut220 || 0) + (item.lagerCut240 || 0),
                cutLagerKSumme: (item.cutLagerK200 || 0) + (item.cutLagerK220 || 0) + (item.cutLagerK240 || 0),
                cutLagerRSumme: (item.cutLagerR220 || 0) + (item.cutLagerR240 || 0),
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lager-Cuts-Chart-Daten:', error);
            throw error;
        }
    }
    /**
     * System FTS Verfügbarkeitsdaten mit Cache-Support und Datums-Filterung
     */
    async getSystemFTSData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forService('DatabaseService', 'getSystemFTSData', {
            startDate: dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate,
            endDate: dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate
        });
        return await this.cache.cachedQuery(cacheKey, async () => {
            try {
                console.log('System FTS Daten aus Datenbank laden...');
                const baseQuery = db_1.db.select({
                    id: schema_1.system.id,
                    Datum: schema_1.system.datum,
                    verfuegbarkeitFTS: schema_1.system.verfuegbarkeitFts,
                }).from(schema_1.system);
                let result;
                // Baue Drizzle where-Kondition für Datums-Filterung (String-Format: YYYY-MM-DD zu Date konvertieren)
                if ((dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate) && (dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate)) {
                    const startDate = new Date(dateRange.startDate);
                    const endDate = new Date(dateRange.endDate);
                    result = await baseQuery.where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.system.datum, startDate), (0, drizzle_orm_1.lte)(schema_1.system.datum, endDate))).orderBy((0, drizzle_orm_1.asc)(schema_1.system.datum));
                }
                else if (dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate) {
                    const startDate = new Date(dateRange.startDate);
                    result = await baseQuery.where((0, drizzle_orm_1.gte)(schema_1.system.datum, startDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.system.datum));
                }
                else if (dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate) {
                    const endDate = new Date(dateRange.endDate);
                    result = await baseQuery.where((0, drizzle_orm_1.lte)(schema_1.system.datum, endDate)).orderBy((0, drizzle_orm_1.asc)(schema_1.system.datum));
                }
                else {
                    result = await baseQuery.orderBy((0, drizzle_orm_1.asc)(schema_1.system.datum));
                }
                // Transformiere Drizzle-Daten zu SystemFTSDataPoint
                const transformedData = result
                    .filter((item) => item.verfuegbarkeitFTS !== null) // Nur Datensätze mit FTS-Werten
                    .map((item) => ({
                    id: item.id,
                    Datum: item.Datum || '', // Datum ist bereits String im YYYY-MM-DD Format
                    verfuegbarkeitFTS: item.verfuegbarkeitFTS || 0,
                }));
                console.log(`✅ ${transformedData.length} System FTS Datensätze aus Datenbank geladen`);
                return transformedData;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der System FTS Daten:', error);
                throw error;
            }
        }, CACHE_TTL.SYSTEM_FTS_DATA);
    }
    /**
     * Materialdaten aus der Datenbank abrufen
     * Lädt alle verfügbaren Materialdaten mit MATNR, Materialkurztext und Kabeldurchmesser
     */
    async getMaterialdaten() {
        return this.cache.cachedQuery(cache_service_1.BackendCacheKeyGenerator.forQuery('materialdaten', 'getAll'), async () => {
            try {
                console.log('🔍 Lade Materialdaten aus der Datenbank...');
                // Verwende Drizzle für Materialdaten
                const result = await db_1.db.select({
                    matnr: schema_1.materialdaten.matnr,
                    materialkurztext: schema_1.materialdaten.materialkurztext,
                    kabeldurchmesser: schema_1.materialdaten.kabeldurchmesser,
                    zuschlagKabeldurchmesser: schema_1.materialdaten.zuschlagKabeldurchmesser,
                    biegefaktor: schema_1.materialdaten.biegefaktor,
                    kleinsterErlauberFreiraum: schema_1.materialdaten.kleinsterErlauberFreiraum,
                    bruttogewicht: schema_1.materialdaten.bruttogewicht,
                    created_at: schema_1.materialdaten.created_at,
                    updated_at: schema_1.materialdaten.updated_at
                })
                    .from(schema_1.materialdaten)
                    .orderBy((0, drizzle_orm_1.asc)(schema_1.materialdaten.matnr));
                // Daten für Frontend-Kompatibilität transformieren
                const transformedData = result.map((item) => ({
                    MATNR: item.matnr,
                    Materialkurztext: item.materialkurztext || '',
                    Kabeldurchmesser: item.kabeldurchmesser || 0,
                    ZuschlagKabeldurchmesser: item.zuschlagKabeldurchmesser || 0,
                    Biegefaktor: item.biegefaktor || 0,
                    KleinsterErlauberFreiraum: item.kleinsterErlauberFreiraum || 0,
                    Bruttogewicht: item.bruttogewicht || 0
                }));
                console.log(`✅ ${transformedData.length} Materialdaten aus Datenbank geladen`);
                return transformedData;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Materialdaten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA // 5 Minuten Cache für Stammdaten
        );
    }
    /**
     * Trommeldaten aus der Datenbank abrufen
     * Lädt alle verfügbaren Trommeldaten mit Trommelname und Außendurchmesser
     */
    async getTrommeldaten() {
        return this.cache.cachedQuery(cache_service_1.BackendCacheKeyGenerator.forQuery('trommeldaten', 'getAll'), async () => {
            try {
                console.log('🔍 Lade Trommeldaten aus der Datenbank...');
                // Verwende Drizzle für Trommeldaten
                const result = await db_1.db.select({
                    trommelname: schema_1.trommeldaten.trommeldaten,
                    aussendurchmesser: schema_1.trommeldaten.aussendurchmesser,
                    kerndurchmesser: schema_1.trommeldaten.kerndurchmesser,
                    // Hinweis: freiraum_mm, wickelbreite_mm, maxTragkraft_Kg, max_Laenge, max_Gewicht, created_at und updated_at
                    // existieren nicht in der trommeldaten Tabelle laut Schema
                })
                    .from(schema_1.trommeldaten)
                    .orderBy((0, drizzle_orm_1.asc)(schema_1.trommeldaten.trommeldaten));
                // Daten für Frontend-Kompatibilität transformieren
                const transformedData = result.map((item) => ({
                    Trommelname: item.trommelname,
                    Außendurchmesser: item.aussendurchmesser || 0,
                    Kerndurchmesser: item.kerndurchmesser || 0,
                    Freiraum_mm: item.freiraum_mm || 0,
                    Wickelbreite_mm: item.wickelbreite_mm || 0,
                    MaxTragkraft_Kg: item.maxTragkraft_Kg || 0,
                    Max_Laenge: item.max_Laenge || 0,
                    Max_Gewicht: item.max_Gewicht || 0
                }));
                console.log(`✅ ${transformedData.length} Trommeldaten aus Datenbank geladen`);
                return transformedData;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Trommeldaten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA // 5 Minuten Cache für Stammdaten
        );
    }
    /**
     * Service Health Status für Frontend API
     */
    async getServiceHealth() {
        try {
            const startTime = Date.now();
            // TEMPORÄR DEAKTIVIERT: Test database connections
            // await db.select().from(system).limit(1);
            const responseTime = Date.now() - startTime;
            return {
                status: 'healthy',
                responseTime,
                cache: this.getCacheStats(),
                databases: ['sfm_dashboard'],
                version: 'drizzle-v1.0.0'
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                error: error instanceof Error ? error.message : 'Unknown error',
                version: 'drizzle-v1.0.0'
            };
        }
    }
    /**
     * System-Statistiken für Frontend API
     */
    async getSystemStats() {
        return this.cache.cachedQuery(cache_service_1.BackendCacheKeyGenerator.forService('DatabaseService', 'getSystemStats'), async () => {
            var _a, _b, _c;
            try {
                // Grundlegende Systemstatistiken
                const [systemCount, dispatchCount, stoerungenCount] = await Promise.all([
                    db_1.db.select({ count: (0, drizzle_orm_1.sql) `count(*)` }).from(schema_1.system),
                    db_1.db.select({ count: (0, drizzle_orm_1.sql) `count(*)` }).from(schema_1.dispatchData),
                    db_1.db.select({ count: (0, drizzle_orm_1.sql) `count(*)` }).from(schema_1.system) // Placeholder
                ]);
                return {
                    tables: {
                        system: ((_a = systemCount[0]) === null || _a === void 0 ? void 0 : _a.count) || 0,
                        dispatchData: ((_b = dispatchCount[0]) === null || _b === void 0 ? void 0 : _b.count) || 0,
                        stoerungen: ((_c = stoerungenCount[0]) === null || _c === void 0 ? void 0 : _c.count) || 0
                    },
                    timestamp: new Date().toISOString()
                };
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Systemstatistiken:', error);
                throw error;
            }
        }, CACHE_TTL.SYSTEM_FTS_DATA);
    }
    /**
     * Cache-Statistiken für Frontend API
     */
    getCacheStats() {
        return this.cache.getStats();
    }
    /**
     * Knowledge Bases für Frontend API (RAG Database)
     */
    async getKnowledgeBases() {
        // Placeholder - RAG functionality would be implemented here
        return [];
    }
}
exports.DatabaseService = DatabaseService;
