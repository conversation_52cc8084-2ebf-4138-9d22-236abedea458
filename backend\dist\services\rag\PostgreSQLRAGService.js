"use strict";
/**
 * PostgreSQLRAGService - RAG Service for PostgreSQL database
 *
 * Provides RAG functionality using PostgreSQL instead of SQLite
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostgreSQLRAGService = void 0;
const pg_1 = require("pg");
const uuid_1 = require("uuid");
const rag_types_1 = require("../../types/rag.types");
class PostgreSQLRAGService {
    constructor() {
        this.client = new pg_1.Client({
            host: 'localhost',
            port: 5434,
            database: 'rag_knowledge',
            user: 'leitstand_dashboard',
            password: 'dashboard_password',
            connectionTimeoutMillis: 5000,
            query_timeout: 10000,
        });
        // Initialize database connection
        this.initializeDatabase().catch(error => {
            console.error('Failed to initialize PostgreSQLRAGService:', error);
        });
    }
    /**
     * Initialize database connection
     */
    async initializeDatabase() {
        try {
            console.log('[PostgreSQL RAG] Attempting to connect to database...');
            await this.client.connect();
            console.log('[PostgreSQL RAG] Database connected successfully');
            // Test the connection with a simple query
            const result = await this.client.query('SELECT 1 as test');
            console.log('[PostgreSQL RAG] Connection test successful:', result.rows[0]);
        }
        catch (error) {
            console.error('[PostgreSQL RAG] Failed to connect to database:', error);
            throw error;
        }
    }
    /**
     * Store vector embedding for a document chunk
     */
    async storeEmbedding(chunkId, embedding, model = 'text-embedding-3-small') {
        try {
            const id = (0, uuid_1.v4)();
            const vectorArray = Array.from(embedding);
            const now = new Date().toISOString();
            // Convert Float32Array to PostgreSQL array format
            const vectorString = '{' + vectorArray.join(',') + '}';
            await this.client.query('INSERT INTO embeddings (id, chunk_id, vector, model_name, dimension, created_at) VALUES ($1, $2, $3, $4, $5, $6)', [id, chunkId, vectorString, model, embedding.length, now]);
            return {
                id,
                chunkId,
                model,
                embedding,
                dimensions: embedding.length,
                createdAt: new Date(now)
            };
        }
        catch (error) {
            console.error('Error storing vector embedding:', error);
            throw {
                code: rag_types_1.RAGErrorCode.VECTOR_STORAGE_FAILED,
                message: 'Failed to store vector embedding',
                details: { chunkId, model, error: error.message },
                timestamp: new Date()
            };
        }
    }
    /**
     * Perform similarity search using cosine similarity
     */
    async searchSimilar(queryEmbedding, options = {}) {
        const { categoryIds, limit = 10, threshold = 0.7, language = 'de' } = options;
        try {
            // Build SQL query to get embeddings with chunks and documents
            let query = `
        SELECT
          e.id as embedding_id,
          e.vector,
          e.model_name,
          c.id as chunk_id,
          c.content as chunk_content,
          c.chunk_index,
          c.token_count,
          c.start_position,
          c.end_position,
          d.id as document_id,
          d.title as document_title,
          d.source,
          d.content_type,
          d.language,
          'default' as category_name
        FROM embeddings e
        JOIN chunks c ON e.chunk_id = c.id
        JOIN documents d ON c.document_id = d.id
        WHERE d.status = 'indexed' AND d.language = $1
      `;
            const params = [language];
            // Add category filter if specified (simplified for now)
            if (categoryIds && categoryIds.length > 0) {
                query += ` AND d.source = ANY($${params.length + 1})`;
                params.push(categoryIds);
            }
            const result = await this.client.query(query, params);
            const rows = result.rows;
            // Calculate similarities
            const similarities = [];
            for (const row of rows) {
                // Parse PostgreSQL array format {1,2,3,...}
                const vectorString = row.vector;
                const vectorArray = new Float32Array(vectorString
                    .slice(1, -1) // Remove { and }
                    .split(',')
                    .map(Number));
                const similarity = this.cosineSimilarity(queryEmbedding, vectorArray);
                if (similarity >= threshold) {
                    similarities.push({
                        row,
                        similarity
                    });
                }
            }
            // Sort by similarity (descending) and limit results
            similarities.sort((a, b) => b.similarity - a.similarity);
            const topResults = similarities.slice(0, limit);
            // Format results
            const results = topResults.map((result, index) => ({
                chunk: {
                    id: result.row.chunk_id,
                    documentId: result.row.document_id,
                    chunkIndex: result.row.chunk_index,
                    content: result.row.chunk_content,
                    tokenCount: result.row.token_count,
                    startPosition: result.row.start_position,
                    endPosition: result.row.end_position,
                    createdAt: new Date(),
                    document: {
                        id: result.row.document_id,
                        knowledgeBaseId: 1,
                        title: result.row.document_title,
                        content: '',
                        contentType: result.row.content_type,
                        source: result.row.source,
                        language: result.row.language,
                        hash: '',
                        isActive: true,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        knowledgeBase: {
                            id: 1,
                            name: result.row.category_name,
                            description: '',
                            category: 'business',
                            isActive: true,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        }
                    }
                },
                similarity: result.similarity,
                rank: index + 1
            }));
            return results;
        }
        catch (error) {
            console.error('Error in similarity search:', error);
            throw {
                code: rag_types_1.RAGErrorCode.SIMILARITY_SEARCH_FAILED,
                message: 'Failed to perform similarity search',
                details: { error: error.message, options },
                timestamp: new Date()
            };
        }
    }
    /**
     * Get vector database statistics
     */
    async getStatistics() {
        try {
            console.log('[PostgreSQL RAG] Getting statistics...');
            const totalEmbeddings = await this.client.query('SELECT COUNT(*) as count FROM embeddings');
            const totalChunks = await this.client.query('SELECT COUNT(*) as count FROM chunks');
            const totalDocuments = await this.client.query('SELECT COUNT(*) as count FROM documents WHERE status = $1', ['indexed']);
            // Get category count
            const totalCategories = await this.client.query('SELECT COUNT(*) as count FROM categories');
            // Get average chunk size
            const avgChunkSize = await this.client.query('SELECT AVG(token_count) as avg FROM chunks');
            // Estimate storage size
            const avgDimensions = await this.client.query('SELECT AVG(array_length(vector, 1)) as avg FROM embeddings');
            const storageSize = (totalEmbeddings.rows[0].count * (avgDimensions.rows[0].avg || 1024)) * 4;
            const stats = {
                totalEmbeddings: parseInt(totalEmbeddings.rows[0].count) || 0,
                totalChunks: parseInt(totalChunks.rows[0].count) || 0,
                totalDocuments: parseInt(totalDocuments.rows[0].count) || 0,
                totalKnowledgeBases: parseInt(totalCategories.rows[0].count) || 0,
                averageChunkSize: Math.round(parseFloat(avgChunkSize.rows[0].avg) || 0),
                storageSize: storageSize || 0
            };
            console.log('[PostgreSQL RAG] Statistics retrieved:', stats);
            return stats;
        }
        catch (error) {
            console.error('[PostgreSQL RAG] Error getting statistics:', error);
            return {
                totalEmbeddings: 0,
                totalChunks: 0,
                totalDocuments: 0,
                totalKnowledgeBases: 0,
                averageChunkSize: 0,
                storageSize: 0
            };
        }
    }
    // Vector Operations Implementation
    /**
     * Calculate cosine similarity between two vectors
     */
    cosineSimilarity(a, b) {
        if (a.length !== b.length) {
            throw new Error('Vectors must have the same dimensions');
        }
        const dotProd = this.dotProduct(a, b);
        const magnitudeA = this.magnitude(a);
        const magnitudeB = this.magnitude(b);
        if (magnitudeA === 0 || magnitudeB === 0) {
            return 0;
        }
        return dotProd / (magnitudeA * magnitudeB);
    }
    /**
     * Calculate dot product of two vectors
     */
    dotProduct(a, b) {
        let sum = 0;
        for (let i = 0; i < a.length; i++) {
            sum += a[i] * b[i];
        }
        return sum;
    }
    /**
     * Calculate magnitude (length) of a vector
     */
    magnitude(vector) {
        let sum = 0;
        for (let i = 0; i < vector.length; i++) {
            sum += vector[i] * vector[i];
        }
        return Math.sqrt(sum);
    }
    /**
     * Normalize a vector to unit length
     */
    normalize(vector) {
        const mag = this.magnitude(vector);
        if (mag === 0) {
            return new Float32Array(vector.length);
        }
        const normalized = new Float32Array(vector.length);
        for (let i = 0; i < vector.length; i++) {
            normalized[i] = vector[i] / mag;
        }
        return normalized;
    }
    /**
     * Store multiple embeddings in batch
     */
    async storeBatchEmbeddings(embeddings) {
        const results = [];
        const batchSize = 100; // Process in batches to avoid memory issues
        for (let i = 0; i < embeddings.length; i += batchSize) {
            const batch = embeddings.slice(i, i + batchSize);
            const batchPromises = batch.map(async ({ chunkId, embedding, model }) => {
                // Convert Float32Array to PostgreSQL array format
                const vectorString = '{' + Array.from(embedding).join(',') + '}';
                const id = (0, uuid_1.v4)();
                const now = new Date().toISOString();
                await this.client.query('INSERT INTO embeddings (id, chunk_id, vector, model_name, dimension, created_at) VALUES ($1, $2, $3, $4, $5, $6)', [id, chunkId, vectorString, model || 'text-embedding-3-small', embedding.length, now]);
                return {
                    id,
                    chunkId,
                    model: model || 'text-embedding-3-small',
                    embedding,
                    dimensions: embedding.length,
                    createdAt: new Date(now)
                };
            });
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            catch (error) {
                console.error(`Error in batch ${i / batchSize + 1}:`, error);
                // Continue with next batch
            }
        }
        return results;
    }
    /**
     * Get embedding by chunk ID
     */
    async getEmbeddingByChunkId(chunkId, model) {
        try {
            let query = 'SELECT * FROM embeddings WHERE chunk_id = $1';
            const params = [chunkId];
            if (model) {
                query += ' AND model_name = $2';
                params.push(model);
            }
            const result = await this.client.query(query, params);
            const embedding = result.rows[0];
            if (!embedding) {
                return null;
            }
            // Parse PostgreSQL array format
            const vectorString = embedding.vector;
            const vectorArray = new Float32Array(vectorString
                .slice(1, -1) // Remove { and }
                .split(',')
                .map(Number));
            return {
                id: embedding.id,
                chunkId: embedding.chunk_id,
                model: embedding.model_name,
                embedding: vectorArray,
                dimensions: embedding.dimension,
                createdAt: new Date(embedding.created_at)
            };
        }
        catch (error) {
            console.error('Error getting embedding by chunk ID:', error);
            return null;
        }
    }
    /**
     * Delete embedding by chunk ID
     */
    async deleteEmbedding(chunkId, model) {
        try {
            let query = 'DELETE FROM embeddings WHERE chunk_id = $1';
            const params = [chunkId];
            if (model) {
                query += ' AND model_name = $2';
                params.push(model);
            }
            await this.client.query(query, params);
            return true;
        }
        catch (error) {
            console.error('Error deleting embedding:', error);
            return false;
        }
    }
    /**
     * Cleanup resources
     */
    async disconnect() {
        if (this.client) {
            await this.client.end();
        }
    }
}
exports.PostgreSQLRAGService = PostgreSQLRAGService;
exports.default = PostgreSQLRAGService;
