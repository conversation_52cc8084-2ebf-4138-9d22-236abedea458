/**
 * Test-Script für Schema-Vergleich
 * Vergleicht die Tabellenstruktur in der Datenbank mit dem Drizzle-Schema
 */

const { Client } = require('pg');

// Datenbankkonfiguration
const dbConfig = {
  host: 'localhost',
  port: 5434,
  user: 'leitstand_dashboard',
  password: 'dashboard_password',
  database: 'leitstand_dashboard',
  ssl: false
};

async function testSchemaComparison() {
  console.log('🔍 Teste Schema-Vergleich...\n');

  const client = new Client(dbConfig);

  try {
    console.log('🔌 Verbinde mit Datenbank...');
    await client.connect();
    console.log('✅ Verbindung erfolgreich hergestellt\n');

    // 1. Zeige Struktur der bereitschafts_personen Tabelle
    console.log('📋 Struktur der bereitschafts_personen Tabelle:');
    const personenStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'bereitschafts_personen'
      ORDER BY ordinal_position;
    `);

    if (personenStructure.rows.length > 0) {
      console.log('  Spalten:');
      personenStructure.rows.forEach(row => {
        console.log(`    - ${row.column_name} (${row.data_type}, nullable: ${row.is_nullable})`);
      });
    } else {
      console.log('  ❌ Keine Spalten gefunden');
    }

    console.log('\n');

    // 2. Zeige Struktur der bereitschafts_wochen Tabelle
    console.log('📋 Struktur der bereitschafts_wochen Tabelle:');
    const wochenStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'bereitschafts_wochen'
      ORDER BY ordinal_position;
    `);

    if (wochenStructure.rows.length > 0) {
      console.log('  Spalten:');
      wochenStructure.rows.forEach(row => {
        console.log(`    - ${row.column_name} (${row.data_type}, nullable: ${row.is_nullable})`);
      });
    } else {
      console.log('  ❌ Keine Spalten gefunden');
    }

    console.log('\n');

    // 3. Zeige Struktur der bereitschafts_konfiguration Tabelle
    console.log('📋 Struktur der bereitschafts_konfiguration Tabelle:');
    const konfigurationStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'bereitschafts_konfiguration'
      ORDER BY ordinal_position;
    `);

    if (konfigurationStructure.rows.length > 0) {
      console.log('  Spalten:');
      konfigurationStructure.rows.forEach(row => {
        console.log(`    - ${row.column_name} (${row.data_type}, nullable: ${row.is_nullable})`);
      });
    } else {
      console.log('  ❌ Keine Spalten gefunden');
    }

    console.log('\n');

    // 4. Teste eine einfache Abfrage
    console.log('🧪 Teste einfache Abfrage auf bereitschafts_personen:');
    try {
      const testQuery = await client.query('SELECT id, name, aktiv FROM bereitschafts_personen LIMIT 1');
      console.log('✅ Abfrage erfolgreich:', testQuery.rows);
    } catch (error) {
      console.log('❌ Abfrage fehlgeschlagen:', error.message);
    }

    console.log('\n');

    // 5. Teste eine Abfrage mit WHERE-Klausel
    console.log('🧪 Teste Abfrage mit WHERE-Klausel:');
    try {
      const testQuery2 = await client.query('SELECT id, name FROM bereitschafts_personen WHERE aktiv = true LIMIT 1');
      console.log('✅ WHERE-Abfrage erfolgreich:', testQuery2.rows);
    } catch (error) {
      console.log('❌ WHERE-Abfrage fehlgeschlagen:', error.message);
    }

  } catch (error) {
    console.error('❌ Fehler beim Testen des Schemas:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await client.end();
    console.log('\n🔌 Datenbankverbindung geschlossen');
  }
}

// Führe Tests aus
testSchemaComparison();