import apiService from './api.service';
import {
  BereitschaftsPerson,
  BereitschaftsWoche,
  BereitschaftsAusnahme,
  BereitschaftsKonfiguration,
  BereitschaftsStatistiken,
  EskalationsRegel,
  CreateBereitschaftsPersonRequest,
  UpdateBereitschaftsPersonRequest,
  CreateBereitschaftsWocheRequest,
  UpdateBereitschaftsWocheRequest,
  CreateBereitschaftsAusnahmeRequest,
  UpdateBereitschaftsAusnahmeRequest,
  UpdateBereitschaftsKonfigurationRequest,
  GenerateWochenplanRequest,
  UpdatePersonenReihenfolgeRequest,
  CreateEskalationsRegelRequest,
  UpdateEskalationsRegelRequest
} from '@/types/bereitschafts';

class BereitschaftsService {
  private baseUrl = '/bereitschafts';

  // Helper method for making API requests
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      if (options?.method === 'POST') {
        return await apiService.post<T>(url, options.body ? JSON.parse(options.body as string) : undefined);
      } else if (options?.method === 'PUT') {
        return await apiService.put<T>(url, options.body ? JSON.parse(options.body as string) : undefined);
      } else if (options?.method === 'DELETE') {
        return await apiService.delete<T>(url);
      } else {
        return await apiService.get<T>(url);
      }
    } catch (error) {
      console.error(`Bereitschafts API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Personen-Management
  async getAllPersonen(): Promise<BereitschaftsPerson[]> {
    try {
      return await this.request<BereitschaftsPerson[]>('/personen');
    } catch (error) {
      console.error('Error fetching bereitschafts personen:', error);
      return [];
    }
  }

  async getPersonById(id: number): Promise<BereitschaftsPerson> {
    try {
      return await this.request<BereitschaftsPerson>(`/personen/${id}`);
    } catch (error) {
      console.error(`Error fetching bereitschafts person ${id}:`, error);
      throw error;
    }
  }

  async createPerson(data: CreateBereitschaftsPersonRequest): Promise<BereitschaftsPerson> {
    try {
      return await this.request<BereitschaftsPerson>('/personen', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Error creating bereitschafts person:', error);
      throw error;
    }
  }

  async updatePerson(id: number, data: UpdateBereitschaftsPersonRequest): Promise<BereitschaftsPerson> {
    try {
      return await this.request<BereitschaftsPerson>(`/personen/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error(`Error updating bereitschafts person ${id}:`, error);
      throw error;
    }
  }

  async deletePerson(id: number): Promise<void> {
    try {
      await this.request<void>(`/personen/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Error deleting bereitschafts person ${id}:`, error);
      throw error;
    }
  }

  async updatePersonenReihenfolge(data: UpdatePersonenReihenfolgeRequest): Promise<BereitschaftsPerson[]> {
    try {
      return await this.request<BereitschaftsPerson[]>('/personen/reihenfolge', {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Error updating personen reihenfolge:', error);
      throw error;
    }
  }

  // Wochenplan-Management
  async getWochenplan(startDate?: string, anzahlWochen?: number): Promise<BereitschaftsWoche[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (anzahlWochen) params.append('anzahlWochen', anzahlWochen.toString());

      const query = params.toString() ? `?${params.toString()}` : '';
      return await this.request<BereitschaftsWoche[]>(`/wochenplan${query}`);
    } catch (error) {
      console.error('Error fetching wochenplan:', error);
      return [];
    }
  }

  async getAllWochen(startDate?: string, anzahlWochen?: number): Promise<BereitschaftsWoche[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (anzahlWochen) params.append('anzahlWochen', anzahlWochen.toString());

      const query = params.toString() ? `?${params.toString()}` : '';
      return await this.request<BereitschaftsWoche[]>(`/wochenplan/all${query}`);
    } catch (error) {
      console.error('Error fetching all wochen:', error);
      return [];
    }
  }

  async getAktuelleBereitschaft(): Promise<BereitschaftsWoche | null> {
    try {
      return await this.request<BereitschaftsWoche | null>('/aktuelle-bereitschaft');
    } catch (error) {
      console.error('Error fetching aktuelle bereitschaft:', error);
      return null;
    }
  }

  async generiereWochenplan(data: GenerateWochenplanRequest): Promise<BereitschaftsWoche[]> {
    try {
      return await this.request<BereitschaftsWoche[]>('/wochenplan/generieren', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Error generating wochenplan:', error);
      throw error;
    }
  }

  async updateWoche(id: number, data: UpdateBereitschaftsWocheRequest): Promise<BereitschaftsWoche> {
    try {
      return await this.request<BereitschaftsWoche>(`/wochenplan/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error(`Error updating woche ${id}:`, error);
      throw error;
    }
  }

  async deleteWoche(id: number): Promise<void> {
    try {
      await this.request<void>(`/wochenplan/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Error deleting woche ${id}:`, error);
      throw error;
    }
  }

  // Ausnahmen-Management
  async getAllAusnahmen(): Promise<BereitschaftsAusnahme[]> {
    try {
      return await this.request<BereitschaftsAusnahme[]>('/ausnahmen');
    } catch (error) {
      console.error('Error fetching ausnahmen:', error);
      return [];
    }
  }

  async createAusnahme(data: CreateBereitschaftsAusnahmeRequest): Promise<BereitschaftsAusnahme> {
    try {
      return await this.request<BereitschaftsAusnahme>('/ausnahmen', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Error creating ausnahme:', error);
      throw error;
    }
  }

  async updateAusnahme(id: number, data: UpdateBereitschaftsAusnahmeRequest): Promise<BereitschaftsAusnahme> {
    try {
      return await this.request<BereitschaftsAusnahme>(`/ausnahmen/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error(`Error updating ausnahme ${id}:`, error);
      throw error;
    }
  }

  async deleteAusnahme(id: number): Promise<void> {
    try {
      await this.request<void>(`/ausnahmen/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Error deleting ausnahme ${id}:`, error);
      throw error;
    }
  }

  // Konfiguration-Management
  async getKonfiguration(): Promise<BereitschaftsKonfiguration> {
    try {
      return await this.request<BereitschaftsKonfiguration>('/konfiguration');
    } catch (error) {
      console.error('Error fetching konfiguration:', error);
      throw error;
    }
  }

  async updateKonfiguration(data: UpdateBereitschaftsKonfigurationRequest): Promise<BereitschaftsKonfiguration> {
    try {
      return await this.request<BereitschaftsKonfiguration>('/konfiguration', {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Error updating konfiguration:', error);
      throw error;
    }
  }

  // Eskalationsmatrix-Management
  async getEskalationsregeln(): Promise<EskalationsRegel[]> {
    try {
      return await this.request<EskalationsRegel[]>('/eskalationsregeln');
    } catch (error) {
      console.error('Error fetching Eskalationsregeln:', error);
      return [];
    }
  }

  async createEskalationsregel(data: CreateEskalationsRegelRequest): Promise<EskalationsRegel> {
    try {
      return await this.request<EskalationsRegel>('/eskalationsregeln', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Error creating Eskalationsregel:', error);
      throw error;
    }
  }

  async updateEskalationsregel(id: number, data: UpdateEskalationsRegelRequest): Promise<EskalationsRegel> {
    try {
      return await this.request<EskalationsRegel>(`/eskalationsregeln/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error(`Error updating Eskalationsregel ${id}:`, error);
      throw error;
    }
  }

  async deleteEskalationsregel(id: number): Promise<void> {
    try {
      await this.request<void>(`/eskalationsregeln/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error(`Error deleting Eskalationsregel ${id}:`, error);
      throw error;
    }
  }

  // Erweiterte Funktionen
  async getBereitschaftsplanMitAusnahmen(startDate?: string, anzahlWochen?: number): Promise<BereitschaftsWoche[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (anzahlWochen) params.append('anzahlWochen', anzahlWochen.toString());
      
      const query = params.toString() ? `?${params.toString()}` : '';
      return await this.request<BereitschaftsWoche[]>(`/plan-mit-ausnahmen${query}`);
    } catch (error) {
      console.error('Error fetching bereitschaftsplan mit ausnahmen:', error);
      return [];
    }
  }

  async getBereitschaftsStatistiken(startDate?: string, endDate?: string): Promise<BereitschaftsStatistiken> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      
      const query = params.toString() ? `?${params.toString()}` : '';
      return await this.request<BereitschaftsStatistiken>(`/statistiken${query}`);
    } catch (error) {
      console.error('Error fetching bereitschafts statistiken:', error);
      throw error;
    }
  }
}

export const bereitschaftsService = new BereitschaftsService();