import { Request, Response } from 'express';
import { db } from '../db';
import { runbook } from '../db/schema';
import { desc, eq, like, or, sql } from 'drizzle-orm';

// Hilfsfunktion zum sicheren Parsen von JSON
function safeJsonParse(jsonString: string | null): any[] {
  if (!jsonString) return [];
  try {
    const parsed = JSON.parse(jsonString);
    return Array.isArray(parsed) ? parsed : [];
  } catch (e) {
    console.error('[RunbookController] <PERSON><PERSON> beim <PERSON> von JSON:', e);
    return [];
  }
}

// Hilfsfunktion zum Prüfen, ob die steps-Spalte existiert
let stepsColumnExists: boolean | null = null;

async function hasStepsColumn(): Promise<boolean> {
  if (stepsColumnExists !== null) {
    return stepsColumnExists;
  }

  try {
    // Versuche eine einfache Query mit der steps-Spalte
    await db.execute(sql`SELECT 1 FROM ${runbook} LIMIT 1`);
    stepsColumnExists = true;
    return true;
  } catch (error) {
    // Wenn die Spalte nicht existiert, wird ein Fehler geworfen
    console.log('[RunbookController] Steps-Spalte existiert noch nicht, verwende content-Fallback');
    stepsColumnExists = false;
    return false;
  }
}

// Hilfsfunktion zum Parsen von Schritten aus Markdown-Content
function parseStepsFromContent(content: string): any[] {
  const steps: any[] = [];
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const stepMatch = line.match(/^(\*\*)?Schritt (\d+):(\*\*)?(.*)/i);

    if (stepMatch) {
      const stepNumber = parseInt(stepMatch[2], 10);
      const description = stepMatch[4].trim();

      if (description) {
        steps.push({
          id: `step-${Date.now()}-${i}`,
          description: description,
          order: stepNumber
        });
      }
    }
  }

  return steps.sort((a, b) => a.order - b.order);
}

export class RunbookController {
  /**
   * GET /api/runbooks
   * Holt alle Runbooks
   */
  async getAllRunbooks(req: Request, res: Response): Promise<void> {
    try {
      const hasSteps = await hasStepsColumn();

      const runbooks = await db.select().from(runbook).orderBy(desc(runbook.updated_at));

      const formattedRunbooks = runbooks.map(rb => {
        const steps = hasSteps && (rb as any).steps ? safeJsonParse((rb as any).steps) : parseStepsFromContent(rb.content);
        return {
          id: rb.id,
          title: rb.title,
          content: rb.content,
          affected_systems: safeJsonParse(rb.affected_systems),
          category: safeJsonParse(rb.category),
          steps: steps,
          created_at: rb.created_at,
          updated_at: rb.updated_at
        };
      });

      res.json({
        success: true,
        data: formattedRunbooks
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Laden der Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden der Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/runbooks/:id
   * Holt ein spezifisches Runbook
   */
  async getRunbookById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const runbookId = parseInt(id, 10);

      if (isNaN(runbookId)) {
        res.status(400).json({
          success: false,
          error: 'Ungültige Runbook-ID'
        });
        return;
      }

      const runbooks = await db.select().from(runbook).where(eq(runbook.id, runbookId));
      const foundRunbook = runbooks[0];

      if (!foundRunbook) {
        res.status(404).json({
          success: false,
          error: 'Runbook nicht gefunden'
        });
        return;
      }

      const hasSteps = await hasStepsColumn();
      const steps = hasSteps && (foundRunbook as any).steps ? safeJsonParse((foundRunbook as any).steps) : parseStepsFromContent(foundRunbook.content);

      const formattedRunbook = {
        id: foundRunbook.id,
        title: foundRunbook.title,
        content: foundRunbook.content,
        affected_systems: safeJsonParse(foundRunbook.affected_systems),
        category: safeJsonParse(foundRunbook.category),
        steps: steps,
        created_at: foundRunbook.created_at,
        updated_at: foundRunbook.updated_at
      };

      res.json({
        success: true,
        data: formattedRunbook
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Laden des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * POST /api/runbooks
   * Erstellt ein neues Runbook
   */
  async createRunbook(req: Request, res: Response): Promise<void> {
    try {
      const { title, content, affected_systems, tags, steps } = req.body;

      if (!title || !content) {
        res.status(400).json({
          success: false,
          error: 'Titel und Inhalt sind erforderlich'
        });
        return;
      }

      const now = new Date().toISOString();
      const hasSteps = await hasStepsColumn();

      const insertData: any = {
        title: title.trim(),
        content: content.trim(),
        affected_systems: affected_systems ? JSON.stringify(affected_systems) : null,
        tags: tags ? JSON.stringify(tags) : null,
        created_at: now,
        updated_at: now
      };

      if (hasSteps && steps) {
        insertData.steps = JSON.stringify(steps);
      }

      const insertResult = await db.insert(runbook).values(insertData).returning();

      const createdRunbook = insertResult[0];
      const formattedRunbook = {
        id: createdRunbook.id,
        title: createdRunbook.title,
        content: createdRunbook.content,
        affected_systems: safeJsonParse(createdRunbook.affected_systems),
        category: safeJsonParse(createdRunbook.category),
        created_at: createdRunbook.created_at,
        updated_at: createdRunbook.updated_at
      };

      res.status(201).json({
        success: true,
        data: formattedRunbook
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Erstellen des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Erstellen des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * PUT /api/runbooks/:id
   * Aktualisiert ein bestehendes Runbook
   */
  async updateRunbook(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const runbookId = parseInt(id, 10);
      const { title, content, affected_systems, tags, steps } = req.body;

      if (isNaN(runbookId)) {
        res.status(400).json({
          success: false,
          error: 'Ungültige Runbook-ID'
        });
        return;
      }

      const existingRunbooks = await db.select().from(runbook).where(eq(runbook.id, runbookId));
      const existingRunbook = existingRunbooks[0];

      if (!existingRunbook) {
        res.status(404).json({
          success: false,
          error: 'Runbook nicht gefunden'
        });
        return;
      }

      const hasSteps = await hasStepsColumn();
      const updateData: any = {
        title: title?.trim() || existingRunbook.title,
        content: content?.trim() || existingRunbook.content,
        affected_systems: affected_systems ? JSON.stringify(affected_systems) : existingRunbook.affected_systems,
        category: tags ? JSON.stringify(tags) : existingRunbook.category,
        updated_at: new Date().toISOString()
      };

      if (hasSteps) {
        updateData.steps = steps ? JSON.stringify(steps) : (existingRunbook as any).steps;
      }

      const updateResult = await db.update(runbook)
        .set(updateData)
        .where(eq(runbook.id, runbookId))
        .returning();

      const updatedRunbook = updateResult[0];
      const formattedRunbook = {
        id: updatedRunbook.id,
        title: updatedRunbook.title,
        content: updatedRunbook.content,
        affected_systems: safeJsonParse(updatedRunbook.affected_systems),
        category: safeJsonParse(updatedRunbook.category),
        created_at: updatedRunbook.created_at,
        updated_at: updatedRunbook.updated_at
      };

      res.json({
        success: true,
        data: formattedRunbook
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Aktualisieren des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Aktualisieren des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * DELETE /api/runbooks/:id
   * Löscht ein Runbook
   */
  async deleteRunbook(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const runbookId = parseInt(id, 10);

      if (isNaN(runbookId)) {
        res.status(400).json({
          success: false,
          error: 'Ungültige Runbook-ID'
        });
        return;
      }

      const existingRunbooks = await db.select().from(runbook).where(eq(runbook.id, runbookId));
      const existingRunbook = existingRunbooks[0];

      if (!existingRunbook) {
        res.status(404).json({
          success: false,
          error: 'Runbook nicht gefunden'
        });
        return;
      }

      await db.delete(runbook).where(eq(runbook.id, runbookId));

      res.json({
        success: true,
        message: 'Runbook erfolgreich gelöscht'
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Löschen des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Löschen des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/runbooks/search
   * Sucht nach Runbooks basierend auf Query-Parameter
   */
  async searchRunbooks(req: Request, res: Response): Promise<void> {
    try {
      const { q } = req.query;
      
      if (!q || typeof q !== 'string') {
        res.status(400).json({
          success: false,
          error: 'Suchbegriff ist erforderlich'
        });
        return;
      }

      const searchTerm = q.trim().toLowerCase();

      const runbooks = await db.select().from(runbook)
        .where(
          or(
            like(runbook.title, `%${searchTerm}%`),
            like(runbook.content, `%${searchTerm}%`),
            like(runbook.category, `%${searchTerm}%`)
          )
        )
        .orderBy(desc(runbook.updated_at));

      const formattedRunbooks = runbooks.map(rb => {
        const hasSteps = stepsColumnExists !== null ? stepsColumnExists : true; // Assume true for search
        const steps = hasSteps && (rb as any).steps ? safeJsonParse((rb as any).steps) : parseStepsFromContent(rb.content);
        return {
          id: rb.id,
          title: rb.title,
          content: rb.content,
          affected_systems: safeJsonParse(rb.affected_systems),
          category: safeJsonParse(rb.category),
          steps: steps,
          created_at: rb.created_at,
          updated_at: rb.updated_at
        };
      });

      res.json({
        success: true,
        data: formattedRunbooks,
        count: formattedRunbooks.length
      });
    } catch (error) {
      console.error('[RunbookController] Fehler bei der Runbook-Suche:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler bei der Runbook-Suche',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}

export default RunbookController;