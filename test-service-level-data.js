/**
 * Test-Script für Service-Level-Daten
 * Überprüft die Datenbankinhalt und API-Endpunkte
 */

const axios = require('axios');

// Backend API Base URL
const API_BASE_URL = 'http://localhost:3001/api';

async function testServiceLevelData() {
  console.log('🔍 Teste Service-Level-Daten...\n');

  try {
    // 1. Test ohne Datumsfilter
    console.log('1️⃣ Teste API ohne Datumsfilter...');
    const response1 = await axios.get(`${API_BASE_URL}/database/service-level`);
    console.log('✅ API-Antwort erhalten:', {
      status: response1.status,
      dataLength: response1.data?.data?.length || 0,
      firstFew: response1.data?.data?.slice(0, 3) || []
    });

    // 2. Test mit dem Zeitraum aus den Logs (2025-07-03 bis 2025-07-30)
    console.log('\n2️⃣ Teste API mit Zeitraum 2025-07-03 bis 2025-07-30...');
    const response2 = await axios.get(`${API_BASE_URL}/database/service-level`, {
      params: {
        startDate: '2025-07-03',
        endDate: '2025-07-30'
      }
    });
    console.log('✅ API-Antwort erhalten:', {
      status: response2.status,
      dataLength: response2.data?.data?.length || 0,
      firstFew: response2.data?.data?.slice(0, 3) || []
    });

    // 3. Test mit aktuellem Datum
    console.log('\n3️⃣ Teste API mit aktuellem Datum...');
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const response3 = await axios.get(`${API_BASE_URL}/database/service-level`, {
      params: {
        startDate: yesterday,
        endDate: today
      }
    });
    console.log('✅ API-Antwort erhalten:', {
      status: response3.status,
      dataLength: response3.data?.data?.length || 0,
      firstFew: response3.data?.data?.slice(0, 3) || []
    });

    // 4. Teste andere Endpunkte für Vergleich
    console.log('\n4️⃣ Teste andere Endpunkte für Vergleich...');

    const endpoints = [
      '/database/tagesleistung',
      '/database/picking',
      '/database/delivery-positions'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${API_BASE_URL}${endpoint}`, {
          params: {
            startDate: '2025-07-03',
            endDate: '2025-07-30'
          }
        });
        console.log(`✅ ${endpoint}: ${response.data?.data?.length || 0} Datensätze`);
      } catch (error) {
        console.log(`❌ ${endpoint}: Fehler - ${error.message}`);
      }
    }

    // 5. Teste Health-Check
    console.log('\n5️⃣ Teste Health-Check...');
    const healthResponse = await axios.get(`${API_BASE_URL}/database/health`);
    console.log('✅ Health-Status:', healthResponse.data);

  } catch (error) {
    console.error('❌ Fehler beim Testen:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

// Führe Test aus
testServiceLevelData();