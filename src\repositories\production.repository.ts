/**
 * Production Repository Interfaces
 *
 * Type definitions for production data management
 */

/**
 * Filter-Interface für Datum-basierte Repositories
 */
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

/**
 * Filter für Produktionsdaten
 */
export interface ProductionFilter extends DateRangeFilter {
  machineId?: string;
  productionType?: 'cutting' | 'ablängerei' | 'efficiency';
  minEfficiency?: number;
  maxEfficiency?: number;
}

export interface SchnitteData {
  Datum?: string;
  datum?: string;
  [key: string]: any; // Für dynamische Maschinenspalten
}

export interface SchnitteSummary {
  date: string;
  totalH1: number;
  totalH3: number;
  grandTotal: number;
}

export interface MachinePerformance {
  machine: string;
  totalCuts: number;
  avgPerDay: number;
  efficiency: number;
}

export interface MachineEfficiencyData {
  Datum: string;
  Machine: string;
  sollSchnitte: number;
  sollSchnitteProTag: number;
  tagesSchnitte: number;
  istSchnitteProStunde: number;
  effizienzProzent: number;
}

export interface EfficiencyStats {
  average: number;
  total: number;
  aboveAverage: number;
  belowAverage: number;
}

export interface AblaengereiStats {
  date: string;
  total220: number;
  total240: number;
  total200: number;
  totalCuts: number;
  pickCut: number;
  cutTT: number;
  cutTR: number;
  cutRR: number;
}

export interface WarehousePerformance {
  warehouse220: { cuts: number; percentage: number };
  warehouse240: { cuts: number; percentage: number };
  warehouse200: { cuts: number; percentage: number };
  totalCuts: number;
}

export interface CuttingTrendAnalysis {
  trend: 'increasing' | 'decreasing' | 'stable';
  changePercentage: number;
  averageDaily: number;
  projectedNext: number;
}

export interface WarehouseDistribution {
  byWarehouse: Record<string, number>;
  totalCuts: number;
  mostActiveWarehouse: string;
  efficiency: number;
}

export interface ProductionStats {
  dailyAverages: SchnitteSummary[];
  machineEfficiency: EfficiencyStats;
  warehousePerformance: WarehousePerformance;
  trends: CuttingTrendAnalysis;
}

/**
 * Schnitte Repository Interface
 */
export interface ISchnitteRepository {
  getAll(filter?: DateRangeFilter): Promise<SchnitteData[]>;
  getGroupedByDate(): Promise<Record<string, SchnitteData[]>>;
  getDailySums(): Promise<SchnitteSummary[]>;
  getMachinePerformance(): Promise<MachinePerformance[]>;
  invalidateCache(): void;
}

/**
 * Maschinen Efficiency Repository Interface
 */
export interface IMaschinenEfficiencyRepository {
  getAll(filter?: ProductionFilter): Promise<MachineEfficiencyData[]>;
  getTopPerformers(limit?: number): Promise<MachineEfficiencyData[]>;
  getLowPerformers(threshold?: number): Promise<MachineEfficiencyData[]>;
  getAverageEfficiency(): Promise<EfficiencyStats>;
  invalidateCache(): void;
}

/**
 * Ablaengerei Repository Interface
 */
export interface IAblaengereiRepository {
  getAll(filter?: DateRangeFilter): Promise<AblaengereiDataPoint[]>;
  getDailyStats(): Promise<AblaengereiStats[]>;
  getWarehousePerformance(): Promise<WarehousePerformance>;
  invalidateCache(): void;
}

/**
 * Cutting Chart Repository Interface
 */
export interface ICuttingChartRepository {
  getAll(filter?: DateRangeFilter): Promise<any[]>;
  getTrendAnalysis(days?: number): Promise<CuttingTrendAnalysis>;
  invalidateCache(): void;
}

/**
 * Lager Cuts Repository Interface
 */
export interface ILagerCutsRepository {
  getAll(filter?: DateRangeFilter): Promise<any[]>;
  getDistributionAnalysis(): Promise<WarehouseDistribution>;
  invalidateCache(): void;
}

/**
 * Production Repository Interface
 */
export interface IProductionRepository {
  schnitte: ISchnitteRepository;
  maschinenEfficiency: IMaschinenEfficiencyRepository;
  ablaengerei: IAblaengereiRepository;
  cuttingChart: ICuttingChartRepository;
  lagerCuts: ILagerCutsRepository;

  invalidateAllCache(): void;
  getOverallProductionStats(): Promise<ProductionStats>;
}

/**
 * Base Repository for Production Data
 */
import { BaseRepository } from './base.repository';
import { apiService } from '../services/api.service';
import { AblaengereiDataPoint } from '@/types/database';

/**
 * Schnitte Repository Implementation
 */
class SchnitteRepositoryImpl extends BaseRepository<SchnitteData, DateRangeFilter> implements ISchnitteRepository {
  protected repositoryName = 'ProductionSchnitteRepository';

  async getAll(filter?: DateRangeFilter): Promise<SchnitteData[]> {
    try {
      const data = await apiService.getSchnitteData();
      if (filter) {
        return data.filter(item => {
          if (filter.startDate && item.datum && item.datum < filter.startDate) return false;
          if (filter.endDate && item.datum && item.datum > filter.endDate) return false;
          return true;
        });
      }
      return data;
    } catch (error) {
      console.error('Error fetching schnitte data:', error);
      throw error;
    }
  }

  async getGroupedByDate(): Promise<Record<string, SchnitteData[]>> {
    try {
      const data = await this.getAll();
      const grouped: Record<string, SchnitteData[]> = {};

      data.forEach(item => {
        const date = item.datum || item.Datum || 'unknown';
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(item);
      });

      return grouped;
    } catch (error) {
      console.error('Error grouping schnitte data by date:', error);
      throw error;
    }
  }

  async getDailySums(): Promise<SchnitteSummary[]> {
    try {
      const grouped = await this.getGroupedByDate();
      const summaries: SchnitteSummary[] = [];

      for (const [date, items] of Object.entries(grouped)) {
        const totalH1 = items.reduce((sum, item) => sum + (item.H1 || 0), 0);
        const totalH3 = items.reduce((sum, item) => sum + (item.H3 || 0), 0);
        const grandTotal = totalH1 + totalH3;

        summaries.push({
          date,
          totalH1,
          totalH3,
          grandTotal
        });
      }

      return summaries.sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error('Error calculating daily sums:', error);
      throw error;
    }
  }

  async getMachinePerformance(): Promise<MachinePerformance[]> {
    try {
      const data = await this.getAll();
      const machineMap = new Map<string, { total: number; count: number }>();

      data.forEach(item => {
        Object.keys(item).forEach(key => {
          if (key !== 'datum' && key !== 'Datum' && typeof item[key] === 'number') {
            const machine = key;
            const value = item[key] as number;
            if (!machineMap.has(machine)) {
              machineMap.set(machine, { total: 0, count: 0 });
            }
            const machineData = machineMap.get(machine)!;
            machineData.total += value;
            machineData.count += 1;
          }
        });
      });

      const performances: MachinePerformance[] = [];
      for (const [machine, data] of machineMap.entries()) {
        performances.push({
          machine,
          totalCuts: data.total,
          avgPerDay: data.total / data.count,
          efficiency: (data.total / data.count) / 100 * 100 // Mock efficiency calculation
        });
      }

      return performances.sort((a, b) => b.totalCuts - a.totalCuts);
    } catch (error) {
      console.error('Error calculating machine performance:', error);
      throw error;
    }
  }
}

/**
 * Maschinen Efficiency Repository Implementation
 */
class MaschinenEfficiencyRepositoryImpl extends BaseRepository<MachineEfficiencyData, ProductionFilter> implements IMaschinenEfficiencyRepository {
  protected repositoryName = 'ProductionMaschinenEfficiencyRepository';

  async getAll(filter?: ProductionFilter): Promise<MachineEfficiencyData[]> {
    try {
      const data = await apiService.getMaschinenEfficiency();
      if (filter) {
        return data.filter(item => {
          if (filter.machineId && item.Machine !== filter.machineId) return false;
          if (filter.minEfficiency && item.effizienzProzent < filter.minEfficiency) return false;
          if (filter.maxEfficiency && item.effizienzProzent > filter.maxEfficiency) return false;
          if (filter.startDate && item.Datum < filter.startDate) return false;
          if (filter.endDate && item.Datum > filter.endDate) return false;
          return true;
        });
      }
      return data;
    } catch (error) {
      console.error('Error fetching machine efficiency data:', error);
      throw error;
    }
  }

  async getTopPerformers(limit: number = 10): Promise<MachineEfficiencyData[]> {
    try {
      const data = await this.getAll();
      return data
        .sort((a, b) => b.effizienzProzent - a.effizienzProzent)
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching top performers:', error);
      throw error;
    }
  }

  async getLowPerformers(threshold: number = 80): Promise<MachineEfficiencyData[]> {
    try {
      const data = await this.getAll();
      return data.filter(item => item.effizienzProzent < threshold);
    } catch (error) {
      console.error('Error fetching low performers:', error);
      throw error;
    }
  }

  async getAverageEfficiency(): Promise<EfficiencyStats> {
    try {
      const data = await this.getAll();
      const efficiencies = data.map(item => item.effizienzProzent);

      if (efficiencies.length === 0) {
        return {
          average: 0,
          total: 0,
          aboveAverage: 0,
          belowAverage: 0
        };
      }

      const average = efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
      const aboveAverage = efficiencies.filter(eff => eff > average).length;
      const belowAverage = efficiencies.filter(eff => eff < average).length;

      return {
        average,
        total: efficiencies.length,
        aboveAverage,
        belowAverage
      };
    } catch (error) {
      console.error('Error calculating average efficiency:', error);
      throw error;
    }
  }
}

/**
 * Ablaengerei Repository Implementation
 */
class AblaengereiRepositoryImpl extends BaseRepository<AblaengereiDataPoint, DateRangeFilter> implements IAblaengereiRepository {
  protected repositoryName = 'ProductionAblaengereiRepository';

  async getAll(filter?: DateRangeFilter): Promise<AblaengereiDataPoint[]> {
    try {
      const data = await apiService.getAblaengereiData(filter?.startDate, filter?.endDate);
      return data;
    } catch (error) {
      console.error('Error fetching ablaengerei data:', error);
      throw error;
    }
  }

  async getDailyStats(): Promise<AblaengereiStats[]> {
    try {
      const data = await this.getAll();
      const dailyMap = new Map<string, AblaengereiStats>();

      data.forEach(item => {
        const date = item.datum;
        if (!dailyMap.has(date)) {
          dailyMap.set(date, {
            date,
            total220: 0,
            total240: 0,
            total200: 0,
            totalCuts: 0,
            pickCut: 0,
            cutTT: 0,
            cutTR: 0,
            cutRR: 0
          });
        }

        const stats = dailyMap.get(date)!;
        stats.total220 += item.lagerCut220 || item.cutLagerK220 || item.cutLagerR220 || 0;
        stats.total240 += item.lagerCut240 || item.cutLagerK240 || item.cutLagerR240 || 0;
        stats.total200 += item.lagerCut200 || item.cutLagerK200 || item.cutLager200 || 0;
        stats.totalCuts += (item.lagerCut220 || item.cutLagerK220 || item.cutLagerR220 || 0) +
                          (item.lagerCut240 || item.cutLagerK240 || item.cutLagerR240 || 0) +
                          (item.lagerCut200 || item.cutLagerK200 || item.cutLager200 || 0);
        stats.pickCut += item.pickCut || 0;
        stats.cutTT += item.cutTT || 0;
        stats.cutTR += item.cutTR || 0;
        stats.cutRR += item.cutRR || 0;
      });

      return Array.from(dailyMap.values()).sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error('Error calculating daily ablaengerei stats:', error);
      throw error;
    }
  }

  async getWarehousePerformance(): Promise<WarehousePerformance> {
    try {
      const data = await this.getAll();
      const total220 = data.reduce((sum, item) => sum + (item.lagerCut220 || item.cutLagerK220 || item.cutLagerR220 || 0), 0);
      const total240 = data.reduce((sum, item) => sum + (item.lagerCut240 || item.cutLagerK240 || item.cutLagerR240 || 0), 0);
      const total200 = data.reduce((sum, item) => sum + (item.lagerCut200 || item.cutLagerK200 || item.cutLager200 || 0), 0);
      const totalCuts = total220 + total240 + total200;

      return {
        warehouse220: {
          cuts: total220,
          percentage: totalCuts > 0 ? (total220 / totalCuts) * 100 : 0
        },
        warehouse240: {
          cuts: total240,
          percentage: totalCuts > 0 ? (total240 / totalCuts) * 100 : 0
        },
        warehouse200: {
          cuts: total200,
          percentage: totalCuts > 0 ? (total200 / totalCuts) * 100 : 0
        },
        totalCuts
      };
    } catch (error) {
      console.error('Error calculating warehouse performance:', error);
      throw error;
    }
  }
}

/**
 * Cutting Chart Repository Implementation
 */
class CuttingChartRepositoryImpl extends BaseRepository<any, DateRangeFilter> implements ICuttingChartRepository {
  protected repositoryName = 'ProductionCuttingChartRepository';

  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    try {
      const data = await apiService.getCuttingChartData(filter?.startDate, filter?.endDate);
      return data;
    } catch (error) {
      console.error('Error fetching cutting chart data:', error);
      throw error;
    }
  }

  async getTrendAnalysis(days: number = 30): Promise<CuttingTrendAnalysis> {
    try {
      const data = await this.getAll();
      const recentData = data.slice(0, days);
      const olderData = data.slice(days, days * 2);

      const recentAvg = recentData.reduce((sum, item) => sum + (item.value || 0), 0) / recentData.length;
      const olderAvg = olderData.length > 0 ? olderData.reduce((sum, item) => sum + (item.value || 0), 0) / olderData.length : 0;

      let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
      if (recentAvg > olderAvg * 1.05) trend = 'increasing';
      else if (recentAvg < olderAvg * 0.95) trend = 'decreasing';

      const changePercentage = olderAvg > 0 ? ((recentAvg - olderAvg) / olderAvg) * 100 : 0;
      const averageDaily = recentData.reduce((sum, item) => sum + (item.value || 0), 0) / recentData.length;
      const projectedNext = averageDaily * 1.02; // Simple projection with 2% growth

      return {
        trend,
        changePercentage,
        averageDaily,
        projectedNext
      };
    } catch (error) {
      console.error('Error calculating cutting trend analysis:', error);
      throw error;
    }
  }
}

/**
 * Lager Cuts Repository Implementation
 */
class LagerCutsRepositoryImpl extends BaseRepository<any, DateRangeFilter> implements ILagerCutsRepository {
  protected repositoryName = 'ProductionLagerCutsRepository';

  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    try {
      const data = await apiService.getLagerCutsChartData(filter?.startDate, filter?.endDate);
      return data;
    } catch (error) {
      console.error('Error fetching lager cuts data:', error);
      throw error;
    }
  }

  async getDistributionAnalysis(): Promise<WarehouseDistribution> {
    try {
      const data = await this.getAll();
      const warehouseMap = new Map<string, number>();

      data.forEach(item => {
        Object.keys(item).forEach(key => {
          if (key.includes('warehouse') || key.includes('200') || key.includes('220') || key.includes('240')) {
            const warehouse = key;
            const value = item[key] as number;
            warehouseMap.set(warehouse, (warehouseMap.get(warehouse) || 0) + value);
          }
        });
      });

      const totalCuts = Array.from(warehouseMap.values()).reduce((sum, cuts) => sum + cuts, 0);
      const mostActiveWarehouse = Array.from(warehouseMap.entries())
        .sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown';

      const efficiency = totalCuts > 0 ? (totalCuts / data.length) / 100 * 100 : 0;

      return {
        byWarehouse: Object.fromEntries(warehouseMap),
        totalCuts,
        mostActiveWarehouse,
        efficiency
      };
    } catch (error) {
      console.error('Error calculating warehouse distribution analysis:', error);
      throw error;
    }
  }
}

/**
 * Production Repository Implementation
 */
export class ProductionRepository implements IProductionRepository {
  public readonly schnitte: ISchnitteRepository;
  public readonly maschinenEfficiency: IMaschinenEfficiencyRepository;
  public readonly ablaengerei: IAblaengereiRepository;
  public readonly cuttingChart: ICuttingChartRepository;
  public readonly lagerCuts: ILagerCutsRepository;

  constructor() {
    this.schnitte = new SchnitteRepositoryImpl();
    this.maschinenEfficiency = new MaschinenEfficiencyRepositoryImpl();
    this.ablaengerei = new AblaengereiRepositoryImpl();
    this.cuttingChart = new CuttingChartRepositoryImpl();
    this.lagerCuts = new LagerCutsRepositoryImpl();
  }

  invalidateAllCache(): void {
    this.schnitte.invalidateCache();
    this.maschinenEfficiency.invalidateCache();
    this.ablaengerei.invalidateCache();
    this.cuttingChart.invalidateCache();
    this.lagerCuts.invalidateCache();
  }

  async getOverallProductionStats(): Promise<ProductionStats> {
    try {
      const [
        dailyAverages,
        machineEfficiency,
        warehousePerformance,
        trends
      ] = await Promise.all([
        this.schnitte.getDailySums(),
        this.maschinenEfficiency.getAverageEfficiency(),
        this.ablaengerei.getWarehousePerformance(),
        this.cuttingChart.getTrendAnalysis()
      ]);

      return {
        dailyAverages,
        machineEfficiency,
        warehousePerformance,
        trends
      };
    } catch (error) {
      console.error('Error fetching overall production stats:', error);
      throw error;
    }
  }
}