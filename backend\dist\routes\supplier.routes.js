"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const supplier_controller_1 = require("../controllers/supplier.controller");
const express_rate_limit_1 = require("express-rate-limit");
const router = (0, express_1.Router)();
const controller = new supplier_controller_1.SupplierController();
// Rate limiting für Supplier-Routen
const supplierLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 Minuten
    max: 100, // Limit pro Window pro IP
    message: {
        success: false,
        error: '<PERSON>u viele Anfragen von dieser IP-Adresse',
        retryAfter: '15 Minuten'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
// Basis-Routen für Lieferanten
router.get('/', supplierLimiter, controller.getAllSuppliers.bind(controller));
router.get('/stats', supplierLimiter, controller.getSupplierStats.bind(controller));
router.get('/search', supplierLimiter, controller.searchSuppliers.bind(controller));
// Routen mit Parameter
router.get('/:supplierId', supplierLimiter, controller.getSupplierById.bind(controller));
router.get('/performance/:supplierId', supplierLimiter, controller.getSupplierPerformance.bind(controller));
router.get('/category/:category', supplierLimiter, controller.getSuppliersByCategory.bind(controller));
router.get('/:supplierId/history', supplierLimiter, controller.getSupplierDeliveryHistory.bind(controller));
// Performance-Routen
router.get('/top', supplierLimiter, controller.getTopPerformingSuppliers.bind(controller));
router.get('/risk', supplierLimiter, controller.getRiskSuppliers.bind(controller));
router.put('/:supplierId/performance', supplierLimiter, controller.updateSupplierPerformance.bind(controller));
exports.default = router;
