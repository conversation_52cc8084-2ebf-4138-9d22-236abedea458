/**
 * Supplier Repository Implementation
 *
 * Implementiert die SupplierRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Lieferantendaten.
 */

import { db } from '../db';
import { eq, gte, lte, desc, asc, and } from 'drizzle-orm';
import {
  DateRange,
  RepositoryStats,
  RepositoryCacheConfig
} from './interfaces';
import { getBackendCache, BackendCacheKeyGenerator } from '../services/cache.service';

// Type definitions for supplier data
export interface Supplier {
  id: string;
  name: string;
  location: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    address: string;
    contactPerson: string;
  };
  performanceMetrics: SupplierPerformanceMetrics;
}

export interface SupplierPerformanceMetrics {
  onTimeDeliveryRate: number;
  qualityRate: number;
  responseTime: number;
  financialStabilityScore: number;
  reliabilityScore: number;
  costCompetitiveness: number;
}

export interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  averagePerformanceScore: number;
  topPerformers: number;
  riskSuppliers: number;
}

export interface SupplierPerformanceData {
  supplierId: string;
  onTimeDeliveryRate: number;
  qualityRate: number;
  responseTime: number;
  financialStabilityScore: number;
  reliabilityScore: number;
  costCompetitiveness: number;
  totalOrders: number;
  totalValue: number;
  lastOrderDate: Date;
  performanceTrend: 'improving' | 'stable' | 'declining';
}

export interface SupplierDeliveryHistory {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number;
  deliveryTime: number;
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number;
}

export interface SupplierRepository {
  getAll(): Promise<Supplier[]>;
  getSupplierStats(): Promise<SupplierStats>;
  getSupplierPerformance(supplierId: string, timeRange: { days: number }): Promise<SupplierPerformanceData>;
  getAllSuppliers(): Promise<Supplier[]>;
  getSupplierById(supplierId: string): Promise<Supplier | null>;
  getSuppliersByCategory(category: string): Promise<Supplier[]>;
  getTopPerformingSuppliers(limit?: number): Promise<Supplier[]>;
  getRiskSuppliers(): Promise<Supplier[]>;
  updateSupplierPerformance(supplierId: string, metrics: Partial<SupplierPerformanceMetrics>): Promise<void>;
  searchSuppliers(query: string): Promise<Supplier[]>;
  getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<SupplierDeliveryHistory[]>;
  getStats(): Promise<RepositoryStats>;
  invalidateCache(key?: string): Promise<void>;
}

/**
 * Cache-TTL für Supplier-Daten (verschiedene Datentypen)
 */
const SUPPLIER_CACHE_TTL = {
  ALL_SUPPLIERS: 5 * 60 * 1000, // 5 Minuten
  SUPPLIER_STATS: 3 * 60 * 1000, // 3 Minuten
  PERFORMANCE: 2 * 60 * 1000, // 2 Minuten
  DELIVERY_HISTORY: 4 * 60 * 1000, // 4 Minuten
  SEARCH: 1 * 60 * 1000, // 1 Minute
};

export class SupplierRepositoryImpl implements SupplierRepository {
  private db = db;
  private cache = getBackendCache();
  private stats: RepositoryStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    avgQueryTime: 0,
    lastAccessed: new Date()
  };

  constructor(database?: any) {
    // Drizzle DB wird direkt importiert - Parameter für Kompatibilität
  }

  /**
   * Get all suppliers
   */
  async getAll(): Promise<Supplier[]> {
    const startTime = Date.now();
    const cacheKey = 'supplier:all';

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate sample suppliers
          const suppliers: Supplier[] = [];
          const supplierNames = [
            'TechCorp GmbH', 'InnoSupply AG', 'QualityParts Ltd', 'ReliableTech Inc',
            'PrecisionComponents', 'GlobalSupplier Co', 'ElectronicParts GmbH'
          ];

          for (let i = 0; i < supplierNames.length; i++) {
            const supplier: Supplier = {
              id: `SUP_${(i + 1).toString().padStart(3, '0')}`,
              name: supplierNames[i],
              location: `Location ${i + 1}`,
              category: Math.random() > 0.5 ? 'Electronics' : 'Components',
              contactInfo: {
                email: `contact@${supplierNames[i].toLowerCase().replace(/\s+/g, '')}.com`,
                phone: `+49 ${Math.floor(Math.random() * 900) + 100} ${Math.floor(Math.random() * 9000) + 1000}`,
                address: `Address ${i + 1}, Germany`,
                contactPerson: `Contact Person ${i + 1}`
              },
              performanceMetrics: {
                onTimeDeliveryRate: 0.75 + Math.random() * 0.25,
                qualityRate: 0.90 + Math.random() * 0.10,
                responseTime: Math.random() * 24 + 2,
                financialStabilityScore: Math.floor(Math.random() * 4) + 6,
                reliabilityScore: Math.floor(Math.random() * 3) + 7,
                costCompetitiveness: Math.floor(Math.random() * 4) + 6
              }
            };
            suppliers.push(supplier);
          }

          return suppliers;
        },
        SUPPLIER_CACHE_TTL.ALL_SUPPLIERS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching all suppliers:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get overall supplier statistics
   */
  async getSupplierStats(): Promise<SupplierStats> {
    const startTime = Date.now();
    const cacheKey = 'supplier:stats';

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate realistic stats
          return {
            totalSuppliers: 45,
            activeSuppliers: 38,
            averagePerformanceScore: 7.2,
            topPerformers: 12,
            riskSuppliers: 3
          };
        },
        SUPPLIER_CACHE_TTL.SUPPLIER_STATS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching supplier stats:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get supplier performance data
   */
  async getSupplierPerformance(
    supplierId: string,
    timeRange: { days: number }
  ): Promise<SupplierPerformanceData> {
    const startTime = Date.now();
    const cacheKey = `supplier:performance:${supplierId}:${timeRange.days}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate realistic performance data
          const performanceData: SupplierPerformanceData = {
            supplierId,
            onTimeDeliveryRate: 0.75 + Math.random() * 0.25, // 75-100%
            qualityRate: 0.90 + Math.random() * 0.10, // 90-100%
            responseTime: Math.random() * 24 + 2, // 2-26 hours
            financialStabilityScore: Math.floor(Math.random() * 4) + 6, // 6-10
            reliabilityScore: Math.floor(Math.random() * 3) + 7, // 7-10
            costCompetitiveness: Math.floor(Math.random() * 4) + 6, // 6-10
            totalOrders: Math.floor(Math.random() * 100) + 20,
            totalValue: Math.floor(Math.random() * 500000) + 50000,
            lastOrderDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
            performanceTrend: Math.random() > 0.6 ? 'improving' : Math.random() > 0.3 ? 'stable' : 'declining'
          };

          return performanceData;
        },
        SUPPLIER_CACHE_TTL.PERFORMANCE
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error(`Error fetching supplier performance for ${supplierId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get all suppliers
   */
  async getAllSuppliers(): Promise<Supplier[]> {
    return this.getAll();
  }

  /**
   * Get supplier by ID
   */
  async getSupplierById(supplierId: string): Promise<Supplier | null> {
    const startTime = Date.now();
    const cacheKey = `supplier:detail:${supplierId}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const suppliers = await this.getAll();
          return suppliers.find(s => s.id === supplierId) || null;
        },
        SUPPLIER_CACHE_TTL.ALL_SUPPLIERS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error(`Error fetching supplier ${supplierId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get suppliers by category
   */
  async getSuppliersByCategory(category: string): Promise<Supplier[]> {
    const startTime = Date.now();
    const cacheKey = `supplier:category:${category}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const suppliers = await this.getAll();
          return suppliers.filter(s => s.category === category);
        },
        SUPPLIER_CACHE_TTL.SEARCH
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error(`Error fetching suppliers by category ${category}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get top performing suppliers
   */
  async getTopPerformingSuppliers(limit?: number): Promise<Supplier[]> {
    const startTime = Date.now();
    const limitNum = limit || 10;
    const cacheKey = `supplier:top:${limitNum}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const suppliers = await this.getAll();

          // Sort by overall performance score (combination of metrics)
          const sortedSuppliers = suppliers.sort((a, b) => {
            const scoreA = (a.performanceMetrics.onTimeDeliveryRate +
                           a.performanceMetrics.qualityRate +
                           a.performanceMetrics.reliabilityScore / 10) / 3;
            const scoreB = (b.performanceMetrics.onTimeDeliveryRate +
                           b.performanceMetrics.qualityRate +
                           b.performanceMetrics.reliabilityScore / 10) / 3;
            return scoreB - scoreA;
          });

          return sortedSuppliers.slice(0, limitNum);
        },
        SUPPLIER_CACHE_TTL.PERFORMANCE
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching top performing suppliers:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get suppliers with risk issues
   */
  async getRiskSuppliers(): Promise<Supplier[]> {
    const startTime = Date.now();
    const cacheKey = 'supplier:risk';

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const suppliers = await this.getAll();

          // Filter suppliers with performance issues
          return suppliers.filter(s =>
            s.performanceMetrics.onTimeDeliveryRate < 0.8 ||
            s.performanceMetrics.qualityRate < 0.95 ||
            s.performanceMetrics.financialStabilityScore < 6
          );
        },
        SUPPLIER_CACHE_TTL.PERFORMANCE
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching risk suppliers:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Update supplier performance metrics
   */
  async updateSupplierPerformance(
    supplierId: string,
    metrics: Partial<SupplierPerformanceMetrics>
  ): Promise<void> {
    const startTime = Date.now();
    const cacheKey = `supplier:update:${supplierId}`;

    this.stats.totalQueries++;

    try {
      await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - in real app, this would update the database
          console.log(`Performance metrics updated for supplier ${supplierId}:`, metrics);
          return { success: true };
        },
        0 // No caching for updates
      );

      // Invalidate related caches
      this.cache.invalidateByDataTypes(['supplier']);

      this.stats.cacheHits++;
      this.updateStats(startTime);

    } catch (error) {
      console.error(`Error updating supplier performance for ${supplierId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Search suppliers by name or category
   */
  async searchSuppliers(query: string): Promise<Supplier[]> {
    const startTime = Date.now();
    const cacheKey = `supplier:search:${query.toLowerCase()}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const suppliers = await this.getAll();
          const lowerQuery = query.toLowerCase();

          return suppliers.filter(s =>
            s.name.toLowerCase().includes(lowerQuery) ||
            s.category.toLowerCase().includes(lowerQuery) ||
            s.location.toLowerCase().includes(lowerQuery)
          );
        },
        SUPPLIER_CACHE_TTL.SEARCH
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error(`Error searching suppliers with query "${query}":`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Get supplier delivery history summary
   */
  async getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<SupplierDeliveryHistory[]> {
    const startTime = Date.now();
    const cacheKey = `supplier:delivery-history:${supplierId}:${timeRange.days}`;

    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Mock implementation - generate sample delivery history
          const history: SupplierDeliveryHistory[] = [];
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - timeRange.days);

          // Generate 10-30 delivery records
          const recordCount = Math.floor(Math.random() * 20) + 10;

          for (let i = 0; i < recordCount; i++) {
            const deliveryDate = new Date(startDate.getTime() + Math.random() * timeRange.days * 24 * 60 * 60 * 1000);
            const promisedTime = Math.floor(Math.random() * 10) + 3; // 3-12 days
            const actualTime = promisedTime + (Math.random() - 0.7) * 3; // Some variation

            history.push({
              deliveryId: `DEL_${supplierId}_${i + 1}`,
              supplierId,
              orderDate: new Date(deliveryDate.getTime() - actualTime * 24 * 60 * 60 * 1000),
              deliveryDate,
              promisedTime,
              deliveryTime: Math.max(actualTime, 1),
              wasLate: actualTime > promisedTime,
              productType: Math.random() > 0.5 ? 'Electronics' : 'Components',
              urgency: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
              quantity: Math.floor(Math.random() * 1000) + 50,
              value: Math.floor(Math.random() * 10000) + 500
            });
          }

          return history.sort((a, b) => b.deliveryDate.getTime() - a.deliveryDate.getTime());
        },
        SUPPLIER_CACHE_TTL.DELIVERY_HISTORY
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error(`Error fetching delivery history for supplier ${supplierId}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Repository-Statistiken abrufen
   */
  async getStats(): Promise<RepositoryStats> {
    this.stats.hitRate = this.stats.totalQueries > 0 ?
      (this.stats.cacheHits / this.stats.totalQueries) * 100 : 0;
    this.stats.lastAccessed = new Date();

    return { ...this.stats };
  }

  /**
   * Repository-Cache invalidieren
   */
  async invalidateCache(key?: string): Promise<void> {
    if (key) {
      this.cache.invalidateByDataTypes([key]);
    } else {
      this.cache.invalidateByDataTypes(['supplier']);
    }
  }

  /**
   * Statistiken aktualisieren
   */
  private updateStats(startTime: number): void {
    const queryTime = Date.now() - startTime;
    this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
    this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
    this.stats.lastAccessed = new Date();
  }
}