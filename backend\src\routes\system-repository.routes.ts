import { Router } from "express";
import { SystemController } from "../controllers/system.controller";
import rateLimit from "express-rate-limit";

const router = Router();
const controller = new SystemController();

// Rate limiting for system endpoints
const systemLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === "development" ? 500 : 100, // Limit each IP
  message: "Too many requests to system API from this IP",
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all routes
router.use(systemLimiter);

// Service Level routes
router.get('/service-level', controller.getServiceLevelData.bind(controller));
router.get('/service-level/current', controller.getCurrentServiceLevel.bind(controller));
router.get('/service-level/stats', controller.getServiceLevelStats.bind(controller));

// Performance routes
router.get('/performance', controller.getDailyPerformance.bind(controller));
router.get('/performance/trends', controller.getPerformanceTrend.bind(controller));

// Picking routes
router.get('/picking', controller.getPickingData.bind(controller));
router.get('/picking/efficiency', controller.getPickingEfficiency.bind(controller));

// Returns routes
router.get('/returns', controller.getReturnsData.bind(controller));
router.get('/returns/analysis', controller.getReturnsAnalysis.bind(controller));

// Delivery Positions routes
router.get('/delivery-positions', controller.getDeliveryPositions.bind(controller));
router.get('/delivery-positions/analysis', controller.getDeliveryAnalysis.bind(controller));

// Tagesleistung routes
router.get('/tagesleistung', controller.getTagesleistungData.bind(controller));
router.get('/tagesleistung/stats', controller.getDailyPerformanceStats.bind(controller));

// System Stats routes
router.get('/stats', controller.getSystemStats.bind(controller));
router.get('/health', controller.getSystemHealthDashboard.bind(controller));

// Dashboard route
router.get('/dashboard', controller.getSystemDashboard.bind(controller));

export default router;