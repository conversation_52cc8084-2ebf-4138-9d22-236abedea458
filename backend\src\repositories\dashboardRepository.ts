import { db } from '../db';
import { dispatchData } from '../db/schema';
import { and, gte, lte, desc, sql, isNotNull } from 'drizzle-orm';

export interface ServiceLevelDataPoint {
  datum: string;
  servicegrad: number;
}

export interface DeliveryPositionDataPoint {
  datum: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

export interface TagesleistungDataPoint {
  datum: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

export interface PickingDataPoint {
  datum: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
}

export class DashboardRepository {
  private db = db;

  /**
   * Holt ServiceGrad-Daten aus der Datenbank
   */
  async getServiceLevelData(startDate?: string, endDate?: string): Promise<ServiceLevelDataPoint[]> {
    let whereConditions = [isNotNull(dispatchData.servicegrad)]; // Nur Datensätze mit servicegrad

    if (startDate && endDate) {
      whereConditions.push(
        gte(dispatchData.datum, startDate),
        lte(dispatchData.datum, endDate)
      );
    }

    const result = await this.db.select({
      datum: dispatchData.datum,
      servicegrad: dispatchData.servicegrad
    })
    .from(dispatchData)
    .where(and(...whereConditions))
    .orderBy(desc(dispatchData.datum));

    return result.map((row: any) => ({
      datum: row.datum,
      servicegrad: Number(row.servicegrad) || 0
    }));
  }

  /**
   * Holt Lieferpositionen-Daten aus der Datenbank
   */
  async getDeliveryPositionsData(startDate?: string, endDate?: string): Promise<DeliveryPositionDataPoint[]> {
    let whereConditions = [
      sql`${dispatchData.ausgeliefert_lup} IS NOT NULL OR ${dispatchData.rueckstaendig} IS NOT NULL`
    ];

    if (startDate && endDate) {
      whereConditions.push(
        gte(dispatchData.datum, startDate),
        lte(dispatchData.datum, endDate)
      );
    }

    const result = await this.db.select({
      datum: dispatchData.datum,
      ausgeliefert_lup: dispatchData.ausgeliefert_lup,
      rueckstaendig: dispatchData.rueckstaendig
    })
    .from(dispatchData)
    .where(and(...whereConditions))
    .orderBy(desc(dispatchData.datum));

    return result.map((row: any) => ({
      datum: row.datum,
      ausgeliefert_lup: Number(row.ausgeliefert_lup) || 0,
      rueckstaendig: Number(row.rueckstaendig) || 0
    }));
  }

  /**
   * Holt Tagesleistung-Daten aus der Datenbank
   */
  async getTagesleistungData(startDate?: string, endDate?: string): Promise<TagesleistungDataPoint[]> {
    let whereConditions = [isNotNull(dispatchData.produzierte_tonnagen)]; // Nur Datensätze mit produzierte_tonnagen

    if (startDate && endDate) {
      whereConditions.push(
        gte(dispatchData.datum, startDate),
        lte(dispatchData.datum, endDate)
      );
    }

    const result = await this.db.select({
      datum: dispatchData.datum,
      produzierte_tonnagen: dispatchData.produzierte_tonnagen,
      direktverladung_kiaa: dispatchData.direktverladung_kiaa,
      umschlag: dispatchData.umschlag,
      kg_pro_colli: dispatchData.kg_pro_colli,
      elefanten: dispatchData.elefanten
    })
    .from(dispatchData)
    .where(and(...whereConditions))
    .orderBy(desc(dispatchData.datum));

    return result.map((row: any) => ({
      datum: row.datum,
      produzierte_tonnagen: Number(row.produzierte_tonnagen) || 0,
      direktverladung_kiaa: Number(row.direktverladung_kiaa) || 0,
      umschlag: Number(row.umschlag) || 0,
      kg_pro_colli: Number(row.kg_pro_colli) || 0,
      elefanten: Number(row.elefanten) || 0
    }));
  }

  /**
   * Holt Picking-Daten aus der Datenbank
   */
  async getPickingData(startDate?: string, endDate?: string): Promise<PickingDataPoint[]> {
    let whereConditions = [
      sql`${dispatchData.atrl} IS NOT NULL OR ${dispatchData.aril} IS NOT NULL`
    ];

    if (startDate && endDate) {
      whereConditions.push(
        gte(dispatchData.datum, startDate),
        lte(dispatchData.datum, endDate)
      );
    }

    const result = await this.db.select({
      datum: dispatchData.datum,
      atrl: dispatchData.atrl,
      aril: dispatchData.aril,
      fuellgrad_aril: dispatchData.fuellgrad_aril
    })
    .from(dispatchData)
    .where(and(...whereConditions))
    .orderBy(desc(dispatchData.datum));

    return result.map((row: any) => ({
      datum: row.datum,
      atrl: Number(row.atrl) || 0,
      aril: Number(row.aril) || 0,
      fuellgrad_aril: Number(row.fuellgrad_aril) || 0
    }));
  }

  /**
   * Holt alle Dashboard-Daten in einer Abfrage für bessere Performance
   */
  async getAllDashboardData(startDate?: string, endDate?: string): Promise<{
    serviceLevel: ServiceLevelDataPoint[];
    deliveryPositions: DeliveryPositionDataPoint[];
    tagesleistung: TagesleistungDataPoint[];
    picking: PickingDataPoint[];
  }> {
    let whereConditions: any[] = [];

    if (startDate && endDate) {
      whereConditions.push(
        gte(dispatchData.datum, startDate),
        lte(dispatchData.datum, endDate)
      );
    }

    const allData = await this.db.select({
      datum: dispatchData.datum,
      servicegrad: dispatchData.servicegrad,
      ausgeliefert_lup: dispatchData.ausgeliefert_lup,
      rueckstaendig: dispatchData.rueckstaendig,
      produzierte_tonnagen: dispatchData.produzierte_tonnagen,
      direktverladung_kiaa: dispatchData.direktverladung_kiaa,
      umschlag: dispatchData.umschlag,
      kg_pro_colli: dispatchData.kg_pro_colli,
      elefanten: dispatchData.elefanten,
      atrl: dispatchData.atrl,
      aril: dispatchData.aril,
      fuellgrad_aril: dispatchData.fuellgrad_aril
    })
    .from(dispatchData)
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .orderBy(desc(dispatchData.datum));

    const serviceLevel: ServiceLevelDataPoint[] = [];
    const deliveryPositions: DeliveryPositionDataPoint[] = [];
    const tagesleistung: TagesleistungDataPoint[] = [];
    const picking: PickingDataPoint[] = [];

    allData.forEach((row: any) => {
      // Service Level Data
      if (row.servicegrad !== null && row.servicegrad !== undefined) {
        serviceLevel.push({
          datum: row.datum,
          servicegrad: Number(row.servicegrad) || 0
        });
      }

      // Delivery Positions Data
      if ((row.ausgeliefert_lup !== null && row.ausgeliefert_lup !== undefined) ||
          (row.rueckstaendig !== null && row.rueckstaendig !== undefined)) {
        deliveryPositions.push({
          datum: row.datum,
          ausgeliefert_lup: Number(row.ausgeliefert_lup) || 0,
          rueckstaendig: Number(row.rueckstaendig) || 0
        });
      }

      // Tagesleistung Data
      if (row.produzierte_tonnagen !== null && row.produzierte_tonnagen !== undefined) {
        tagesleistung.push({
          datum: row.datum,
          produzierte_tonnagen: Number(row.produzierte_tonnagen) || 0,
          direktverladung_kiaa: Number(row.direktverladung_kiaa) || 0,
          umschlag: Number(row.umschlag) || 0,
          kg_pro_colli: Number(row.kg_pro_colli) || 0,
          elefanten: Number(row.elefanten) || 0
        });
      }

      // Picking Data
      if ((row.atrl !== null && row.atrl !== undefined) ||
          (row.aril !== null && row.aril !== undefined)) {
        picking.push({
          datum: row.datum,
          atrl: Number(row.atrl) || 0,
          aril: Number(row.aril) || 0,
          fuellgrad_aril: Number(row.fuellgrad_aril) || 0
        });
      }
    });

    return {
      serviceLevel,
      deliveryPositions,
      tagesleistung,
      picking
    };
  }
}

export const dashboardRepository = new DashboardRepository();